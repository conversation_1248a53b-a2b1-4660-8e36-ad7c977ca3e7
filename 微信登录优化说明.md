# 微信授权登录优化说明

## 问题
微信授权登录重定向回来后会闪现登录页，然后才跳转到目标页面。

## 解决方案
在微信环境下，如果检测到有授权code，先显示加载状态，处理完登录后直接跳转，避免显示登录页面。

## 主要修改

### 1. pages/login.vue

**添加加载状态UI:**
```vue
<div v-if="isProcessingWxLogin" class="wx-login-loading">
  <div class="loading-content">
    <div class="loading-spinner"></div>
    <p>正在处理微信登录...</p>
  </div>
</div>
```

**beforeMount 检测:**
```javascript
beforeMount() {
  // 在微信环境下，如果有授权code且未登录，显示加载状态
  if (process.client && this.isWeixinBrowser()) {
    const urlParams = new URLSearchParams(window.location.search);
    const hasWxCode = urlParams.get('code');
    
    if (hasWxCode && !this.globalIsLogin()) {
      this.isProcessingWxLogin = true;
    }
  }
}
```

**mounted 优化处理:**
```javascript
async mounted() {
  await this.getWxConfig();
  
  // 先处理微信授权登录
  const hasHandledWxLogin = await this.wxSignLogin();
  
  // 如果处理了微信登录，直接返回，不显示登录页面
  if (hasHandledWxLogin) {
    return;
  }
  
  // 隐藏加载状态，显示登录页面
  this.isProcessingWxLogin = false;
  this.checkWxAuthorization();
  this.loadTencentCaptcha();
}
```

### 2. mixins/login/index.js

**wxSignLogin 返回处理状态:**
```javascript
async wxSignLogin(url) {
  // ... 处理逻辑
  if (this.code && !this.globalIsLogin()) {
    try {
      const res = await this.$api.user.getWxLogin(this.code, this.state);
      if (res.status === 200) {
        // 处理登录成功逻辑
        return true; // 表示已处理微信登录
      }
    } catch (error) {
      console.error('微信登录处理失败:', error);
      return false;
    }
  }
  return false; // 未处理
}
```

## 优化效果

**优化前:**
```
微信授权重定向 → 显示登录页面(闪现) → 处理登录 → 跳转
```

**优化后:**
```
微信授权重定向 → 显示加载状态 → 处理登录 → 直接跳转
```

## 关键点

1. **仅在微信环境下判断**: 使用 `isWeixinBrowser()` 确保只在微信中处理
2. **简单的状态控制**: 用 `isProcessingWxLogin` 控制显示/隐藏
3. **优先处理**: 在 `mounted` 中优先处理微信登录，成功后直接返回
4. **保持原有逻辑**: 不影响非微信环境的正常登录流程

这样就用最少的代码解决了闪现问题，保持了代码的简洁性。
