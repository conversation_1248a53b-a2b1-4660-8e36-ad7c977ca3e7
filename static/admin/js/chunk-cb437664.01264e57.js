(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-cb437664"],{"0cb2":function(t,e,i){var s=i("7b0b"),r=Math.floor,n="".replace,a=/\$([$&'`]|\d{1,2}|<[^>]*>)/g,o=/\$([$&'`]|\d{1,2})/g;t.exports=function(t,e,i,l,h,c){var d=i+t.length,u=l.length,f=o;return void 0!==h&&(h=s(h),f=a),n.call(c,f,(function(s,n){var a;switch(n.charAt(0)){case"$":return"$";case"&":return t;case"`":return e.slice(0,i);case"'":return e.slice(d);case"<":a=h[n.slice(1,-1)];break;default:var o=+n;if(0===o)return s;if(o>u){var c=r(o/10);return 0===c?s:c<=u?void 0===l[c-1]?n.charAt(1):l[c-1]+n.charAt(1):s}a=l[o-1]}return void 0===a?"":a}))}},"168e":function(t,e,i){"use strict";i("4de4"),i("d3b7");e["a"]={data:function(){return{filterData:[],btnStatus:!1}},watch:{},created:function(){},mounted:function(){},methods:{filterList:function(){console.log(this.filterData),this.btnStatus?(this.tableData=this.filterData,this.btnStatus=!1):(this.tableData=this.tableData.filter((function(t){return!t.unClassify})),this.btnStatus=!0)}}}},"232c":function(t,e,i){"use strict";i("f44b")},"2c3e":function(t,e,i){var s=i("83ab"),r=i("9f7f").UNSUPPORTED_Y,n=i("9bf2").f,a=i("69f3").get,o=RegExp.prototype;s&&r&&n(RegExp.prototype,"sticky",{configurable:!0,get:function(){if(this!==o){if(this instanceof RegExp)return!!a(this).sticky;throw TypeError("Incompatible receiver, RegExp required")}}})},"4d63":function(t,e,i){var s=i("83ab"),r=i("da84"),n=i("94ca"),a=i("7156"),o=i("9bf2").f,l=i("241c").f,h=i("44e7"),c=i("ad6d"),d=i("9f7f"),u=i("6eeb"),f=i("d039"),g=i("69f3").set,m=i("2626"),p=i("b622"),v=p("match"),y=r.RegExp,E=y.prototype,T=/a/g,S=/a/g,b=new y(T)!==T,A=d.UNSUPPORTED_Y,L=s&&n("RegExp",!b||A||f((function(){return S[v]=!1,y(T)!=T||y(S)==S||"/a/i"!=y(T,"i")})));if(L){var R=function(t,e){var i,s=this instanceof R,r=h(t),n=void 0===e;if(!s&&r&&t.constructor===R&&n)return t;b?r&&!n&&(t=t.source):t instanceof R&&(n&&(e=c.call(t)),t=t.source),A&&(i=!!e&&e.indexOf("y")>-1,i&&(e=e.replace(/y/g,"")));var o=a(b?new y(t,e):y(t,e),s?this:E,R);return A&&i&&g(o,{sticky:i}),o},D=function(t){t in R||o(R,t,{configurable:!0,get:function(){return y[t]},set:function(e){y[t]=e}})},I=l(y),k=0;while(I.length>k)D(I[k++]);E.constructor=R,R.prototype=E,u(r,"RegExp",R)}m("RegExp")},5319:function(t,e,i){"use strict";var s=i("d784"),r=i("825a"),n=i("50c4"),a=i("a691"),o=i("1d80"),l=i("8aa5"),h=i("0cb2"),c=i("14c3"),d=Math.max,u=Math.min,f=function(t){return void 0===t?t:String(t)};s("replace",2,(function(t,e,i,s){var g=s.REGEXP_REPLACE_SUBSTITUTES_UNDEFINED_CAPTURE,m=s.REPLACE_KEEPS_$0,p=g?"$":"$0";return[function(i,s){var r=o(this),n=void 0==i?void 0:i[t];return void 0!==n?n.call(i,r,s):e.call(String(r),i,s)},function(t,s){if(!g&&m||"string"===typeof s&&-1===s.indexOf(p)){var o=i(e,t,this,s);if(o.done)return o.value}var v=r(t),y=String(this),E="function"===typeof s;E||(s=String(s));var T=v.global;if(T){var S=v.unicode;v.lastIndex=0}var b=[];while(1){var A=c(v,y);if(null===A)break;if(b.push(A),!T)break;var L=String(A[0]);""===L&&(v.lastIndex=l(y,n(v.lastIndex),S))}for(var R="",D=0,I=0;I<b.length;I++){A=b[I];for(var k=String(A[0]),w=d(u(a(A.index),y.length),0),C=[],_=1;_<A.length;_++)C.push(f(A[_]));var O=A.groups;if(E){var x=[k].concat(C,w,y);void 0!==O&&x.push(O);var P=String(s.apply(void 0,x))}else P=h(k,y,w,C,O,s);w>=D&&(R+=y.slice(D,w)+P,D=w+k.length)}return R+y.slice(D)}]}))},5902:function(t,e,i){"use strict";var s=i("7a23"),r=Object(s["jb"])("data-v-31d5e994");Object(s["K"])("data-v-31d5e994");var n={class:"power-head"},a={class:"line"},o=Object(s["q"])("td",{class:"title"},"分类属性",-1),l=Object(s["p"])("设置分类"),h=Object(s["p"])("取消分类"),c={class:"UserTree"},d=Object(s["q"])("div",{class:"user"},"请选择分类:",-1),u={class:"down-tree"},f={class:"rightControl"},g=Object(s["p"])("删除"),m={class:"dialog-footer"},p=Object(s["p"])("取 消"),v=Object(s["p"])("确 定");Object(s["I"])();var y=r((function(t,e,i,y,E,T){var S=Object(s["Q"])("el-radio"),b=Object(s["Q"])("el-radio-group"),A=Object(s["Q"])("el-tree"),L=Object(s["Q"])("el-table-column"),R=Object(s["Q"])("el-button"),D=Object(s["Q"])("el-table"),I=Object(s["Q"])("el-dialog");return Object(s["H"])(),Object(s["l"])("div",null,[E.editVisible?(Object(s["H"])(),Object(s["l"])(I,{key:0,title:"批量设置分类",modelValue:E.editVisible,"onUpdate:modelValue":e[3]||(e[3]=function(t){return E.editVisible=t}),width:"70%",onClose:e[4]||(e[4]=function(t){return T.closeDialog()})},{footer:r((function(){return[Object(s["q"])("span",m,[Object(s["q"])(R,{onClick:e[2]||(e[2]=function(t){return T.closeDialog()}),size:"mini"},{default:r((function(){return[p]})),_:1}),Object(s["q"])(R,{type:"primary",onClick:T.savePower,size:"mini"},{default:r((function(){return[v]})),_:1},8,["onClick"])])]})),default:r((function(){return[Object(s["q"])("div",n,[Object(s["q"])("table",null,[Object(s["q"])("tr",a,[o,Object(s["q"])("td",null,[Object(s["q"])(b,{onChange:T.CancelRoleList,modelValue:E.radio,"onUpdate:modelValue":e[1]||(e[1]=function(t){return E.radio=t}),fill:"#797979","text-color":"#797979"},{default:r((function(){return[Object(s["q"])(S,{label:1},{default:r((function(){return[l]})),_:1}),Object(s["q"])(S,{label:0},{default:r((function(){return[h]})),_:1})]})),_:1},8,["onChange","modelValue"])])])]),Object(s["gb"])(Object(s["q"])("div",c,[d,Object(s["q"])("div",u,[Object(s["q"])(A,{ref:"tree",data:E.tableData,"show-checkbox":"","node-key":"id","default-expand-all":!0,"check-strictly":!0,onCheck:T.getChecked,onCheckChange:T.handleCheckChange,props:{children:"childTags",label:"tagName"}},null,8,["data","onCheck","onCheckChange"])])],512),[[s["cb"],E.radio]]),Object(s["gb"])(Object(s["q"])("div",f,[Object(s["q"])(D,{class:"tableControl",ref:"multipleTable",border:"",data:E.classifyData,"default-sort":{prop:"date",order:"descending"},"tooltip-effect":"dark",style:{width:"100%"}},{default:r((function(){return[Object(s["q"])(L,{prop:"moduleId",label:"内容ID",width:"150",align:"center"}),Object(s["q"])(L,{prop:"title",label:"内容标题",align:"center"}),Object(s["q"])(L,{prop:"tagName",label:"已设置分类",align:"center"}),Object(s["q"])(L,{prop:"approvalStatus",label:"操作",width:"200",align:"center"},{default:r((function(t){return[Object(s["q"])(R,{size:"mini",type:"danger",plain:"",onClick:function(e){return T.handleDelete(t.$index,t.row)}},{default:r((function(){return[g]})),_:2},1032,["onClick"])]})),_:1})]})),_:1},8,["data"])],512),[[s["cb"],!E.radio]])])]})),_:1},8,["modelValue"])):Object(s["m"])("",!0)])})),E=(i("d3b7"),i("159b"),i("99af"),i("e9c4"),i("b0c0"),i("7864")),T=i("2b4c"),S=i("d990"),b=i("bbf7"),A={name:"classifyDialog",mixins:[S["a"],b["a"]],props:{choseData:{type:Array,required:!0}},data:function(){return{editVisible:!1,checkList:[],type:"",radio:1,tableData:[],title:"",classifyData:[]}},methods:{openDialog:function(t){this.editVisible=!0,this.type=t},gitClassList:function(){var t=this,e={tagIds:[],tagType:this.type};Object(T["md"])(e).then((function(e){t.tableData=e.data}))},handleCheckChange:function(){this.checkList=this.$refs.tree.getCheckedKeys()},savePower:function(){if(1===this.radio){var t=[],e=[];if("course"===this.type?this.choseData.forEach((function(i){t.push(i.courseId),e.push({moduleId:i.id,title:i.title})})):this.choseData.forEach((function(e){t.push(e.id)})),this.checkList.length>0){var i={moduleIds:[].concat(t),moduleType:this.type,operateType:this.radio,tagIds:this.checkList,moduleList:[].concat(e)};Object(T["ie"])(i).then((function(t){200==t.status&&Object(E["c"])({message:"设置分类成功",type:"success"})}))}}this.closeDialog()},closeDialog:function(){this.type="",this.radio=1,this.editVisible=!1,this.classifyData=[],this.checkList=[],this.$emit("init")},getChecked:function(t){var e=this.$refs.tree.getNode(t.id);this.setNode(e)},setNode:function(t){t.checked?(this.setChildrenNode(t,t.checked),this.setParentNode(t)):this.setChildrenNode(t,t.checked)},setChildrenNode:function(t,e){for(var i=t.childNodes.length,s=0;s<i;s++)t.childNodes[s].checked=e,this.setChildrenNode(t.childNodes[s],e)},setParentNode:function(t){if(t.parent)for(var e in t)"parent"===e&&(t[e].checked=!0,this.setParentNode(t[e]))},CancelRoleList:function(t){var e=this;if(0===t){var i=[];"course"===this.type?this.choseData.forEach((function(t){i.push({moduleId:t.courseId,title:t.title})})):"live_info"===this.type||"eda"===this.type?this.choseData.forEach((function(t){i.push({moduleId:JSON.stringify(t.id),title:t.name||t.title})})):"survey"===this.type?this.choseData.forEach((function(t){i.push({moduleId:t.id,title:t.templateName})})):"tool_impact_factor"===this.type?this.choseData.forEach((function(t){i.push({moduleId:t.id,title:t.fullname})})):this.choseData.forEach((function(t){i.push({moduleId:t.id,title:t.title})}));var s={moduleType:this.type,modules:[].concat(i)};Object(T["Bc"])(s).then((function(t){e.classifyData=t.data}))}},handleDelete:function(t,e){var i=this;this.$confirm("是否删除分类?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){var t={moduleId:e.moduleId,moduleType:i.type,tagId:e.tagId};Object(T["ib"])(t).then((function(t){200===t.status&&i.CancelRoleList(0)})),i.$message({type:"success",message:"删除成功!"})}))}}},L=(i("d31f"),i("d959")),R=i.n(L);const D=R()(A,[["render",y],["__scopeId","data-v-31d5e994"]]);e["a"]=D},6243:function(t,e,i){},b64b:function(t,e,i){var s=i("23e7"),r=i("7b0b"),n=i("df75"),a=i("d039"),o=a((function(){n(1)}));s({target:"Object",stat:!0,forced:o},{keys:function(t){return n(r(t))}})},b85c:function(t,e,i){"use strict";i.d(e,"a",(function(){return r}));i("a4d3"),i("e01a"),i("d3b7"),i("d28b"),i("3ca3"),i("ddb0");var s=i("06c5");function r(t,e){var i="undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!i){if(Array.isArray(t)||(i=Object(s["a"])(t))||e&&t&&"number"===typeof t.length){i&&(t=i);var r=0,n=function(){};return{s:n,n:function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}},e:function(t){throw t},f:n}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,o=!0,l=!1;return{s:function(){i=i.call(t)},n:function(){var t=i.next();return o=t.done,t},e:function(t){l=!0,a=t},f:function(){try{o||null==i["return"]||i["return"]()}finally{if(l)throw a}}}}},bbf7:function(t,e,i){"use strict";e["a"]={data:function(){return{}},watch:{},created:function(){},mounted:function(){},computed:{isScroll:function(){return this.$store.state.isScroll}},methods:{initScroll:function(){var t=this;this.$nextTick((function(){var e=t.$refs.container.scrollHeight,i=t.$refs.container.clientHeight;e>i?t.$store.commit("setIsScroll",!0):t.$store.commit("setIsScroll",!1),document.getElementById("container").addEventListener("scroll",(function(){var e=t.$refs.container.scrollHeight,i=t.$refs.container.clientHeight;e>i&&t.$store.commit("setIsScroll",!0);var s=t.$refs.container.scrollTop;i+s+20==e&&t.$store.commit("setIsScroll",!1)}))}))}}}},c26f:function(t,e,i){"use strict";i("a15b"),i("b64b"),i("4d63"),i("ac1f"),i("2c3e"),i("25f0"),i("00b4"),i("5319");var s={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#x27;","`":"&#x60;"},r={"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#x27;":"'","&#x60;":"`","&ldquo;":"“","&rdquo;":"”","&lsquo;":"‘","&rsquo;":"’","&middot;":"·","&nbsp;":" ","&hellip;":"…"},n={excapeHtml:function(t){var e=function(t){return s[t]},i="(?:"+Object.keys(s).join("|")+")",r=RegExp(i),n=RegExp(i,"g"),a=null==t?"":""+t;return r.test(a)?a.replace(n,e):a},unexcapeHtml:function(t){var e=function(t){return r[t]},i="(?:"+Object.keys(r).join("|")+")",s=RegExp(i),n=RegExp(i,"g"),a=null==t?"":""+t;return s.test(a)?a.replace(n,e):a}};e["a"]=n},d31f:function(t,e,i){"use strict";i("6243")},d990:function(t,e,i){"use strict";var s=i("b85c");i("d3b7"),i("159b"),i("b0c0");e["a"]={data:function(){return{btnList:{}}},watch:{},created:function(){},mounted:function(){this.btnShow()},methods:{findInnerRoutes:function(t,e){var i=this;t.forEach((function(t){if(t.childMenu&&t.childMenu.length>0){var r,n=Object(s["a"])(t.childMenu);try{for(n.s();!(r=n.n()).done;){var a=r.value;a.route==i.$route.name&&a.childMenu&&a.childMenu.length>0&&a.childMenu.forEach((function(t){2==t.menuType&&(e.push(t.route),t.childMenu&&t.childMenu.length>0&&t.childMenu.forEach((function(t){e.push(t.route),t.childMenu&&t.childMenu.length>0&&t.childMenu.forEach((function(t){e.push(t.route)}))})))})),i.findInnerRoutes(a.childMenu,e)}}catch(o){n.e(o)}finally{n.f()}}}))},btnShow:function(){var t=this,e=[];this.findInnerRoutes(this.$store.state.menuInfo,e),e.forEach((function(e){t.btnList[e]=!0}))}}}},ec70:function(t,e,i){"use strict";i.r(e);var s=i("7a23"),r=Object(s["jb"])("data-v-1eb25e52");Object(s["K"])("data-v-1eb25e52");var n={class:"yxd_live"},a={class:"header"},o={class:"left"},l={class:"col"},h={class:"col"},c={key:0,class:"col"},d={class:"col"},u={class:"right"},f=Object(s["p"])("查询"),g=Object(s["p"])("重置"),m={class:"operate"},p={class:"button-group"},v=Object(s["p"])("批量审核"),y=Object(s["p"])("批量设置分类"),E=Object(s["p"])("未设置分类"),T=Object(s["p"])("批量导出"),S=Object(s["p"])("设置阅读量"),b=Object(s["q"])("div",{class:"operate_title"},[Object(s["q"])("div",null,"说明："),Object(s["p"])(' 审核通过后，点"上线"按钮，即可在专区上线；'),Object(s["q"])("br"),Object(s["p"])(' 去审后，需点"下线"按钮，方可从专区下线。 ')],-1),A={class:"table"},L={style:{color:"red"}},R=Object(s["p"])("查看"),D=Object(s["p"])("编辑"),I={class:"pagination"},k={class:"dialog-footer"},w=Object(s["p"])("取 消"),C=Object(s["p"])("确 定"),_={class:"detail_content"},O={key:0,class:"detail_sensitive"},x=Object(s["q"])("span",{class:"detail_sp"},"内含敏感词:",-1),P={style:{color:"red"}},M={class:"detail_create"},F=Object(s["q"])("span",{class:"detail_sp"},"创建人:",-1),N=Object(s["q"])("span",{class:"detail_sp"},"创建时间:",-1),U={class:"detail_title"},B=Object(s["q"])("span",{class:"detail_sp"},"标题:",-1),$={class:"detail_text"},G=Object(s["q"])("span",{class:"detail_sp"},"正文: ",-1),j=Object(s["p"])(),K={class:"detail_imags"},V={key:0,ref:"videoPlayer",controls:""},H={key:1},q={key:1,class:"detail_cover"},Y=Object(s["q"])("span",{class:"detail_sp"},"封面:",-1),W={class:"cover-container"},z={key:2,class:"detail_attachment"},X=Object(s["q"])("span",{class:"detail_sp"},"附件:",-1),Q={class:"attachment-container"},J={key:3,class:"detail_radio"},Z=Object(s["q"])("span",{class:"detail_sp"},"审核操作： ",-1),tt=Object(s["p"])("审核通过"),et=Object(s["p"])("审核不通过"),it={key:4,class:"detail_textarea"},st=Object(s["q"])("span",{class:"detail_sh"},"说明： ",-1),rt={class:"dialog-footer"},nt=Object(s["p"])("取 消"),at=Object(s["p"])("确 定"),ot=Object(s["q"])("span",{class:"detail_sh"},"审核操作： ",-1),lt=Object(s["p"])("审核通过"),ht=Object(s["p"])("审核不通过"),ct={key:0,class:"detail_textarea"},dt=Object(s["q"])("span",{class:"detail_sh"},"说明： ",-1),ut={class:"dialog-footer"},ft=Object(s["p"])("取 消"),gt=Object(s["p"])("确 定"),mt={class:"random-setting-form"},pt={class:"form-item"},vt=Object(s["q"])("span",{class:"label"},"最小随机数：",-1),yt={class:"form-item"},Et=Object(s["q"])("span",{class:"label"},"最大随机数：",-1),Tt={class:"dialog-footer"},St=Object(s["p"])("取 消"),bt=Object(s["p"])("提 交");Object(s["I"])();var At=r((function(t,e,i,At,Lt,Rt){var Dt=Object(s["Q"])("el-input"),It=Object(s["Q"])("el-cascader"),kt=Object(s["Q"])("el-option"),wt=Object(s["Q"])("el-select"),Ct=Object(s["Q"])("el-button"),_t=Object(s["Q"])("el-table-column"),Ot=Object(s["Q"])("el-table"),xt=Object(s["Q"])("el-pagination"),Pt=Object(s["Q"])("el-tag"),Mt=Object(s["Q"])("el-dialog"),Ft=Object(s["Q"])("el-image"),Nt=Object(s["Q"])("el-radio"),Ut=Object(s["Q"])("el-radio-group"),Bt=Object(s["Q"])("classify-dialog");return Object(s["H"])(),Object(s["l"])("div",n,[Object(s["q"])("div",{id:"container",class:["container",{isScroll:t.isScroll}],ref:"container"},[Object(s["q"])("div",a,[Object(s["q"])("div",o,[Object(s["q"])("div",l,[Object(s["q"])(Dt,{modelValue:Lt.title,"onUpdate:modelValue":e[1]||(e[1]=function(t){return Lt.title=t}),placeholder:"请输入标题"},null,8,["modelValue"])]),Object(s["q"])("div",h,[Object(s["q"])(Dt,{modelValue:Lt.createdName,"onUpdate:modelValue":e[2]||(e[2]=function(t){return Lt.createdName=t}),placeholder:"请输入创建人"},null,8,["modelValue"])]),(Object(s["H"])(),Object(s["l"])("div",c,[Object(s["q"])(It,{placeholder:"请选择分类",options:Lt.ClassifyData,ref:"Classify","collapse-tags":"",onChange:Rt.ClassifyChange,props:{checkStrictly:!0,value:"id",label:"tagName",children:"childTags",multiple:!0},clearable:""},null,8,["options","onChange"])])),Object(s["q"])("div",d,[Object(s["q"])(wt,{modelValue:Lt.state,"onUpdate:modelValue":e[3]||(e[3]=function(t){return Lt.state=t}),placeholder:"请选择审核状态",clearable:""},{default:r((function(){return[Object(s["q"])(kt,{label:"待审核",value:0}),Object(s["q"])(kt,{label:"审核通过",value:1}),Object(s["q"])(kt,{label:"审核未通过",value:2})]})),_:1},8,["modelValue"])])]),Object(s["q"])("div",u,[Object(s["q"])(Ct,{onClick:Rt.init,icon:"el-icon-search"},{default:r((function(){return[f]})),_:1},8,["onClick"]),Object(s["q"])(Ct,{onClick:Rt.reset},{default:r((function(){return[g]})),_:1},8,["onClick"])])]),Object(s["q"])("div",m,[Object(s["q"])("span",p,[t.btnList.batch_ugc_audit?(Object(s["H"])(),Object(s["l"])(Ct,{key:0,onClick:Rt.batch_audit},{default:r((function(){return[v]})),_:1},8,["onClick"])):Object(s["m"])("",!0),t.btnList.batch_ugc_audit?(Object(s["H"])(),Object(s["l"])(Ct,{key:1,onClick:Rt.setClass},{default:r((function(){return[y]})),_:1},8,["onClick"])):Object(s["m"])("",!0),Object(s["q"])(Ct,{onClick:e[4]||(e[4]=function(e){return t.filterList()}),type:Lt.btnStatus?"primary":""},{default:r((function(){return[E]})),_:1},8,["type"]),Object(s["q"])(Ct,{onClick:Rt.exportUgc,loading:Lt.exportLoading},{default:r((function(){return[T]})),_:1},8,["onClick","loading"]),Object(s["q"])(Ct,{onClick:Rt.openRandomSettingDialog},{default:r((function(){return[S]})),_:1},8,["onClick"])]),b]),Object(s["q"])("div",A,[Object(s["q"])(Ot,{ref:"multipleTable",border:"",data:Lt.tableData,"tooltip-effect":"dark",style:{width:"100%"},onSelectionChange:Rt.handleSelectionChange},{default:r((function(){return[Object(s["q"])(_t,{type:"selection",width:"55",align:"center"}),Object(s["q"])(_t,{sortable:"",prop:"id",label:"ID",width:"120",align:"center"}),Object(s["q"])(_t,{prop:"title",label:"标题",align:"center","min-width":"120"},{default:r((function(t){return[t.row.url&&1===t.row.auditState&&1===t.row.onlineState?(Object(s["H"])(),Object(s["l"])("span",{key:0,onClick:function(e){return Rt.openUrl(t.row.url)},class:"title-link",title:t.row.title},Object(s["U"])(t.row.title),9,["onClick","title"])):(Object(s["H"])(),Object(s["l"])("span",{key:1,title:t.row.title},Object(s["U"])(t.row.title),9,["title"]))]})),_:1}),Object(s["q"])(_t,{prop:"playUrl",label:"PV/UV","min-width":"70",align:"center"},{default:r((function(t){return[Object(s["p"])(Object(s["U"])(t.row.hits)+"/"+Object(s["U"])(t.row.userHits),1)]})),_:1}),Object(s["q"])(_t,{prop:"mobile",label:"手机号",align:"center",width:"120"}),Object(s["q"])(_t,{prop:"accountType",label:"身份类型",align:"center",width:"100"},{default:r((function(t){return[Object(s["p"])(Object(s["U"])(0===t.row.accountType?"医生":1===t.row.accountType?"游客":"未知"),1)]})),_:1}),Object(s["q"])(_t,{prop:"tags",label:"分类",align:"center","min-width":"120"},{default:r((function(t){return[Object(s["q"])("span",null,Object(s["U"])(t.row.tags||"-"),1)]})),_:1}),Object(s["q"])(_t,{prop:"sensitiveWords",label:"内含敏感词",width:"120",align:"center"},{default:r((function(t){return[Object(s["q"])("span",L,Object(s["U"])(t.row.sensitiveWords||"-"),1)]})),_:1}),Object(s["q"])(_t,{prop:"createdName",label:"创建人",sortable:"",align:"center"}),Object(s["q"])(_t,{prop:"createdTime",label:"创建时间",sortable:"","min-width":"150",align:"center"}),Object(s["q"])(_t,{prop:"onlineState",label:"状态",width:"120",align:"center"},{default:r((function(t){return[Object(s["p"])(Object(s["U"])(0==t.row.auditState?"待审核":1==t.row.auditState?"审核通过":"审核不通过"),1)]})),_:1}),Object(s["q"])(_t,{prop:"randomVal",label:"阅读量基础值",width:"100",align:"center"}),Object(s["q"])(_t,{label:"操作",align:"center","min-width":"280",fixed:"right"},{default:r((function(e){return[t.btnList.ugc_detail?(Object(s["H"])(),Object(s["l"])(Ct,{key:0,onClick:function(t){return Rt.searchDetail(e.row)}},{default:r((function(){return[R]})),_:2},1032,["onClick"])):Object(s["m"])("",!0),t.btnList.ugc_audit?(Object(s["H"])(),Object(s["l"])(Ct,{key:1,onClick:function(t){return Rt.goToEdit(e.row)}},{default:r((function(){return[D]})),_:2},1032,["onClick"])):Object(s["m"])("",!0),t.btnList.ugc_audit?(Object(s["H"])(),Object(s["l"])(Ct,{key:2,onClick:function(t){return Rt.audit(e.row)}},{default:r((function(){return[Object(s["p"])(Object(s["U"])(1==e.row.auditState?"去审":"审核"),1)]})),_:2},1032,["onClick"])):Object(s["m"])("",!0),t.btnList.end_btn?(Object(s["H"])(),Object(s["l"])(Ct,{key:3,size:"mini",onClick:function(t){return Rt.handleUp(e.$index,e.row)}},{default:r((function(){return[Object(s["p"])(Object(s["U"])(1==e.row.onlineState?"下线":"上线"),1)]})),_:2},1032,["onClick"])):Object(s["m"])("",!0)]})),_:1})]})),_:1},8,["data","onSelectionChange"])]),Object(s["q"])("div",I,[Object(s["q"])(xt,{onSizeChange:Rt.handleSizeChange,onCurrentChange:Rt.handleCurrentChange,"current-page":Lt.currentPage,"page-sizes":[10,20,30,50,100],"page-size":Lt.pageSize,layout:"total, sizes, prev, pager, next, jumper",total:Lt.total},null,8,["onSizeChange","onCurrentChange","current-page","page-size","total"])]),Lt.dialogVisible?(Object(s["H"])(),Object(s["l"])(Mt,{key:0,title:Lt.lineText,modelValue:Lt.dialogVisible,"onUpdate:modelValue":e[6]||(e[6]=function(t){return Lt.dialogVisible=t}),width:"70%"},{footer:r((function(){return[Object(s["q"])("span",k,[Object(s["q"])(Ct,{onClick:e[5]||(e[5]=function(t){return Lt.dialogVisible=!1}),size:"mini"},{default:r((function(){return[w]})),_:1}),Object(s["q"])(Ct,{type:"primary",onClick:Rt.lineSave,size:"mini"},{default:r((function(){return[C]})),_:1},8,["onClick"])])]})),default:r((function(){return[Object(s["q"])("span",null,[Object(s["p"])("是否"+Object(s["U"])("上线提示"==Lt.lineText?"上线":"下线")+" ",1),Object(s["q"])(Pt,null,{default:r((function(){return[Object(s["p"])(Object(s["U"])(t.currentChose.title),1)]})),_:1})])]})),_:1},8,["title","modelValue"])):Object(s["m"])("",!0),Object(s["q"])(Mt,{title:"查看详情",modelValue:Lt.dialogDetail,"onUpdate:modelValue":e[10]||(e[10]=function(t){return Lt.dialogDetail=t}),width:"80%"},{footer:r((function(){return[Object(s["q"])("span",rt,[Object(s["q"])(Ct,{onClick:e[9]||(e[9]=function(t){return Lt.dialogDetail=!1}),size:"mini"},{default:r((function(){return[nt]})),_:1}),Object(s["q"])(Ct,{type:"primary",onClick:Rt.examine,size:"mini"},{default:r((function(){return[at]})),_:1},8,["onClick"])])]})),default:r((function(){return[Object(s["q"])("div",_,[Lt.detailData.sensitiveWords?(Object(s["H"])(),Object(s["l"])("div",O,[x,Object(s["q"])("span",P,Object(s["U"])(Lt.detailData.sensitiveWords),1)])):Object(s["m"])("",!0),Object(s["q"])("div",M,[Object(s["q"])("div",null,[F,Object(s["p"])(" "+Object(s["U"])(Lt.detailData.createdName),1)]),Object(s["q"])("div",null,[N,Object(s["p"])(" "+Object(s["U"])(Lt.detailData.createdTime),1)])]),Object(s["q"])("div",U,[B,Object(s["p"])(" "+Object(s["U"])(Lt.detailData.title),1)]),Object(s["q"])("div",$,[G,j,Object(s["q"])("div",{innerHTML:Rt.processedContent,class:"content-wrapper"},null,8,["innerHTML"])]),Object(s["q"])("div",K,[(Object(s["H"])(!0),Object(s["l"])(s["b"],null,Object(s["O"])(Lt.detailData.imageUrls,(function(t,e){return Object(s["H"])(),Object(s["l"])(Ft,{src:t,key:e},null,8,["src"])})),128)),Lt.detailData.videoUrl?(Object(s["H"])(),Object(s["l"])("video",V,null,512)):Object(s["m"])("",!0),(Object(s["H"])(),Object(s["l"])("div",H))]),Lt.detailData.ugcCover?(Object(s["H"])(),Object(s["l"])("div",q,[Y,Object(s["q"])("div",W,[Object(s["q"])(Ft,{src:Lt.detailData.ugcCover,class:"cover-image"},null,8,["src"])])])):Object(s["m"])("",!0),Lt.detailData.attachment?(Object(s["H"])(),Object(s["l"])("div",z,[X,Object(s["q"])("div",Q,[Object(s["q"])("a",{href:Lt.detailData.attachment,target:"_blank",class:"attachment-link"},Object(s["U"])(Lt.detailData.attachmentName||"点击下载附件"),9,["href"])])])):Object(s["m"])("",!0),Lt.shenhe?(Object(s["H"])(),Object(s["l"])("div",J,[Z,Object(s["q"])(Ut,{modelValue:Lt.radio,"onUpdate:modelValue":e[7]||(e[7]=function(t){return Lt.radio=t})},{default:r((function(){return[Object(s["q"])(Nt,{label:1},{default:r((function(){return[tt]})),_:1}),Object(s["q"])(Nt,{label:2},{default:r((function(){return[et]})),_:1})]})),_:1},8,["modelValue"])])):Object(s["m"])("",!0),2==Lt.radio?(Object(s["H"])(),Object(s["l"])("div",it,[st,Object(s["q"])(Dt,{type:"textarea",rows:3,placeholder:"请填写不通过原因",modelValue:Lt.text,"onUpdate:modelValue":e[8]||(e[8]=function(t){return Lt.text=t})},null,8,["modelValue"])])):Object(s["m"])("",!0)])]})),_:1},8,["modelValue"]),Object(s["q"])(Mt,{title:"批量审核",modelValue:Lt.dialogAuditAll,"onUpdate:modelValue":e[14]||(e[14]=function(t){return Lt.dialogAuditAll=t}),width:"800"},{footer:r((function(){return[Object(s["q"])("span",ut,[Object(s["q"])(Ct,{onClick:e[13]||(e[13]=function(t){return Lt.dialogAuditAll=!1}),size:"mini"},{default:r((function(){return[ft]})),_:1}),Object(s["q"])(Ct,{type:"primary",onClick:Rt.examineAll,size:"mini"},{default:r((function(){return[gt]})),_:1},8,["onClick"])])]})),default:r((function(){return[Object(s["q"])(Ot,{data:Lt.choseData,onRowClick:t.singleElection,class:"tables_deep","highlight-current-row":""},{default:r((function(){return[Object(s["q"])(_t,{align:"center",prop:"title",label:"标题"}),Object(s["q"])(_t,{align:"center",prop:"createdName",label:"创建人"}),Object(s["q"])(_t,{align:"center",prop:"createdTime",label:"创建时间"})]})),_:1},8,["data","onRowClick"]),ot,Object(s["q"])(Ut,{modelValue:Lt.radio1,"onUpdate:modelValue":e[11]||(e[11]=function(t){return Lt.radio1=t})},{default:r((function(){return[Object(s["q"])(Nt,{label:1},{default:r((function(){return[lt]})),_:1}),Object(s["q"])(Nt,{label:2},{default:r((function(){return[ht]})),_:1})]})),_:1},8,["modelValue"]),2==Lt.radio1?(Object(s["H"])(),Object(s["l"])("div",ct,[dt,Object(s["q"])(Dt,{type:"textarea",rows:3,placeholder:"请填写不通过原因",modelValue:Lt.textAll,"onUpdate:modelValue":e[12]||(e[12]=function(t){return Lt.textAll=t})},null,8,["modelValue"])])):Object(s["m"])("",!0)]})),_:1},8,["modelValue"]),Object(s["q"])(Mt,{title:"设置阅读量随机值",modelValue:Lt.randomSettingDialog,"onUpdate:modelValue":e[18]||(e[18]=function(t){return Lt.randomSettingDialog=t}),width:"500"},{footer:r((function(){return[Object(s["q"])("span",Tt,[Object(s["q"])(Ct,{onClick:e[17]||(e[17]=function(t){return Lt.randomSettingDialog=!1}),size:"mini"},{default:r((function(){return[St]})),_:1}),Object(s["q"])(Ct,{type:"primary",onClick:Rt.submitRandomSetting,size:"mini"},{default:r((function(){return[bt]})),_:1},8,["onClick"])])]})),default:r((function(){return[Object(s["q"])("div",mt,[Object(s["q"])("div",pt,[vt,Object(s["q"])(Dt,{modelValue:Lt.minRandomValue,"onUpdate:modelValue":e[15]||(e[15]=function(t){return Lt.minRandomValue=t}),modelModifiers:{number:!0},type:"number",placeholder:"请输入最小随机数"},null,8,["modelValue"])]),Object(s["q"])("div",yt,[Et,Object(s["q"])(Dt,{modelValue:Lt.maxRandomValue,"onUpdate:modelValue":e[16]||(e[16]=function(t){return Lt.maxRandomValue=t}),modelModifiers:{number:!0},type:"number",placeholder:"请输入最大随机数"},null,8,["modelValue"])])])]})),_:1},8,["modelValue"]),Object(s["q"])(Bt,{ref:"classifyRef",choseData:Lt.choseData,onInit:Rt.set},null,8,["choseData","onInit"])],2)])})),Lt=(i("d3b7"),i("3ca3"),i("ddb0"),i("2b3d"),i("9861"),i("4de4"),i("d81d"),i("159b"),i("d990")),Rt=i("bbf7"),Dt=i("168e"),It=i("2b4c"),kt=i("7864");function wt(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t["default"]:t}var Ct={exports:{}};(function(t,e){(function(e){var i=/^(?=((?:[a-zA-Z0-9+\-.]+:)?))\1(?=((?:\/\/[^\/?#]*)?))\2(?=((?:(?:[^?#\/]*\/)*[^;?#\/]*)?))\3((?:;[^?#]*)?)(\?[^#]*)?(#[^]*)?$/,s=/^(?=([^\/?#]*))\1([^]*)$/,r=/(?:\/|^)\.(?=\/)/g,n=/(?:\/|^)\.\.\/(?!\.\.\/)[^\/]*(?=\/)/g,a={buildAbsoluteURL:function(t,e,i){if(i=i||{},t=t.trim(),e=e.trim(),!e){if(!i.alwaysNormalize)return t;var r=a.parseURL(t);if(!r)throw new Error("Error trying to parse base URL.");return r.path=a.normalizePath(r.path),a.buildURLFromParts(r)}var n=a.parseURL(e);if(!n)throw new Error("Error trying to parse relative URL.");if(n.scheme)return i.alwaysNormalize?(n.path=a.normalizePath(n.path),a.buildURLFromParts(n)):e;var o=a.parseURL(t);if(!o)throw new Error("Error trying to parse base URL.");if(!o.netLoc&&o.path&&"/"!==o.path[0]){var l=s.exec(o.path);o.netLoc=l[1],o.path=l[2]}o.netLoc&&!o.path&&(o.path="/");var h={scheme:o.scheme,netLoc:n.netLoc,path:null,params:n.params,query:n.query,fragment:n.fragment};if(!n.netLoc&&(h.netLoc=o.netLoc,"/"!==n.path[0]))if(n.path){var c=o.path,d=c.substring(0,c.lastIndexOf("/")+1)+n.path;h.path=a.normalizePath(d)}else h.path=o.path,n.params||(h.params=o.params,n.query||(h.query=o.query));return null===h.path&&(h.path=i.alwaysNormalize?a.normalizePath(n.path):n.path),a.buildURLFromParts(h)},parseURL:function(t){var e=i.exec(t);return e?{scheme:e[1]||"",netLoc:e[2]||"",path:e[3]||"",params:e[4]||"",query:e[5]||"",fragment:e[6]||""}:null},normalizePath:function(t){t=t.split("").reverse().join("").replace(r,"");while(t.length!==(t=t.replace(n,"")).length);return t.split("").reverse().join("")},buildURLFromParts:function(t){return t.scheme+t.netLoc+t.path+t.params+t.query+t.fragment}};t.exports=a})()})(Ct);var _t=Ct.exports;function Ot(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(t);e&&(s=s.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),i.push.apply(i,s)}return i}function xt(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?Ot(Object(i),!0).forEach((function(e){Ft(t,e,i[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):Ot(Object(i)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))}))}return t}function Pt(t,e){if("object"!=typeof t||!t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var s=i.call(t,e||"default");if("object"!=typeof s)return s;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}function Mt(t){var e=Pt(t,"string");return"symbol"==typeof e?e:String(e)}function Ft(t,e,i){return e=Mt(e),e in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}function Nt(){return Nt=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var i=arguments[e];for(var s in i)Object.prototype.hasOwnProperty.call(i,s)&&(t[s]=i[s])}return t},Nt.apply(this,arguments)}const Ut=Number.isFinite||function(t){return"number"===typeof t&&isFinite(t)},Bt=Number.isSafeInteger||function(t){return"number"===typeof t&&Math.abs(t)<=$t},$t=Number.MAX_SAFE_INTEGER||9007199254740991;let Gt=function(t){return t["MEDIA_ATTACHING"]="hlsMediaAttaching",t["MEDIA_ATTACHED"]="hlsMediaAttached",t["MEDIA_DETACHING"]="hlsMediaDetaching",t["MEDIA_DETACHED"]="hlsMediaDetached",t["BUFFER_RESET"]="hlsBufferReset",t["BUFFER_CODECS"]="hlsBufferCodecs",t["BUFFER_CREATED"]="hlsBufferCreated",t["BUFFER_APPENDING"]="hlsBufferAppending",t["BUFFER_APPENDED"]="hlsBufferAppended",t["BUFFER_EOS"]="hlsBufferEos",t["BUFFER_FLUSHING"]="hlsBufferFlushing",t["BUFFER_FLUSHED"]="hlsBufferFlushed",t["MANIFEST_LOADING"]="hlsManifestLoading",t["MANIFEST_LOADED"]="hlsManifestLoaded",t["MANIFEST_PARSED"]="hlsManifestParsed",t["LEVEL_SWITCHING"]="hlsLevelSwitching",t["LEVEL_SWITCHED"]="hlsLevelSwitched",t["LEVEL_LOADING"]="hlsLevelLoading",t["LEVEL_LOADED"]="hlsLevelLoaded",t["LEVEL_UPDATED"]="hlsLevelUpdated",t["LEVEL_PTS_UPDATED"]="hlsLevelPtsUpdated",t["LEVELS_UPDATED"]="hlsLevelsUpdated",t["AUDIO_TRACKS_UPDATED"]="hlsAudioTracksUpdated",t["AUDIO_TRACK_SWITCHING"]="hlsAudioTrackSwitching",t["AUDIO_TRACK_SWITCHED"]="hlsAudioTrackSwitched",t["AUDIO_TRACK_LOADING"]="hlsAudioTrackLoading",t["AUDIO_TRACK_LOADED"]="hlsAudioTrackLoaded",t["SUBTITLE_TRACKS_UPDATED"]="hlsSubtitleTracksUpdated",t["SUBTITLE_TRACKS_CLEARED"]="hlsSubtitleTracksCleared",t["SUBTITLE_TRACK_SWITCH"]="hlsSubtitleTrackSwitch",t["SUBTITLE_TRACK_LOADING"]="hlsSubtitleTrackLoading",t["SUBTITLE_TRACK_LOADED"]="hlsSubtitleTrackLoaded",t["SUBTITLE_FRAG_PROCESSED"]="hlsSubtitleFragProcessed",t["CUES_PARSED"]="hlsCuesParsed",t["NON_NATIVE_TEXT_TRACKS_FOUND"]="hlsNonNativeTextTracksFound",t["INIT_PTS_FOUND"]="hlsInitPtsFound",t["FRAG_LOADING"]="hlsFragLoading",t["FRAG_LOAD_EMERGENCY_ABORTED"]="hlsFragLoadEmergencyAborted",t["FRAG_LOADED"]="hlsFragLoaded",t["FRAG_DECRYPTED"]="hlsFragDecrypted",t["FRAG_PARSING_INIT_SEGMENT"]="hlsFragParsingInitSegment",t["FRAG_PARSING_USERDATA"]="hlsFragParsingUserdata",t["FRAG_PARSING_METADATA"]="hlsFragParsingMetadata",t["FRAG_PARSED"]="hlsFragParsed",t["FRAG_BUFFERED"]="hlsFragBuffered",t["FRAG_CHANGED"]="hlsFragChanged",t["FPS_DROP"]="hlsFpsDrop",t["FPS_DROP_LEVEL_CAPPING"]="hlsFpsDropLevelCapping",t["MAX_AUTO_LEVEL_UPDATED"]="hlsMaxAutoLevelUpdated",t["ERROR"]="hlsError",t["DESTROYING"]="hlsDestroying",t["KEY_LOADING"]="hlsKeyLoading",t["KEY_LOADED"]="hlsKeyLoaded",t["LIVE_BACK_BUFFER_REACHED"]="hlsLiveBackBufferReached",t["BACK_BUFFER_REACHED"]="hlsBackBufferReached",t["STEERING_MANIFEST_LOADED"]="hlsSteeringManifestLoaded",t}({}),jt=function(t){return t["NETWORK_ERROR"]="networkError",t["MEDIA_ERROR"]="mediaError",t["KEY_SYSTEM_ERROR"]="keySystemError",t["MUX_ERROR"]="muxError",t["OTHER_ERROR"]="otherError",t}({}),Kt=function(t){return t["KEY_SYSTEM_NO_KEYS"]="keySystemNoKeys",t["KEY_SYSTEM_NO_ACCESS"]="keySystemNoAccess",t["KEY_SYSTEM_NO_SESSION"]="keySystemNoSession",t["KEY_SYSTEM_NO_CONFIGURED_LICENSE"]="keySystemNoConfiguredLicense",t["KEY_SYSTEM_LICENSE_REQUEST_FAILED"]="keySystemLicenseRequestFailed",t["KEY_SYSTEM_SERVER_CERTIFICATE_REQUEST_FAILED"]="keySystemServerCertificateRequestFailed",t["KEY_SYSTEM_SERVER_CERTIFICATE_UPDATE_FAILED"]="keySystemServerCertificateUpdateFailed",t["KEY_SYSTEM_SESSION_UPDATE_FAILED"]="keySystemSessionUpdateFailed",t["KEY_SYSTEM_STATUS_OUTPUT_RESTRICTED"]="keySystemStatusOutputRestricted",t["KEY_SYSTEM_STATUS_INTERNAL_ERROR"]="keySystemStatusInternalError",t["MANIFEST_LOAD_ERROR"]="manifestLoadError",t["MANIFEST_LOAD_TIMEOUT"]="manifestLoadTimeOut",t["MANIFEST_PARSING_ERROR"]="manifestParsingError",t["MANIFEST_INCOMPATIBLE_CODECS_ERROR"]="manifestIncompatibleCodecsError",t["LEVEL_EMPTY_ERROR"]="levelEmptyError",t["LEVEL_LOAD_ERROR"]="levelLoadError",t["LEVEL_LOAD_TIMEOUT"]="levelLoadTimeOut",t["LEVEL_PARSING_ERROR"]="levelParsingError",t["LEVEL_SWITCH_ERROR"]="levelSwitchError",t["AUDIO_TRACK_LOAD_ERROR"]="audioTrackLoadError",t["AUDIO_TRACK_LOAD_TIMEOUT"]="audioTrackLoadTimeOut",t["SUBTITLE_LOAD_ERROR"]="subtitleTrackLoadError",t["SUBTITLE_TRACK_LOAD_TIMEOUT"]="subtitleTrackLoadTimeOut",t["FRAG_LOAD_ERROR"]="fragLoadError",t["FRAG_LOAD_TIMEOUT"]="fragLoadTimeOut",t["FRAG_DECRYPT_ERROR"]="fragDecryptError",t["FRAG_PARSING_ERROR"]="fragParsingError",t["FRAG_GAP"]="fragGap",t["REMUX_ALLOC_ERROR"]="remuxAllocError",t["KEY_LOAD_ERROR"]="keyLoadError",t["KEY_LOAD_TIMEOUT"]="keyLoadTimeOut",t["BUFFER_ADD_CODEC_ERROR"]="bufferAddCodecError",t["BUFFER_INCOMPATIBLE_CODECS_ERROR"]="bufferIncompatibleCodecsError",t["BUFFER_APPEND_ERROR"]="bufferAppendError",t["BUFFER_APPENDING_ERROR"]="bufferAppendingError",t["BUFFER_STALLED_ERROR"]="bufferStalledError",t["BUFFER_FULL_ERROR"]="bufferFullError",t["BUFFER_SEEK_OVER_HOLE"]="bufferSeekOverHole",t["BUFFER_NUDGE_ON_STALL"]="bufferNudgeOnStall",t["INTERNAL_EXCEPTION"]="internalException",t["INTERNAL_ABORTED"]="aborted",t["UNKNOWN"]="unknown",t}({});const Vt=function(){},Ht={trace:Vt,debug:Vt,log:Vt,warn:Vt,info:Vt,error:Vt};let qt=Ht;function Yt(t){const e=self.console[t];return e?e.bind(self.console,`[${t}] >`):Vt}function Wt(t,...e){e.forEach((function(e){qt[e]=t[e]?t[e].bind(t):Yt(e)}))}function zt(t,e){if("object"===typeof console&&!0===t||"object"===typeof t){Wt(t,"debug","log","info","warn","error");try{qt.log(`Debug logs enabled for "${e}" in hls.js version 1.5.11`)}catch(i){qt=Ht}}else qt=Ht}const Xt=qt,Qt=/^(\d+)x(\d+)$/,Jt=/(.+?)=(".*?"|.*?)(?:,|$)/g;class Zt{constructor(t){"string"===typeof t&&(t=Zt.parseAttrList(t)),Nt(this,t)}get clientAttrs(){return Object.keys(this).filter(t=>"X-"===t.substring(0,2))}decimalInteger(t){const e=parseInt(this[t],10);return e>Number.MAX_SAFE_INTEGER?1/0:e}hexadecimalInteger(t){if(this[t]){let e=(this[t]||"0x").slice(2);e=(1&e.length?"0":"")+e;const i=new Uint8Array(e.length/2);for(let t=0;t<e.length/2;t++)i[t]=parseInt(e.slice(2*t,2*t+2),16);return i}return null}hexadecimalIntegerAsNumber(t){const e=parseInt(this[t],16);return e>Number.MAX_SAFE_INTEGER?1/0:e}decimalFloatingPoint(t){return parseFloat(this[t])}optionalFloat(t,e){const i=this[t];return i?parseFloat(i):e}enumeratedString(t){return this[t]}bool(t){return"YES"===this[t]}decimalResolution(t){const e=Qt.exec(this[t]);if(null!==e)return{width:parseInt(e[1],10),height:parseInt(e[2],10)}}static parseAttrList(t){let e;const i={},s='"';Jt.lastIndex=0;while(null!==(e=Jt.exec(t))){let t=e[2];0===t.indexOf(s)&&t.lastIndexOf(s)===t.length-1&&(t=t.slice(1,-1));const r=e[1].trim();i[r]=t}return i}}function te(t){return"ID"!==t&&"CLASS"!==t&&"START-DATE"!==t&&"DURATION"!==t&&"END-DATE"!==t&&"END-ON-NEXT"!==t}function ee(t){return"SCTE35-OUT"===t||"SCTE35-IN"===t}class ie{constructor(t,e){if(this.attr=void 0,this._startDate=void 0,this._endDate=void 0,this._badValueForSameId=void 0,e){const i=e.attr;for(const e in i)if(Object.prototype.hasOwnProperty.call(t,e)&&t[e]!==i[e]){Xt.warn(`DATERANGE tag attribute: "${e}" does not match for tags with ID: "${t.ID}"`),this._badValueForSameId=e;break}t=Nt(new Zt({}),i,t)}if(this.attr=t,this._startDate=new Date(t["START-DATE"]),"END-DATE"in this.attr){const t=new Date(this.attr["END-DATE"]);Ut(t.getTime())&&(this._endDate=t)}}get id(){return this.attr.ID}get class(){return this.attr.CLASS}get startDate(){return this._startDate}get endDate(){if(this._endDate)return this._endDate;const t=this.duration;return null!==t?new Date(this._startDate.getTime()+1e3*t):null}get duration(){if("DURATION"in this.attr){const t=this.attr.decimalFloatingPoint("DURATION");if(Ut(t))return t}else if(this._endDate)return(this._endDate.getTime()-this._startDate.getTime())/1e3;return null}get plannedDuration(){return"PLANNED-DURATION"in this.attr?this.attr.decimalFloatingPoint("PLANNED-DURATION"):null}get endOnNext(){return this.attr.bool("END-ON-NEXT")}get isValid(){return!!this.id&&!this._badValueForSameId&&Ut(this.startDate.getTime())&&(null===this.duration||this.duration>=0)&&(!this.endOnNext||!!this.class)}}class se{constructor(){this.aborted=!1,this.loaded=0,this.retry=0,this.total=0,this.chunkCount=0,this.bwEstimate=0,this.loading={start:0,first:0,end:0},this.parsing={start:0,end:0},this.buffering={start:0,first:0,end:0}}}var re={AUDIO:"audio",VIDEO:"video",AUDIOVIDEO:"audiovideo"};class ne{constructor(t){this._byteRange=null,this._url=null,this.baseurl=void 0,this.relurl=void 0,this.elementaryStreams={[re.AUDIO]:null,[re.VIDEO]:null,[re.AUDIOVIDEO]:null},this.baseurl=t}setByteRange(t,e){const i=t.split("@",2);let s;s=1===i.length?(null==e?void 0:e.byteRangeEndOffset)||0:parseInt(i[1]),this._byteRange=[s,parseInt(i[0])+s]}get byteRange(){return this._byteRange?this._byteRange:[]}get byteRangeStartOffset(){return this.byteRange[0]}get byteRangeEndOffset(){return this.byteRange[1]}get url(){return!this._url&&this.baseurl&&this.relurl&&(this._url=_t.buildAbsoluteURL(this.baseurl,this.relurl,{alwaysNormalize:!0})),this._url||""}set url(t){this._url=t}}class ae extends ne{constructor(t,e){super(e),this._decryptdata=null,this.rawProgramDateTime=null,this.programDateTime=null,this.tagList=[],this.duration=0,this.sn=0,this.levelkeys=void 0,this.type=void 0,this.loader=null,this.keyLoader=null,this.level=-1,this.cc=0,this.startPTS=void 0,this.endPTS=void 0,this.startDTS=void 0,this.endDTS=void 0,this.start=0,this.deltaPTS=void 0,this.maxStartPTS=void 0,this.minEndPTS=void 0,this.stats=new se,this.data=void 0,this.bitrateTest=!1,this.title=null,this.initSegment=null,this.endList=void 0,this.gap=void 0,this.urlId=0,this.type=t}get decryptdata(){const{levelkeys:t}=this;if(!t&&!this._decryptdata)return null;if(!this._decryptdata&&this.levelkeys&&!this.levelkeys.NONE){const t=this.levelkeys.identity;if(t)this._decryptdata=t.getDecryptData(this.sn);else{const t=Object.keys(this.levelkeys);if(1===t.length)return this._decryptdata=this.levelkeys[t[0]].getDecryptData(this.sn)}}return this._decryptdata}get end(){return this.start+this.duration}get endProgramDateTime(){if(null===this.programDateTime)return null;if(!Ut(this.programDateTime))return null;const t=Ut(this.duration)?this.duration:0;return this.programDateTime+1e3*t}get encrypted(){var t;if(null!=(t=this._decryptdata)&&t.encrypted)return!0;if(this.levelkeys){const t=Object.keys(this.levelkeys),e=t.length;if(e>1||1===e&&this.levelkeys[t[0]].encrypted)return!0}return!1}setKeyFormat(t){if(this.levelkeys){const e=this.levelkeys[t];e&&!this._decryptdata&&(this._decryptdata=e.getDecryptData(this.sn))}}abortRequests(){var t,e;null==(t=this.loader)||t.abort(),null==(e=this.keyLoader)||e.abort()}setElementaryStreamInfo(t,e,i,s,r,n=!1){const{elementaryStreams:a}=this,o=a[t];o?(o.startPTS=Math.min(o.startPTS,e),o.endPTS=Math.max(o.endPTS,i),o.startDTS=Math.min(o.startDTS,s),o.endDTS=Math.max(o.endDTS,r)):a[t]={startPTS:e,endPTS:i,startDTS:s,endDTS:r,partial:n}}clearElementaryStreamInfo(){const{elementaryStreams:t}=this;t[re.AUDIO]=null,t[re.VIDEO]=null,t[re.AUDIOVIDEO]=null}}class oe extends ne{constructor(t,e,i,s,r){super(i),this.fragOffset=0,this.duration=0,this.gap=!1,this.independent=!1,this.relurl=void 0,this.fragment=void 0,this.index=void 0,this.stats=new se,this.duration=t.decimalFloatingPoint("DURATION"),this.gap=t.bool("GAP"),this.independent=t.bool("INDEPENDENT"),this.relurl=t.enumeratedString("URI"),this.fragment=e,this.index=s;const n=t.enumeratedString("BYTERANGE");n&&this.setByteRange(n,r),r&&(this.fragOffset=r.fragOffset+r.duration)}get start(){return this.fragment.start+this.fragOffset}get end(){return this.start+this.duration}get loaded(){const{elementaryStreams:t}=this;return!!(t.audio||t.video||t.audiovideo)}}const le=10;class he{constructor(t){this.PTSKnown=!1,this.alignedSliding=!1,this.averagetargetduration=void 0,this.endCC=0,this.endSN=0,this.fragments=void 0,this.fragmentHint=void 0,this.partList=null,this.dateRanges=void 0,this.live=!0,this.ageHeader=0,this.advancedDateTime=void 0,this.updated=!0,this.advanced=!0,this.availabilityDelay=void 0,this.misses=0,this.startCC=0,this.startSN=0,this.startTimeOffset=null,this.targetduration=0,this.totalduration=0,this.type=null,this.url=void 0,this.m3u8="",this.version=null,this.canBlockReload=!1,this.canSkipUntil=0,this.canSkipDateRanges=!1,this.skippedSegments=0,this.recentlyRemovedDateranges=void 0,this.partHoldBack=0,this.holdBack=0,this.partTarget=0,this.preloadHint=void 0,this.renditionReports=void 0,this.tuneInGoal=0,this.deltaUpdateFailed=void 0,this.driftStartTime=0,this.driftEndTime=0,this.driftStart=0,this.driftEnd=0,this.encryptedFragments=void 0,this.playlistParsingError=null,this.variableList=null,this.hasVariableRefs=!1,this.fragments=[],this.encryptedFragments=[],this.dateRanges={},this.url=t}reloaded(t){if(!t)return this.advanced=!0,void(this.updated=!0);const e=this.lastPartSn-t.lastPartSn,i=this.lastPartIndex-t.lastPartIndex;this.updated=this.endSN!==t.endSN||!!i||!!e||!this.live,this.advanced=this.endSN>t.endSN||e>0||0===e&&i>0,this.updated||this.advanced?this.misses=Math.floor(.6*t.misses):this.misses=t.misses+1,this.availabilityDelay=t.availabilityDelay}get hasProgramDateTime(){return!!this.fragments.length&&Ut(this.fragments[this.fragments.length-1].programDateTime)}get levelTargetDuration(){return this.averagetargetduration||this.targetduration||le}get drift(){const t=this.driftEndTime-this.driftStartTime;if(t>0){const e=this.driftEnd-this.driftStart;return 1e3*e/t}return 1}get edge(){return this.partEnd||this.fragmentEnd}get partEnd(){var t;return null!=(t=this.partList)&&t.length?this.partList[this.partList.length-1].end:this.fragmentEnd}get fragmentEnd(){var t;return null!=(t=this.fragments)&&t.length?this.fragments[this.fragments.length-1].end:0}get age(){return this.advancedDateTime?Math.max(Date.now()-this.advancedDateTime,0)/1e3:0}get lastPartIndex(){var t;return null!=(t=this.partList)&&t.length?this.partList[this.partList.length-1].index:-1}get lastPartSn(){var t;return null!=(t=this.partList)&&t.length?this.partList[this.partList.length-1].fragment.sn:this.endSN}}function ce(t){return Uint8Array.from(atob(t),t=>t.charCodeAt(0))}function de(t){const e=ge(t).subarray(0,16),i=new Uint8Array(16);return i.set(e,16-e.length),i}function ue(t){const e=function(t,e,i){const s=t[e];t[e]=t[i],t[i]=s};e(t,0,3),e(t,1,2),e(t,4,5),e(t,6,7)}function fe(t){const e=t.split(":");let i=null;if("data"===e[0]&&2===e.length){const t=e[1].split(";"),s=t[t.length-1].split(",");if(2===s.length){const e="base64"===s[0],r=s[1];e?(t.splice(-1,1),i=ce(r)):i=de(r)}}return i}function ge(t){return Uint8Array.from(unescape(encodeURIComponent(t)),t=>t.charCodeAt(0))}const me="undefined"!==typeof self?self:void 0;var pe={CLEARKEY:"org.w3.clearkey",FAIRPLAY:"com.apple.fps",PLAYREADY:"com.microsoft.playready",WIDEVINE:"com.widevine.alpha"},ve={CLEARKEY:"org.w3.clearkey",FAIRPLAY:"com.apple.streamingkeydelivery",PLAYREADY:"com.microsoft.playready",WIDEVINE:"urn:uuid:edef8ba9-79d6-4ace-a3c8-27dcd51d21ed"};function ye(t){switch(t){case ve.FAIRPLAY:return pe.FAIRPLAY;case ve.PLAYREADY:return pe.PLAYREADY;case ve.WIDEVINE:return pe.WIDEVINE;case ve.CLEARKEY:return pe.CLEARKEY}}var Ee={WIDEVINE:"edef8ba979d64acea3c827dcd51d21ed"};function Te(t){if(t===Ee.WIDEVINE)return pe.WIDEVINE}function Se(t){switch(t){case pe.FAIRPLAY:return ve.FAIRPLAY;case pe.PLAYREADY:return ve.PLAYREADY;case pe.WIDEVINE:return ve.WIDEVINE;case pe.CLEARKEY:return ve.CLEARKEY}}function be(t){const{drmSystems:e,widevineLicenseUrl:i}=t,s=e?[pe.FAIRPLAY,pe.WIDEVINE,pe.PLAYREADY,pe.CLEARKEY].filter(t=>!!e[t]):[];return!s[pe.WIDEVINE]&&i&&s.push(pe.WIDEVINE),s}const Ae=function(t){return null!=me&&null!=(t=me.navigator)&&t.requestMediaKeySystemAccess?self.navigator.requestMediaKeySystemAccess.bind(self.navigator):null}();function Le(t,e,i,s){let r;switch(t){case pe.FAIRPLAY:r=["cenc","sinf"];break;case pe.WIDEVINE:case pe.PLAYREADY:r=["cenc"];break;case pe.CLEARKEY:r=["cenc","keyids"];break;default:throw new Error("Unknown key-system: "+t)}return Re(r,e,i,s)}function Re(t,e,i,s){const r={initDataTypes:t,persistentState:s.persistentState||"optional",distinctiveIdentifier:s.distinctiveIdentifier||"optional",sessionTypes:s.sessionTypes||[s.sessionType||"temporary"],audioCapabilities:e.map(t=>({contentType:`audio/mp4; codecs="${t}"`,robustness:s.audioRobustness||"",encryptionScheme:s.audioEncryptionScheme||null})),videoCapabilities:i.map(t=>({contentType:`video/mp4; codecs="${t}"`,robustness:s.videoRobustness||"",encryptionScheme:s.videoEncryptionScheme||null}))};return[r]}function De(t,e,i){return Uint8Array.prototype.slice?t.slice(e,i):new Uint8Array(Array.prototype.slice.call(t,e,i))}const Ie=(t,e)=>e+10<=t.length&&73===t[e]&&68===t[e+1]&&51===t[e+2]&&t[e+3]<255&&t[e+4]<255&&t[e+6]<128&&t[e+7]<128&&t[e+8]<128&&t[e+9]<128,ke=(t,e)=>e+10<=t.length&&51===t[e]&&68===t[e+1]&&73===t[e+2]&&t[e+3]<255&&t[e+4]<255&&t[e+6]<128&&t[e+7]<128&&t[e+8]<128&&t[e+9]<128,we=(t,e)=>{const i=e;let s=0;while(Ie(t,e)){s+=10;const i=Ce(t,e+6);s+=i,ke(t,e+10)&&(s+=10),e+=s}if(s>0)return t.subarray(i,i+s)},Ce=(t,e)=>{let i=0;return i=(127&t[e])<<21,i|=(127&t[e+1])<<14,i|=(127&t[e+2])<<7,i|=127&t[e+3],i},_e=(t,e)=>Ie(t,e)&&Ce(t,e+6)+10<=t.length-e,Oe=t=>{const e=Me(t);for(let i=0;i<e.length;i++){const t=e[i];if(xe(t))return $e(t)}},xe=t=>t&&"PRIV"===t.key&&"com.apple.streaming.transportStreamTimestamp"===t.info,Pe=t=>{const e=String.fromCharCode(t[0],t[1],t[2],t[3]),i=Ce(t,4),s=10;return{type:e,size:i,data:t.subarray(s,s+i)}},Me=t=>{let e=0;const i=[];while(Ie(t,e)){const s=Ce(t,e+6);e+=10;const r=e+s;while(e+8<r){const s=Pe(t.subarray(e)),r=Fe(s);r&&i.push(r),e+=s.size+10}ke(t,e)&&(e+=10)}return i},Fe=t=>"PRIV"===t.type?Ne(t):"W"===t.type[0]?Be(t):Ue(t),Ne=t=>{if(t.size<2)return;const e=Ge(t.data,!0),i=new Uint8Array(t.data.subarray(e.length+1));return{key:t.type,info:e,data:i.buffer}},Ue=t=>{if(t.size<2)return;if("TXXX"===t.type){let e=1;const i=Ge(t.data.subarray(e),!0);e+=i.length+1;const s=Ge(t.data.subarray(e));return{key:t.type,info:i,data:s}}const e=Ge(t.data.subarray(1));return{key:t.type,data:e}},Be=t=>{if("WXXX"===t.type){if(t.size<2)return;let e=1;const i=Ge(t.data.subarray(e),!0);e+=i.length+1;const s=Ge(t.data.subarray(e));return{key:t.type,info:i,data:s}}const e=Ge(t.data);return{key:t.type,data:e}},$e=t=>{if(8===t.data.byteLength){const e=new Uint8Array(t.data),i=1&e[3];let s=(e[4]<<23)+(e[5]<<15)+(e[6]<<7)+e[7];return s/=45,i&&(s+=47721858.84),Math.round(s)}},Ge=(t,e=!1)=>{const i=Ke();if(i){const s=i.decode(t);if(e){const t=s.indexOf("\0");return-1!==t?s.substring(0,t):s}return s.replace(/\0/g,"")}const s=t.length;let r,n,a,o="",l=0;while(l<s){if(r=t[l++],0===r&&e)return o;if(0!==r&&3!==r)switch(r>>4){case 0:case 1:case 2:case 3:case 4:case 5:case 6:case 7:o+=String.fromCharCode(r);break;case 12:case 13:n=t[l++],o+=String.fromCharCode((31&r)<<6|63&n);break;case 14:n=t[l++],a=t[l++],o+=String.fromCharCode((15&r)<<12|(63&n)<<6|(63&a)<<0);break}}return o};let je;function Ke(){if(!navigator.userAgent.includes("PlayStation 4"))return je||"undefined"===typeof self.TextDecoder||(je=new self.TextDecoder("utf-8")),je}const Ve={hexDump:function(t){let e="";for(let i=0;i<t.length;i++){let s=t[i].toString(16);s.length<2&&(s="0"+s),e+=s}return e}},He=Math.pow(2,32)-1,qe=[].push,Ye={video:1,audio:2,id3:3,text:4};function We(t){return String.fromCharCode.apply(null,t)}function ze(t,e){const i=t[e]<<8|t[e+1];return i<0?65536+i:i}function Xe(t,e){const i=Je(t,e);return i<0?4294967296+i:i}function Qe(t,e){let i=Xe(t,e);return i*=Math.pow(2,32),i+=Xe(t,e+4),i}function Je(t,e){return t[e]<<24|t[e+1]<<16|t[e+2]<<8|t[e+3]}function Ze(t,e,i){t[e]=i>>24,t[e+1]=i>>16&255,t[e+2]=i>>8&255,t[e+3]=255&i}function ti(t){const e=t.byteLength;for(let i=0;i<e;){const s=Xe(t,i);if(s>8&&109===t[i+4]&&111===t[i+5]&&111===t[i+6]&&102===t[i+7])return!0;i=s>1?i+s:e}return!1}function ei(t,e){const i=[];if(!e.length)return i;const s=t.byteLength;for(let r=0;r<s;){const n=Xe(t,r),a=We(t.subarray(r+4,r+8)),o=n>1?r+n:s;if(a===e[0])if(1===e.length)i.push(t.subarray(r+8,o));else{const s=ei(t.subarray(r+8,o),e.slice(1));s.length&&qe.apply(i,s)}r=o}return i}function ii(t){const e=[],i=t[0];let s=8;const r=Xe(t,s);s+=4;let n=0,a=0;0===i?(n=Xe(t,s),a=Xe(t,s+4),s+=8):(n=Qe(t,s),a=Qe(t,s+8),s+=16),s+=2;let o=t.length+a;const l=ze(t,s);s+=2;for(let h=0;h<l;h++){let i=s;const n=Xe(t,i);i+=4;const a=2147483647&n,l=(2147483648&n)>>>31;if(1===l)return Xt.warn("SIDX has hierarchical references (not supported)"),null;const h=Xe(t,i);i+=4,e.push({referenceSize:a,subsegmentDuration:h,info:{duration:h/r,start:o,end:o+a-1}}),o+=a,i+=4,s=i}return{earliestPresentationTime:n,timescale:r,version:i,referencesCount:l,references:e}}function si(t){const e=[],i=ei(t,["moov","trak"]);for(let r=0;r<i.length;r++){const t=i[r],s=ei(t,["tkhd"])[0];if(s){let i=s[0];const r=Xe(s,0===i?12:20),n=ei(t,["mdia","mdhd"])[0];if(n){i=n[0];const s=Xe(n,0===i?12:20),a=ei(t,["mdia","hdlr"])[0];if(a){const i=We(a.subarray(8,12)),n={soun:re.AUDIO,vide:re.VIDEO}[i];if(n){const i=ei(t,["mdia","minf","stbl","stsd"])[0],a=ri(i);e[r]={timescale:s,type:n},e[n]=xt({timescale:s,id:r},a)}}}}}const s=ei(t,["moov","mvex","trex"]);return s.forEach(t=>{const i=Xe(t,4),s=e[i];s&&(s.default={duration:Xe(t,12),flags:Xe(t,20)})}),e}function ri(t){const e=t.subarray(8),i=e.subarray(86),s=We(e.subarray(4,8));let r=s;const n="enca"===s||"encv"===s;if(n){const t=ei(e,[s])[0],i=t.subarray("enca"===s?28:78),n=ei(i,["sinf"]);n.forEach(t=>{const e=ei(t,["schm"])[0];if(e){const i=We(e.subarray(4,8));if("cbcs"===i||"cenc"===i){const e=ei(t,["frma"])[0];e&&(r=We(e))}}})}switch(r){case"avc1":case"avc2":case"avc3":case"avc4":{const t=ei(i,["avcC"])[0];r+="."+ai(t[1])+ai(t[2])+ai(t[3]);break}case"mp4a":{const t=ei(e,[s])[0],i=ei(t.subarray(28),["esds"])[0];if(i&&i.length>12){let t=4;if(3!==i[t++])break;t=ni(i,t),t+=2;const e=i[t++];if(128&e&&(t+=2),64&e&&(t+=i[t++]),4!==i[t++])break;t=ni(i,t);const s=i[t++];if(64!==s)break;if(r+="."+ai(s),t+=12,5!==i[t++])break;t=ni(i,t);const n=i[t++];let a=(248&n)>>3;31===a&&(a+=1+((7&n)<<3)+((224&i[t])>>5)),r+="."+a}break}case"hvc1":case"hev1":{const t=ei(i,["hvcC"])[0],e=t[1],s=["","A","B","C"][e>>6],n=31&e,a=Xe(t,2),o=(32&e)>>5?"H":"L",l=t[12],h=t.subarray(6,12);r+="."+s+n,r+="."+a.toString(16).toUpperCase(),r+="."+o+l;let c="";for(let i=h.length;i--;){const t=h[i];if(t||c){const e=t.toString(16).toUpperCase();c="."+e+c}}r+=c;break}case"dvh1":case"dvhe":{const t=ei(i,["dvcC"])[0],e=t[2]>>1&127,s=t[2]<<5&32|t[3]>>3&31;r+="."+oi(e)+"."+oi(s);break}case"vp09":{const t=ei(i,["vpcC"])[0],e=t[4],s=t[5],n=t[6]>>4&15;r+="."+oi(e)+"."+oi(s)+"."+oi(n);break}case"av01":{const t=ei(i,["av1C"])[0],e=t[1]>>>5,s=31&t[1],n=t[2]>>>7?"H":"M",a=(64&t[2])>>6,o=(32&t[2])>>5,l=2===e&&a?o?12:10:a?10:8,h=(16&t[2])>>4,c=(8&t[2])>>3,d=(4&t[2])>>2,u=3&t[2],f=1,g=1,m=1,p=0;r+="."+e+"."+oi(s)+n+"."+oi(l)+"."+h+"."+c+d+u+"."+oi(f)+"."+oi(g)+"."+oi(m)+"."+p;break}}return{codec:r,encrypted:n}}function ni(t,e){const i=e+5;while(128&t[e++]&&e<i);return e}function ai(t){return("0"+t.toString(16).toUpperCase()).slice(-2)}function oi(t){return(t<10?"0":"")+t}function li(t,e){if(!t||!e)return t;const i=e.keyId;if(i&&e.isCommonEncryption){const e=ei(t,["moov","trak"]);e.forEach(t=>{const e=ei(t,["mdia","minf","stbl","stsd"])[0],s=e.subarray(8);let r=ei(s,["enca"]);const n=r.length>0;n||(r=ei(s,["encv"])),r.forEach(t=>{const e=n?t.subarray(28):t.subarray(78),s=ei(e,["sinf"]);s.forEach(t=>{const e=hi(t);if(e){const t=e.subarray(8,24);t.some(t=>0!==t)||(Xt.log(`[eme] Patching keyId in 'enc${n?"a":"v"}>sinf>>tenc' box: ${Ve.hexDump(t)} -> ${Ve.hexDump(i)}`),e.set(i,8))}})})})}return t}function hi(t){const e=ei(t,["schm"])[0];if(e){const i=We(e.subarray(4,8));if("cbcs"===i||"cenc"===i)return ei(t,["schi","tenc"])[0]}return Xt.error("[eme] missing 'schm' box"),null}function ci(t,e){return ei(e,["moof","traf"]).reduce((e,i)=>{const s=ei(i,["tfdt"])[0],r=s[0],n=ei(i,["tfhd"]).reduce((e,i)=>{const n=Xe(i,4),a=t[n];if(a){let t=Xe(s,4);if(1===r){if(t===He)return Xt.warn("[mp4-demuxer]: Ignoring assumed invalid signed 64-bit track fragment decode time"),e;t*=He+1,t+=Xe(s,8)}const i=a.timescale||9e4,n=t/i;if(Ut(n)&&(null===e||n<e))return n}return e},null);return null!==n&&Ut(n)&&(null===e||n<e)?n:e},null)}function di(t,e){let i=0,s=0,r=0;const n=ei(t,["moof","traf"]);for(let a=0;a<n.length;a++){const t=n[a],o=ei(t,["tfhd"])[0],l=Xe(o,4),h=e[l];if(!h)continue;const c=h.default,d=Xe(o,0)|(null==c?void 0:c.flags);let u=null==c?void 0:c.duration;8&d&&(u=Xe(o,2&d?12:8));const f=h.timescale||9e4,g=ei(t,["trun"]);for(let e=0;e<g.length;e++){if(i=ui(g[e]),!i&&u){const t=Xe(g[e],4);i=u*t}h.type===re.VIDEO?s+=i/f:h.type===re.AUDIO&&(r+=i/f)}}if(0===s&&0===r){let e=1/0,i=0,s=0;const r=ei(t,["sidx"]);for(let t=0;t<r.length;t++){const n=ii(r[t]);if(null!=n&&n.references){e=Math.min(e,n.earliestPresentationTime/n.timescale);const t=n.references.reduce((t,e)=>t+e.info.duration||0,0);i=Math.max(i,t+n.earliestPresentationTime/n.timescale),s=i-e}}if(s&&Ut(s))return s}return s||r}function ui(t){const e=Xe(t,0);let i=8;1&e&&(i+=4),4&e&&(i+=4);let s=0;const r=Xe(t,4);for(let n=0;n<r;n++){if(256&e){const e=Xe(t,i);s+=e,i+=4}512&e&&(i+=4),1024&e&&(i+=4),2048&e&&(i+=4)}return s}function fi(t,e,i){ei(e,["moof","traf"]).forEach(e=>{ei(e,["tfhd"]).forEach(s=>{const r=Xe(s,4),n=t[r];if(!n)return;const a=n.timescale||9e4;ei(e,["tfdt"]).forEach(t=>{const e=t[0],s=i*a;if(s){let i=Xe(t,4);if(0===e)i-=s,i=Math.max(i,0),Ze(t,4,i);else{i*=Math.pow(2,32),i+=Xe(t,8),i-=s,i=Math.max(i,0);const e=Math.floor(i/(He+1)),r=Math.floor(i%(He+1));Ze(t,4,e),Ze(t,8,r)}}})})})}function gi(t){const e={valid:null,remainder:null},i=ei(t,["moof"]);if(i.length<2)return e.remainder=t,e;const s=i[i.length-1];return e.valid=De(t,0,s.byteOffset-8),e.remainder=De(t,s.byteOffset-8),e}function mi(t,e){const i=new Uint8Array(t.length+e.length);return i.set(t),i.set(e,t.length),i}function pi(t,e){const i=[],s=e.samples,r=e.timescale,n=e.id;let a=!1;const o=ei(s,["moof"]);return o.map(o=>{const l=o.byteOffset-8,h=ei(o,["traf"]);h.map(o=>{const h=ei(o,["tfdt"]).map(t=>{const e=t[0];let i=Xe(t,4);return 1===e&&(i*=Math.pow(2,32),i+=Xe(t,8)),i/r})[0];return void 0!==h&&(t=h),ei(o,["tfhd"]).map(h=>{const c=Xe(h,4),d=16777215&Xe(h,0),u=0!==(1&d),f=0!==(2&d),g=0!==(8&d);let m=0;const p=0!==(16&d);let v=0;const y=0!==(32&d);let E=8;c===n&&(u&&(E+=8),f&&(E+=4),g&&(m=Xe(h,E),E+=4),p&&(v=Xe(h,E),E+=4),y&&(E+=4),"video"===e.type&&(a=vi(e.codec)),ei(o,["trun"]).map(n=>{const o=n[0],h=16777215&Xe(n,0),c=0!==(1&h);let d=0;const u=0!==(4&h),f=0!==(256&h);let g=0;const p=0!==(512&h);let y=0;const E=0!==(1024&h),T=0!==(2048&h);let S=0;const b=Xe(n,4);let A=8;c&&(d=Xe(n,A),A+=4),u&&(A+=4);let L=d+l;for(let l=0;l<b;l++){if(f?(g=Xe(n,A),A+=4):g=m,p?(y=Xe(n,A),A+=4):y=v,E&&(A+=4),T&&(S=0===o?Xe(n,A):Je(n,A),A+=4),e.type===re.VIDEO){let e=0;while(e<y){const n=Xe(s,L);if(L+=4,yi(a,s[L])){const e=s.subarray(L,L+n);Ei(e,a?2:1,t+S/r,i)}L+=n,e+=n+4}}t+=g/r}}))})})}),i}function vi(t){if(!t)return!1;const e=t.indexOf("."),i=e<0?t:t.substring(0,e);return"hvc1"===i||"hev1"===i||"dvh1"===i||"dvhe"===i}function yi(t,e){if(t){const t=e>>1&63;return 39===t||40===t}{const t=31&e;return 6===t}}function Ei(t,e,i,s){const r=Ti(t);let n=0;n+=e;let a=0,o=0,l=0;while(n<r.length){a=0;do{if(n>=r.length)break;l=r[n++],a+=l}while(255===l);o=0;do{if(n>=r.length)break;l=r[n++],o+=l}while(255===l);const t=r.length-n;let e=n;if(o<t)n+=o;else if(o>t){Xt.error(`Malformed SEI payload. ${o} is too small, only ${t} bytes left to parse.`);break}if(4===a){const t=r[e++];if(181===t){const t=ze(r,e);if(e+=2,49===t){const t=Xe(r,e);if(e+=4,1195456820===t){const t=r[e++];if(3===t){const n=r[e++],o=31&n,l=64&n,h=l?2+3*o:0,c=new Uint8Array(h);if(l){c[0]=n;for(let t=1;t<h;t++)c[t]=r[e++]}s.push({type:t,payloadType:a,pts:i,bytes:c})}}}}}else if(5===a&&o>16){const t=[];for(let i=0;i<16;i++){const s=r[e++].toString(16);t.push(1==s.length?"0"+s:s),3!==i&&5!==i&&7!==i&&9!==i||t.push("-")}const n=o-16,l=new Uint8Array(n);for(let i=0;i<n;i++)l[i]=r[e++];s.push({payloadType:a,pts:i,uuid:t.join(""),userData:Ge(l),userDataBytes:l})}}}function Ti(t){const e=t.byteLength,i=[];let s=1;while(s<e-2)0===t[s]&&0===t[s+1]&&3===t[s+2]?(i.push(s+2),s+=2):s++;if(0===i.length)return t;const r=e-i.length,n=new Uint8Array(r);let a=0;for(s=0;s<r;a++,s++)a===i[0]&&(a++,i.shift()),n[s]=t[a];return n}function Si(t){const e=t[0];let i="",s="",r=0,n=0,a=0,o=0,l=0,h=0;if(0===e){while("\0"!==We(t.subarray(h,h+1)))i+=We(t.subarray(h,h+1)),h+=1;i+=We(t.subarray(h,h+1)),h+=1;while("\0"!==We(t.subarray(h,h+1)))s+=We(t.subarray(h,h+1)),h+=1;s+=We(t.subarray(h,h+1)),h+=1,r=Xe(t,12),n=Xe(t,16),o=Xe(t,20),l=Xe(t,24),h=28}else if(1===e){h+=4,r=Xe(t,h),h+=4;const e=Xe(t,h);h+=4;const n=Xe(t,h);h+=4,a=2**32*e+n,Bt(a)||(a=Number.MAX_SAFE_INTEGER,Xt.warn("Presentation time exceeds safe integer limit and wrapped to max safe integer in parsing emsg box")),o=Xe(t,h),h+=4,l=Xe(t,h),h+=4;while("\0"!==We(t.subarray(h,h+1)))i+=We(t.subarray(h,h+1)),h+=1;i+=We(t.subarray(h,h+1)),h+=1;while("\0"!==We(t.subarray(h,h+1)))s+=We(t.subarray(h,h+1)),h+=1;s+=We(t.subarray(h,h+1)),h+=1}const c=t.subarray(h,t.byteLength);return{schemeIdUri:i,value:s,timeScale:r,presentationTime:a,presentationTimeDelta:n,eventDuration:o,id:l,payload:c}}function bi(t,...e){const i=e.length;let s=8,r=i;while(r--)s+=e[r].byteLength;const n=new Uint8Array(s);for(n[0]=s>>24&255,n[1]=s>>16&255,n[2]=s>>8&255,n[3]=255&s,n.set(t,4),r=0,s=8;r<i;r++)n.set(e[r],s),s+=e[r].byteLength;return n}function Ai(t,e,i){if(16!==t.byteLength)throw new RangeError("Invalid system id");let s,r,n;if(e){s=1,r=new Uint8Array(16*e.length);for(let t=0;t<e.length;t++){const i=e[t];if(16!==i.byteLength)throw new RangeError("Invalid key");r.set(i,16*t)}}else s=0,r=new Uint8Array;s>0?(n=new Uint8Array(4),e.length>0&&new DataView(n.buffer).setUint32(0,e.length,!1)):n=new Uint8Array;const a=new Uint8Array(4);return i&&i.byteLength>0&&new DataView(a.buffer).setUint32(0,i.byteLength,!1),bi([112,115,115,104],new Uint8Array([s,0,0,0]),t,n,r,a,i||new Uint8Array)}function Li(t){if(!(t instanceof ArrayBuffer)||t.byteLength<32)return null;const e={version:0,systemId:"",kids:null,data:null},i=new DataView(t),s=i.getUint32(0);if(t.byteLength!==s&&s>44)return null;const r=i.getUint32(4);if(1886614376!==r)return null;if(e.version=i.getUint32(8)>>>24,e.version>1)return null;e.systemId=Ve.hexDump(new Uint8Array(t,12,16));const n=i.getUint32(28);if(0===e.version){if(s-32<n)return null;e.data=new Uint8Array(t,32,n)}else if(1===e.version){e.kids=[];for(let i=0;i<n;i++)e.kids.push(new Uint8Array(t,32+16*i,16))}return e}let Ri={};class Di{static clearKeyUriToKeyIdMap(){Ri={}}constructor(t,e,i,s=[1],r=null){this.uri=void 0,this.method=void 0,this.keyFormat=void 0,this.keyFormatVersions=void 0,this.encrypted=void 0,this.isCommonEncryption=void 0,this.iv=null,this.key=null,this.keyId=null,this.pssh=null,this.method=t,this.uri=e,this.keyFormat=i,this.keyFormatVersions=s,this.iv=r,this.encrypted=!!t&&"NONE"!==t,this.isCommonEncryption=this.encrypted&&"AES-128"!==t}isSupported(){if(this.method){if("AES-128"===this.method||"NONE"===this.method)return!0;if("identity"===this.keyFormat)return"SAMPLE-AES"===this.method;switch(this.keyFormat){case ve.FAIRPLAY:case ve.WIDEVINE:case ve.PLAYREADY:case ve.CLEARKEY:return-1!==["ISO-23001-7","SAMPLE-AES","SAMPLE-AES-CENC","SAMPLE-AES-CTR"].indexOf(this.method)}}return!1}getDecryptData(t){if(!this.encrypted||!this.uri)return null;if("AES-128"===this.method&&this.uri&&!this.iv){"number"!==typeof t&&("AES-128"!==this.method||this.iv||Xt.warn(`missing IV for initialization segment with method="${this.method}" - compliance issue`),t=0);const e=Ii(t),i=new Di(this.method,this.uri,"identity",this.keyFormatVersions,e);return i}const e=fe(this.uri);if(e)switch(this.keyFormat){case ve.WIDEVINE:this.pssh=e,e.length>=22&&(this.keyId=e.subarray(e.length-22,e.length-6));break;case ve.PLAYREADY:{const t=new Uint8Array([154,4,240,121,152,64,66,134,171,146,230,91,224,136,95,149]);this.pssh=Ai(t,null,e);const i=new Uint16Array(e.buffer,e.byteOffset,e.byteLength/2),s=String.fromCharCode.apply(null,Array.from(i)),r=s.substring(s.indexOf("<"),s.length),n=new DOMParser,a=n.parseFromString(r,"text/xml"),o=a.getElementsByTagName("KID")[0];if(o){const t=o.childNodes[0]?o.childNodes[0].nodeValue:o.getAttribute("VALUE");if(t){const e=ce(t).subarray(0,16);ue(e),this.keyId=e}}break}default:{let t=e.subarray(0,16);if(16!==t.length){const e=new Uint8Array(16);e.set(t,16-t.length),t=e}this.keyId=t;break}}if(!this.keyId||16!==this.keyId.byteLength){let t=Ri[this.uri];if(!t){const e=Object.keys(Ri).length%Number.MAX_SAFE_INTEGER;t=new Uint8Array(16);const i=new DataView(t.buffer,12,4);i.setUint32(0,e),Ri[this.uri]=t}this.keyId=t}return this}}function Ii(t){const e=new Uint8Array(16);for(let i=12;i<16;i++)e[i]=t>>8*(15-i)&255;return e}const ki=/\{\$([a-zA-Z0-9-_]+)\}/g;function wi(t){return ki.test(t)}function Ci(t,e,i){if(null!==t.variableList||t.hasVariableRefs)for(let s=i.length;s--;){const r=i[s],n=e[r];n&&(e[r]=_i(t,n))}}function _i(t,e){if(null!==t.variableList||t.hasVariableRefs){const i=t.variableList;return e.replace(ki,e=>{const s=e.substring(2,e.length-1),r=null==i?void 0:i[s];return void 0===r?(t.playlistParsingError||(t.playlistParsingError=new Error(`Missing preceding EXT-X-DEFINE tag for Variable Reference: "${s}"`)),e):r})}return e}function Oi(t,e,i){let s,r,n=t.variableList;if(n||(t.variableList=n={}),"QUERYPARAM"in e){s=e.QUERYPARAM;try{const t=new self.URL(i).searchParams;if(!t.has(s))throw new Error(`"${s}" does not match any query parameter in URI: "${i}"`);r=t.get(s)}catch(a){t.playlistParsingError||(t.playlistParsingError=new Error("EXT-X-DEFINE QUERYPARAM: "+a.message))}}else s=e.NAME,r=e.VALUE;s in n?t.playlistParsingError||(t.playlistParsingError=new Error(`EXT-X-DEFINE duplicate Variable Name declarations: "${s}"`)):n[s]=r||""}function xi(t,e,i){const s=e.IMPORT;if(i&&s in i){let e=t.variableList;e||(t.variableList=e={}),e[s]=i[s]}else t.playlistParsingError||(t.playlistParsingError=new Error(`EXT-X-DEFINE IMPORT attribute not found in Multivariant Playlist: "${s}"`))}function Pi(t=!0){if("undefined"===typeof self)return;const e=(t||!self.MediaSource)&&self.ManagedMediaSource;return e||self.MediaSource||self.WebKitMediaSource}function Mi(t){return"undefined"!==typeof self&&t===self.ManagedMediaSource}const Fi={audio:{a3ds:1,"ac-3":.95,"ac-4":1,alac:.9,alaw:1,dra1:1,"dts+":1,"dts-":1,dtsc:1,dtse:1,dtsh:1,"ec-3":.9,enca:1,fLaC:.9,flac:.9,FLAC:.9,g719:1,g726:1,m4ae:1,mha1:1,mha2:1,mhm1:1,mhm2:1,mlpa:1,mp4a:1,"raw ":1,Opus:1,opus:1,samr:1,sawb:1,sawp:1,sevc:1,sqcp:1,ssmv:1,twos:1,ulaw:1},video:{avc1:1,avc2:1,avc3:1,avc4:1,avcp:1,av01:.8,drac:1,dva1:1,dvav:1,dvh1:.7,dvhe:.7,encv:1,hev1:.75,hvc1:.75,mjp2:1,mp4v:1,mvc1:1,mvc2:1,mvc3:1,mvc4:1,resv:1,rv60:1,s263:1,svc1:1,svc2:1,"vc-1":1,vp08:1,vp09:.9},text:{stpp:1,wvtt:1}};function Ni(t,e){const i=Fi[e];return!!i&&!!i[t.slice(0,4)]}function Ui(t,e,i=!0){return!t.split(",").some(t=>!Bi(t,e,i))}function Bi(t,e,i=!0){var s;const r=Pi(i);return null!=(s=null==r?void 0:r.isTypeSupported($i(t,e)))&&s}function $i(t,e){return`${e}/mp4;codecs="${t}"`}function Gi(t){if(t){const e=t.substring(0,4);return Fi.video[e]}return 2}function ji(t){return t.split(",").reduce((t,e)=>{const i=Fi.video[e];return i?(2*i+t)/(t?3:2):(Fi.audio[e]+t)/(t?2:1)},0)}const Ki={};function Vi(t,e=!0){if(Ki[t])return Ki[t];const i={flac:["flac","fLaC","FLAC"],opus:["opus","Opus"]}[t];for(let s=0;s<i.length;s++)if(Bi(i[s],"audio",e))return Ki[t]=i[s],i[s];return t}const Hi=/flac|opus/i;function qi(t,e=!0){return t.replace(Hi,t=>Vi(t.toLowerCase(),e))}function Yi(t,e){return t&&"mp4a"!==t?t:e?e.split(",")[0]:e}function Wi(t){const e=t.split(".");if(e.length>2){let t=e.shift()+".";return t+=parseInt(e.shift()).toString(16),t+=("000"+parseInt(e.shift()).toString(16)).slice(-4),t}return t}const zi=/#EXT-X-STREAM-INF:([^\r\n]*)(?:[\r\n](?:#[^\r\n]*)?)*([^\r\n]+)|#EXT-X-(SESSION-DATA|SESSION-KEY|DEFINE|CONTENT-STEERING|START):([^\r\n]*)[\r\n]+/g,Xi=/#EXT-X-MEDIA:(.*)/g,Qi=/^#EXT(?:INF|-X-TARGETDURATION):/m,Ji=new RegExp([/#EXTINF:\s*(\d*(?:\.\d+)?)(?:,(.*)\s+)?/.source,/(?!#) *(\S[^\r\n]*)/.source,/#EXT-X-BYTERANGE:*(.+)/.source,/#EXT-X-PROGRAM-DATE-TIME:(.+)/.source,/#.*/.source].join("|"),"g"),Zi=new RegExp([/#(EXTM3U)/.source,/#EXT-X-(DATERANGE|DEFINE|KEY|MAP|PART|PART-INF|PLAYLIST-TYPE|PRELOAD-HINT|RENDITION-REPORT|SERVER-CONTROL|SKIP|START):(.+)/.source,/#EXT-X-(BITRATE|DISCONTINUITY-SEQUENCE|MEDIA-SEQUENCE|TARGETDURATION|VERSION): *(\d+)/.source,/#EXT-X-(DISCONTINUITY|ENDLIST|GAP|INDEPENDENT-SEGMENTS)/.source,/(#)([^:]*):(.*)/.source,/(#)(.*)(?:.*)\r?\n?/.source].join("|"));class ts{static findGroup(t,e){for(let i=0;i<t.length;i++){const s=t[i];if(s.id===e)return s}}static resolve(t,e){return _t.buildAbsoluteURL(e,t,{alwaysNormalize:!0})}static isMediaPlaylist(t){return Qi.test(t)}static parseMasterPlaylist(t,e){const i=wi(t),s={contentSteering:null,levels:[],playlistParsingError:null,sessionData:null,sessionKeys:null,startTimeOffset:null,variableList:null,hasVariableRefs:i},r=[];let n;zi.lastIndex=0;while(null!=(n=zi.exec(t)))if(n[1]){var a;const t=new Zt(n[1]);Ci(s,t,["CODECS","SUPPLEMENTAL-CODECS","ALLOWED-CPC","PATHWAY-ID","STABLE-VARIANT-ID","AUDIO","VIDEO","SUBTITLES","CLOSED-CAPTIONS","NAME"]);const i=_i(s,n[2]),o={attrs:t,bitrate:t.decimalInteger("BANDWIDTH")||t.decimalInteger("AVERAGE-BANDWIDTH"),name:t.NAME,url:ts.resolve(i,e)},l=t.decimalResolution("RESOLUTION");l&&(o.width=l.width,o.height=l.height),ss(t.CODECS,o),null!=(a=o.unknownCodecs)&&a.length||r.push(o),s.levels.push(o)}else if(n[3]){const t=n[3],i=n[4];switch(t){case"SESSION-DATA":{const t=new Zt(i);Ci(s,t,["DATA-ID","LANGUAGE","VALUE","URI"]);const e=t["DATA-ID"];e&&(null===s.sessionData&&(s.sessionData={}),s.sessionData[e]=t);break}case"SESSION-KEY":{const t=es(i,e,s);t.encrypted&&t.isSupported()?(null===s.sessionKeys&&(s.sessionKeys=[]),s.sessionKeys.push(t)):Xt.warn(`[Keys] Ignoring invalid EXT-X-SESSION-KEY tag: "${i}"`);break}case"DEFINE":{const t=new Zt(i);Ci(s,t,["NAME","VALUE","QUERYPARAM"]),Oi(s,t,e)}break;case"CONTENT-STEERING":{const t=new Zt(i);Ci(s,t,["SERVER-URI","PATHWAY-ID"]),s.contentSteering={uri:ts.resolve(t["SERVER-URI"],e),pathwayId:t["PATHWAY-ID"]||"."};break}case"START":s.startTimeOffset=is(i);break}}const o=r.length>0&&r.length<s.levels.length;return s.levels=o?r:s.levels,0===s.levels.length&&(s.playlistParsingError=new Error("no levels found in manifest")),s}static parseMasterPlaylistMedia(t,e,i){let s;const r={},n=i.levels,a={AUDIO:n.map(t=>({id:t.attrs.AUDIO,audioCodec:t.audioCodec})),SUBTITLES:n.map(t=>({id:t.attrs.SUBTITLES,textCodec:t.textCodec})),"CLOSED-CAPTIONS":[]};let o=0;Xi.lastIndex=0;while(null!==(s=Xi.exec(t))){const t=new Zt(s[1]),n=t.TYPE;if(n){const s=a[n],l=r[n]||[];r[n]=l,Ci(i,t,["URI","GROUP-ID","LANGUAGE","ASSOC-LANGUAGE","STABLE-RENDITION-ID","NAME","INSTREAM-ID","CHARACTERISTICS","CHANNELS"]);const h=t.LANGUAGE,c=t["ASSOC-LANGUAGE"],d=t.CHANNELS,u=t.CHARACTERISTICS,f=t["INSTREAM-ID"],g={attrs:t,bitrate:0,id:o++,groupId:t["GROUP-ID"]||"",name:t.NAME||h||"",type:n,default:t.bool("DEFAULT"),autoselect:t.bool("AUTOSELECT"),forced:t.bool("FORCED"),lang:h,url:t.URI?ts.resolve(t.URI,e):""};if(c&&(g.assocLang=c),d&&(g.channels=d),u&&(g.characteristics=u),f&&(g.instreamId=f),null!=s&&s.length){const t=ts.findGroup(s,g.groupId)||s[0];rs(g,t,"audioCodec"),rs(g,t,"textCodec")}l.push(g)}}return r}static parseLevelPlaylist(t,e,i,s,r,n){const a=new he(e),o=a.fragments;let l,h,c,d=null,u=0,f=0,g=0,m=0,p=null,v=new ae(s,e),y=-1,E=!1,T=null;Ji.lastIndex=0,a.m3u8=t,a.hasVariableRefs=wi(t);while(null!==(l=Ji.exec(t))){E&&(E=!1,v=new ae(s,e),v.start=g,v.sn=u,v.cc=m,v.level=i,d&&(v.initSegment=d,v.rawProgramDateTime=d.rawProgramDateTime,d.rawProgramDateTime=null,T&&(v.setByteRange(T),T=null)));const t=l[1];if(t){v.duration=parseFloat(t);const e=(" "+l[2]).slice(1);v.title=e||null,v.tagList.push(e?["INF",t,e]:["INF",t])}else if(l[3]){if(Ut(v.duration)){v.start=g,c&&ls(v,c,a),v.sn=u,v.level=i,v.cc=m,o.push(v);const t=(" "+l[3]).slice(1);v.relurl=_i(a,t),as(v,p),p=v,g+=v.duration,u++,f=0,E=!0}}else if(l[4]){const t=(" "+l[4]).slice(1);p?v.setByteRange(t,p):v.setByteRange(t)}else if(l[5])v.rawProgramDateTime=(" "+l[5]).slice(1),v.tagList.push(["PROGRAM-DATE-TIME",v.rawProgramDateTime]),-1===y&&(y=o.length);else{if(l=l[0].match(Zi),!l){Xt.warn("No matches on slow regex match for level playlist!");continue}for(h=1;h<l.length;h++)if("undefined"!==typeof l[h])break;const t=(" "+l[h]).slice(1),r=(" "+l[h+1]).slice(1),g=l[h+2]?(" "+l[h+2]).slice(1):"";switch(t){case"PLAYLIST-TYPE":a.type=r.toUpperCase();break;case"MEDIA-SEQUENCE":u=a.startSN=parseInt(r);break;case"SKIP":{const t=new Zt(r);Ci(a,t,["RECENTLY-REMOVED-DATERANGES"]);const e=t.decimalInteger("SKIPPED-SEGMENTS");if(Ut(e)){a.skippedSegments=e;for(let t=e;t--;)o.unshift(null);u+=e}const i=t.enumeratedString("RECENTLY-REMOVED-DATERANGES");i&&(a.recentlyRemovedDateranges=i.split("\t"));break}case"TARGETDURATION":a.targetduration=Math.max(parseInt(r),1);break;case"VERSION":a.version=parseInt(r);break;case"INDEPENDENT-SEGMENTS":case"EXTM3U":break;case"ENDLIST":a.live=!1;break;case"#":(r||g)&&v.tagList.push(g?[r,g]:[r]);break;case"DISCONTINUITY":m++,v.tagList.push(["DIS"]);break;case"GAP":v.gap=!0,v.tagList.push([t]);break;case"BITRATE":v.tagList.push([t,r]);break;case"DATERANGE":{const t=new Zt(r);Ci(a,t,["ID","CLASS","START-DATE","END-DATE","SCTE35-CMD","SCTE35-OUT","SCTE35-IN"]),Ci(a,t,t.clientAttrs);const e=new ie(t,a.dateRanges[t.ID]);e.isValid||a.skippedSegments?a.dateRanges[e.id]=e:Xt.warn(`Ignoring invalid DATERANGE tag: "${r}"`),v.tagList.push(["EXT-X-DATERANGE",r]);break}case"DEFINE":{const t=new Zt(r);Ci(a,t,["NAME","VALUE","IMPORT","QUERYPARAM"]),"IMPORT"in t?xi(a,t,n):Oi(a,t,e)}break;case"DISCONTINUITY-SEQUENCE":m=parseInt(r);break;case"KEY":{const t=es(r,e,a);if(t.isSupported()){if("NONE"===t.method){c=void 0;break}c||(c={}),c[t.keyFormat]&&(c=Nt({},c)),c[t.keyFormat]=t}else Xt.warn(`[Keys] Ignoring invalid EXT-X-KEY tag: "${r}"`);break}case"START":a.startTimeOffset=is(r);break;case"MAP":{const t=new Zt(r);if(Ci(a,t,["BYTERANGE","URI"]),v.duration){const r=new ae(s,e);os(r,t,i,c),d=r,v.initSegment=d,d.rawProgramDateTime&&!v.rawProgramDateTime&&(v.rawProgramDateTime=d.rawProgramDateTime)}else{const e=v.byteRangeEndOffset;if(e){const t=v.byteRangeStartOffset;T=`${e-t}@${t}`}else T=null;os(v,t,i,c),d=v,E=!0}break}case"SERVER-CONTROL":{const t=new Zt(r);a.canBlockReload=t.bool("CAN-BLOCK-RELOAD"),a.canSkipUntil=t.optionalFloat("CAN-SKIP-UNTIL",0),a.canSkipDateRanges=a.canSkipUntil>0&&t.bool("CAN-SKIP-DATERANGES"),a.partHoldBack=t.optionalFloat("PART-HOLD-BACK",0),a.holdBack=t.optionalFloat("HOLD-BACK",0);break}case"PART-INF":{const t=new Zt(r);a.partTarget=t.decimalFloatingPoint("PART-TARGET");break}case"PART":{let t=a.partList;t||(t=a.partList=[]);const i=f>0?t[t.length-1]:void 0,s=f++,n=new Zt(r);Ci(a,n,["BYTERANGE","URI"]);const o=new oe(n,v,e,s,i);t.push(o),v.duration+=o.duration;break}case"PRELOAD-HINT":{const t=new Zt(r);Ci(a,t,["URI"]),a.preloadHint=t;break}case"RENDITION-REPORT":{const t=new Zt(r);Ci(a,t,["URI"]),a.renditionReports=a.renditionReports||[],a.renditionReports.push(t);break}default:Xt.warn("line parsed but not handled: "+l);break}}}p&&!p.relurl?(o.pop(),g-=p.duration,a.partList&&(a.fragmentHint=p)):a.partList&&(as(v,p),v.cc=m,a.fragmentHint=v,c&&ls(v,c,a));const S=o.length,b=o[0],A=o[S-1];if(g+=a.skippedSegments*a.targetduration,g>0&&S&&A){a.averagetargetduration=g/S;const t=A.sn;a.endSN="initSegment"!==t?t:0,a.live||(A.endList=!0),b&&(a.startCC=b.cc)}else a.endSN=0,a.startCC=0;return a.fragmentHint&&(g+=a.fragmentHint.duration),a.totalduration=g,a.endCC=m,y>0&&ns(o,y),a}}function es(t,e,i){var s,r;const n=new Zt(t);Ci(i,n,["KEYFORMAT","KEYFORMATVERSIONS","URI","IV","URI"]);const a=null!=(s=n.METHOD)?s:"",o=n.URI,l=n.hexadecimalInteger("IV"),h=n.KEYFORMATVERSIONS,c=null!=(r=n.KEYFORMAT)?r:"identity";o&&n.IV&&!l&&Xt.error("Invalid IV: "+n.IV);const d=o?ts.resolve(o,e):"",u=(h||"1").split("/").map(Number).filter(Number.isFinite);return new Di(a,d,c,u,l)}function is(t){const e=new Zt(t),i=e.decimalFloatingPoint("TIME-OFFSET");return Ut(i)?i:null}function ss(t,e){let i=(t||"").split(/[ ,]+/).filter(t=>t);["video","audio","text"].forEach(t=>{const s=i.filter(e=>Ni(e,t));s.length&&(e[t+"Codec"]=s.join(","),i=i.filter(t=>-1===s.indexOf(t)))}),e.unknownCodecs=i}function rs(t,e,i){const s=e[i];s&&(t[i]=s)}function ns(t,e){let i=t[e];for(let s=e;s--;){const e=t[s];if(!e)return;e.programDateTime=i.programDateTime-1e3*e.duration,i=e}}function as(t,e){t.rawProgramDateTime?t.programDateTime=Date.parse(t.rawProgramDateTime):null!=e&&e.programDateTime&&(t.programDateTime=e.endProgramDateTime),Ut(t.programDateTime)||(t.programDateTime=null,t.rawProgramDateTime=null)}function os(t,e,i,s){t.relurl=e.URI,e.BYTERANGE&&t.setByteRange(e.BYTERANGE),t.level=i,t.sn="initSegment",s&&(t.levelkeys=s),t.initSegment=null}function ls(t,e,i){t.levelkeys=e;const{encryptedFragments:s}=i;s.length&&s[s.length-1].levelkeys===e||!Object.keys(e).some(t=>e[t].isCommonEncryption)||s.push(t)}var hs={MANIFEST:"manifest",LEVEL:"level",AUDIO_TRACK:"audioTrack",SUBTITLE_TRACK:"subtitleTrack"},cs={MAIN:"main",AUDIO:"audio",SUBTITLE:"subtitle"};function ds(t){const{type:e}=t;switch(e){case hs.AUDIO_TRACK:return cs.AUDIO;case hs.SUBTITLE_TRACK:return cs.SUBTITLE;default:return cs.MAIN}}function us(t,e){let i=t.url;return void 0!==i&&0!==i.indexOf("data:")||(i=e.url),i}class fs{constructor(t){this.hls=void 0,this.loaders=Object.create(null),this.variableList=null,this.hls=t,this.registerListeners()}startLoad(t){}stopLoad(){this.destroyInternalLoaders()}registerListeners(){const{hls:t}=this;t.on(Gt.MANIFEST_LOADING,this.onManifestLoading,this),t.on(Gt.LEVEL_LOADING,this.onLevelLoading,this),t.on(Gt.AUDIO_TRACK_LOADING,this.onAudioTrackLoading,this),t.on(Gt.SUBTITLE_TRACK_LOADING,this.onSubtitleTrackLoading,this)}unregisterListeners(){const{hls:t}=this;t.off(Gt.MANIFEST_LOADING,this.onManifestLoading,this),t.off(Gt.LEVEL_LOADING,this.onLevelLoading,this),t.off(Gt.AUDIO_TRACK_LOADING,this.onAudioTrackLoading,this),t.off(Gt.SUBTITLE_TRACK_LOADING,this.onSubtitleTrackLoading,this)}createInternalLoader(t){const e=this.hls.config,i=e.pLoader,s=e.loader,r=i||s,n=new r(e);return this.loaders[t.type]=n,n}getInternalLoader(t){return this.loaders[t.type]}resetInternalLoader(t){this.loaders[t]&&delete this.loaders[t]}destroyInternalLoaders(){for(const t in this.loaders){const e=this.loaders[t];e&&e.destroy(),this.resetInternalLoader(t)}}destroy(){this.variableList=null,this.unregisterListeners(),this.destroyInternalLoaders()}onManifestLoading(t,e){const{url:i}=e;this.variableList=null,this.load({id:null,level:0,responseType:"text",type:hs.MANIFEST,url:i,deliveryDirectives:null})}onLevelLoading(t,e){const{id:i,level:s,pathwayId:r,url:n,deliveryDirectives:a}=e;this.load({id:i,level:s,pathwayId:r,responseType:"text",type:hs.LEVEL,url:n,deliveryDirectives:a})}onAudioTrackLoading(t,e){const{id:i,groupId:s,url:r,deliveryDirectives:n}=e;this.load({id:i,groupId:s,level:null,responseType:"text",type:hs.AUDIO_TRACK,url:r,deliveryDirectives:n})}onSubtitleTrackLoading(t,e){const{id:i,groupId:s,url:r,deliveryDirectives:n}=e;this.load({id:i,groupId:s,level:null,responseType:"text",type:hs.SUBTITLE_TRACK,url:r,deliveryDirectives:n})}load(t){var e;const i=this.hls.config;let s,r=this.getInternalLoader(t);if(r){const e=r.context;if(e&&e.url===t.url&&e.level===t.level)return void Xt.trace("[playlist-loader]: playlist request ongoing");Xt.log("[playlist-loader]: aborting previous loader for type: "+t.type),r.abort()}if(s=t.type===hs.MANIFEST?i.manifestLoadPolicy.default:Nt({},i.playlistLoadPolicy.default,{timeoutRetry:null,errorRetry:null}),r=this.createInternalLoader(t),Ut(null==(e=t.deliveryDirectives)?void 0:e.part)){let e;if(t.type===hs.LEVEL&&null!==t.level?e=this.hls.levels[t.level].details:t.type===hs.AUDIO_TRACK&&null!==t.id?e=this.hls.audioTracks[t.id].details:t.type===hs.SUBTITLE_TRACK&&null!==t.id&&(e=this.hls.subtitleTracks[t.id].details),e){const t=e.partTarget,i=e.targetduration;if(t&&i){const e=1e3*Math.max(3*t,.8*i);s=Nt({},s,{maxTimeToFirstByteMs:Math.min(e,s.maxTimeToFirstByteMs),maxLoadTimeMs:Math.min(e,s.maxTimeToFirstByteMs)})}}}const n=s.errorRetry||s.timeoutRetry||{},a={loadPolicy:s,timeout:s.maxLoadTimeMs,maxRetry:n.maxNumRetry||0,retryDelay:n.retryDelayMs||0,maxRetryDelay:n.maxRetryDelayMs||0},o={onSuccess:(t,e,i,s)=>{const r=this.getInternalLoader(i);this.resetInternalLoader(i.type);const n=t.data;0===n.indexOf("#EXTM3U")?(e.parsing.start=performance.now(),ts.isMediaPlaylist(n)?this.handleTrackOrLevelPlaylist(t,e,i,s||null,r):this.handleMasterPlaylist(t,e,i,s)):this.handleManifestParsingError(t,i,new Error("no EXTM3U delimiter"),s||null,e)},onError:(t,e,i,s)=>{this.handleNetworkError(e,i,!1,t,s)},onTimeout:(t,e,i)=>{this.handleNetworkError(e,i,!0,void 0,t)}};r.load(t,a,o)}handleMasterPlaylist(t,e,i,s){const r=this.hls,n=t.data,a=us(t,i),o=ts.parseMasterPlaylist(n,a);if(o.playlistParsingError)return void this.handleManifestParsingError(t,i,o.playlistParsingError,s,e);const{contentSteering:l,levels:h,sessionData:c,sessionKeys:d,startTimeOffset:u,variableList:f}=o;this.variableList=f;const{AUDIO:g=[],SUBTITLES:m,"CLOSED-CAPTIONS":p}=ts.parseMasterPlaylistMedia(n,a,o);if(g.length){const t=g.some(t=>!t.url);t||!h[0].audioCodec||h[0].attrs.AUDIO||(Xt.log("[playlist-loader]: audio codec signaled in quality level, but no embedded audio track signaled, create one"),g.unshift({type:"main",name:"main",groupId:"main",default:!1,autoselect:!1,forced:!1,id:-1,attrs:new Zt({}),bitrate:0,url:""}))}r.trigger(Gt.MANIFEST_LOADED,{levels:h,audioTracks:g,subtitles:m,captions:p,contentSteering:l,url:a,stats:e,networkDetails:s,sessionData:c,sessionKeys:d,startTimeOffset:u,variableList:f})}handleTrackOrLevelPlaylist(t,e,i,s,r){const n=this.hls,{id:a,level:o,type:l}=i,h=us(t,i),c=0,d=Ut(o)?o:Ut(a)?a:0,u=ds(i),f=ts.parseLevelPlaylist(t.data,h,d,u,c,this.variableList);if(l===hs.MANIFEST){const t={attrs:new Zt({}),bitrate:0,details:f,name:"",url:h};n.trigger(Gt.MANIFEST_LOADED,{levels:[t],audioTracks:[],url:h,stats:e,networkDetails:s,sessionData:null,sessionKeys:null,contentSteering:null,startTimeOffset:null,variableList:null})}e.parsing.end=performance.now(),i.levelDetails=f,this.handlePlaylistLoaded(f,t,e,i,s,r)}handleManifestParsingError(t,e,i,s,r){this.hls.trigger(Gt.ERROR,{type:jt.NETWORK_ERROR,details:Kt.MANIFEST_PARSING_ERROR,fatal:e.type===hs.MANIFEST,url:t.url,err:i,error:i,reason:i.message,response:t,context:e,networkDetails:s,stats:r})}handleNetworkError(t,e,i=!1,s,r){let n=`A network ${i?"timeout":"error"+(s?" (status "+s.code+")":"")} occurred while loading ${t.type}`;t.type===hs.LEVEL?n+=`: ${t.level} id: ${t.id}`:t.type!==hs.AUDIO_TRACK&&t.type!==hs.SUBTITLE_TRACK||(n+=` id: ${t.id} group-id: "${t.groupId}"`);const a=new Error(n);Xt.warn("[playlist-loader]: "+n);let o=Kt.UNKNOWN,l=!1;const h=this.getInternalLoader(t);switch(t.type){case hs.MANIFEST:o=i?Kt.MANIFEST_LOAD_TIMEOUT:Kt.MANIFEST_LOAD_ERROR,l=!0;break;case hs.LEVEL:o=i?Kt.LEVEL_LOAD_TIMEOUT:Kt.LEVEL_LOAD_ERROR,l=!1;break;case hs.AUDIO_TRACK:o=i?Kt.AUDIO_TRACK_LOAD_TIMEOUT:Kt.AUDIO_TRACK_LOAD_ERROR,l=!1;break;case hs.SUBTITLE_TRACK:o=i?Kt.SUBTITLE_TRACK_LOAD_TIMEOUT:Kt.SUBTITLE_LOAD_ERROR,l=!1;break}h&&this.resetInternalLoader(t.type);const c={type:jt.NETWORK_ERROR,details:o,fatal:l,url:t.url,loader:h,context:t,error:a,networkDetails:e,stats:r};if(s){const i=(null==e?void 0:e.url)||t.url;c.response=xt({url:i,data:void 0},s)}this.hls.trigger(Gt.ERROR,c)}handlePlaylistLoaded(t,e,i,s,r,n){const a=this.hls,{type:o,level:l,id:h,groupId:c,deliveryDirectives:d}=s,u=us(e,s),f=ds(s),g="number"===typeof s.level&&f===cs.MAIN?l:void 0;if(!t.fragments.length){const t=new Error("No Segments found in Playlist");return void a.trigger(Gt.ERROR,{type:jt.NETWORK_ERROR,details:Kt.LEVEL_EMPTY_ERROR,fatal:!1,url:u,error:t,reason:t.message,response:e,context:s,level:g,parent:f,networkDetails:r,stats:i})}t.targetduration||(t.playlistParsingError=new Error("Missing Target Duration"));const m=t.playlistParsingError;if(m)a.trigger(Gt.ERROR,{type:jt.NETWORK_ERROR,details:Kt.LEVEL_PARSING_ERROR,fatal:!1,url:u,error:m,reason:m.message,response:e,context:s,level:g,parent:f,networkDetails:r,stats:i});else switch(t.live&&n&&(n.getCacheAge&&(t.ageHeader=n.getCacheAge()||0),n.getCacheAge&&!isNaN(t.ageHeader)||(t.ageHeader=0)),o){case hs.MANIFEST:case hs.LEVEL:a.trigger(Gt.LEVEL_LOADED,{details:t,level:g||0,id:h||0,stats:i,networkDetails:r,deliveryDirectives:d});break;case hs.AUDIO_TRACK:a.trigger(Gt.AUDIO_TRACK_LOADED,{details:t,id:h||0,groupId:c||"",stats:i,networkDetails:r,deliveryDirectives:d});break;case hs.SUBTITLE_TRACK:a.trigger(Gt.SUBTITLE_TRACK_LOADED,{details:t,id:h||0,groupId:c||"",stats:i,networkDetails:r,deliveryDirectives:d});break}}}function gs(t,e){let i;try{i=new Event("addtrack")}catch(s){i=document.createEvent("Event"),i.initEvent("addtrack",!1,!1)}i.track=t,e.dispatchEvent(i)}function ms(t,e){const i=t.mode;if("disabled"===i&&(t.mode="hidden"),t.cues&&!t.cues.getCueById(e.id))try{if(t.addCue(e),!t.cues.getCueById(e.id))throw new Error("addCue is failed for: "+e)}catch(s){Xt.debug("[texttrack-utils]: "+s);try{const i=new self.TextTrackCue(e.startTime,e.endTime,e.text);i.id=e.id,t.addCue(i)}catch(r){Xt.debug("[texttrack-utils]: Legacy TextTrackCue fallback failed: "+r)}}"disabled"===i&&(t.mode=i)}function ps(t){const e=t.mode;if("disabled"===e&&(t.mode="hidden"),t.cues)for(let i=t.cues.length;i--;)t.removeCue(t.cues[i]);"disabled"===e&&(t.mode=e)}function vs(t,e,i,s){const r=t.mode;if("disabled"===r&&(t.mode="hidden"),t.cues&&t.cues.length>0){const r=Es(t.cues,e,i);for(let e=0;e<r.length;e++)s&&!s(r[e])||t.removeCue(r[e])}"disabled"===r&&(t.mode=r)}function ys(t,e){if(e<t[0].startTime)return 0;const i=t.length-1;if(e>t[i].endTime)return-1;let s=0,r=i;while(s<=r){const n=Math.floor((r+s)/2);if(e<t[n].startTime)r=n-1;else{if(!(e>t[n].startTime&&s<i))return n;s=n+1}}return t[s].startTime-e<e-t[r].startTime?s:r}function Es(t,e,i){const s=[],r=ys(t,e);if(r>-1)for(let n=r,a=t.length;n<a;n++){const r=t[n];if(r.startTime>=e&&r.endTime<=i)s.push(r);else if(r.startTime>i)return s}return s}function Ts(t){const e=[];for(let i=0;i<t.length;i++){const s=t[i];"subtitles"!==s.kind&&"captions"!==s.kind||!s.label||e.push(t[i])}return e}var Ss={audioId3:"org.id3",dateRange:"com.apple.quicktime.HLS",emsg:"https://aomedia.org/emsg/ID3"};const bs=.25;function As(){if("undefined"!==typeof self)return self.VTTCue||self.TextTrackCue}function Ls(t,e,i,s,r){let n=new t(e,i,"");try{n.value=s,r&&(n.type=r)}catch(a){n=new t(e,i,JSON.stringify(r?xt({type:r},s):s))}return n}const Rs=(()=>{const t=As();try{t&&new t(0,Number.POSITIVE_INFINITY,"")}catch(e){return Number.MAX_VALUE}return Number.POSITIVE_INFINITY})();function Ds(t,e){return t.getTime()/1e3-e}function Is(t){return Uint8Array.from(t.replace(/^0x/,"").replace(/([\da-fA-F]{2}) ?/g,"0x$1 ").replace(/ +$/,"").split(" ")).buffer}class ks{constructor(t){this.hls=void 0,this.id3Track=null,this.media=null,this.dateRangeCuesAppended={},this.hls=t,this._registerListeners()}destroy(){this._unregisterListeners(),this.id3Track=null,this.media=null,this.dateRangeCuesAppended={},this.hls=null}_registerListeners(){const{hls:t}=this;t.on(Gt.MEDIA_ATTACHED,this.onMediaAttached,this),t.on(Gt.MEDIA_DETACHING,this.onMediaDetaching,this),t.on(Gt.MANIFEST_LOADING,this.onManifestLoading,this),t.on(Gt.FRAG_PARSING_METADATA,this.onFragParsingMetadata,this),t.on(Gt.BUFFER_FLUSHING,this.onBufferFlushing,this),t.on(Gt.LEVEL_UPDATED,this.onLevelUpdated,this)}_unregisterListeners(){const{hls:t}=this;t.off(Gt.MEDIA_ATTACHED,this.onMediaAttached,this),t.off(Gt.MEDIA_DETACHING,this.onMediaDetaching,this),t.off(Gt.MANIFEST_LOADING,this.onManifestLoading,this),t.off(Gt.FRAG_PARSING_METADATA,this.onFragParsingMetadata,this),t.off(Gt.BUFFER_FLUSHING,this.onBufferFlushing,this),t.off(Gt.LEVEL_UPDATED,this.onLevelUpdated,this)}onMediaAttached(t,e){this.media=e.media}onMediaDetaching(){this.id3Track&&(ps(this.id3Track),this.id3Track=null,this.media=null,this.dateRangeCuesAppended={})}onManifestLoading(){this.dateRangeCuesAppended={}}createTrack(t){const e=this.getID3Track(t.textTracks);return e.mode="hidden",e}getID3Track(t){if(this.media){for(let e=0;e<t.length;e++){const i=t[e];if("metadata"===i.kind&&"id3"===i.label)return gs(i,this.media),i}return this.media.addTextTrack("metadata","id3")}}onFragParsingMetadata(t,e){if(!this.media)return;const{hls:{config:{enableEmsgMetadataCues:i,enableID3MetadataCues:s}}}=this;if(!i&&!s)return;const{samples:r}=e;this.id3Track||(this.id3Track=this.createTrack(this.media));const n=As();if(n)for(let a=0;a<r.length;a++){const t=r[a].type;if(t===Ss.emsg&&!i||!s)continue;const e=Me(r[a].data);if(e){const i=r[a].pts;let s=i+r[a].duration;s>Rs&&(s=Rs);const o=s-i;o<=0&&(s=i+bs);for(let r=0;r<e.length;r++){const a=e[r];if(!xe(a)){this.updateId3CueEnds(i,t);const e=Ls(n,i,s,a,t);e&&this.id3Track.addCue(e)}}}}}updateId3CueEnds(t,e){var i;const s=null==(i=this.id3Track)?void 0:i.cues;if(s)for(let r=s.length;r--;){const i=s[r];i.type===e&&i.startTime<t&&i.endTime===Rs&&(i.endTime=t)}}onBufferFlushing(t,{startOffset:e,endOffset:i,type:s}){const{id3Track:r,hls:n}=this;if(!n)return;const{config:{enableEmsgMetadataCues:a,enableID3MetadataCues:o}}=n;if(r&&(a||o)){let t;t="audio"===s?t=>t.type===Ss.audioId3&&o:"video"===s?t=>t.type===Ss.emsg&&a:t=>t.type===Ss.audioId3&&o||t.type===Ss.emsg&&a,vs(r,e,i,t)}}onLevelUpdated(t,{details:e}){if(!this.media||!e.hasProgramDateTime||!this.hls.config.enableDateRangeMetadataCues)return;const{dateRangeCuesAppended:i,id3Track:s}=this,{dateRanges:r}=e,n=Object.keys(r);if(s){const t=Object.keys(i).filter(t=>!n.includes(t));for(let e=t.length;e--;){const r=t[e];Object.keys(i[r].cues).forEach(t=>{s.removeCue(i[r].cues[t])}),delete i[r]}}const a=e.fragments[e.fragments.length-1];if(0===n.length||!Ut(null==a?void 0:a.programDateTime))return;this.id3Track||(this.id3Track=this.createTrack(this.media));const o=a.programDateTime/1e3-a.start,l=As();for(let h=0;h<n.length;h++){const t=n[h],e=r[t],s=Ds(e.startDate,o),a=i[t],c=(null==a?void 0:a.cues)||{};let d=(null==a?void 0:a.durationKnown)||!1,u=Rs;const f=e.endDate;if(f)u=Ds(f,o),d=!0;else if(e.endOnNext&&!d){const t=n.reduce((t,i)=>{if(i!==e.id){const s=r[i];if(s.class===e.class&&s.startDate>e.startDate&&(!t||e.startDate<t.startDate))return s}return t},null);t&&(u=Ds(t.startDate,o),d=!0)}const g=Object.keys(e.attr);for(let i=0;i<g.length;i++){const r=g[i];if(!te(r))continue;const n=c[r];if(n)d&&!a.durationKnown&&(n.endTime=u);else if(l){let i=e.attr[r];ee(r)&&(i=Is(i));const n=Ls(l,s,u,{key:r,data:i},Ss.dateRange);n&&(n.id=t,this.id3Track.addCue(n),c[r]=n)}}i[t]={cues:c,dateRange:e,durationKnown:d}}}}class ws{constructor(t){this.hls=void 0,this.config=void 0,this.media=null,this.levelDetails=null,this.currentTime=0,this.stallCount=0,this._latency=null,this.timeupdateHandler=()=>this.timeupdate(),this.hls=t,this.config=t.config,this.registerListeners()}get latency(){return this._latency||0}get maxLatency(){const{config:t,levelDetails:e}=this;return void 0!==t.liveMaxLatencyDuration?t.liveMaxLatencyDuration:e?t.liveMaxLatencyDurationCount*e.targetduration:0}get targetLatency(){const{levelDetails:t}=this;if(null===t)return null;const{holdBack:e,partHoldBack:i,targetduration:s}=t,{liveSyncDuration:r,liveSyncDurationCount:n,lowLatencyMode:a}=this.config,o=this.hls.userConfig;let l=a&&i||e;(o.liveSyncDuration||o.liveSyncDurationCount||0===l)&&(l=void 0!==r?r:n*s);const h=s,c=1;return l+Math.min(this.stallCount*c,h)}get liveSyncPosition(){const t=this.estimateLiveEdge(),e=this.targetLatency,i=this.levelDetails;if(null===t||null===e||null===i)return null;const s=i.edge,r=t-e-this.edgeStalled,n=s-i.totalduration,a=s-(this.config.lowLatencyMode&&i.partTarget||i.targetduration);return Math.min(Math.max(n,r),a)}get drift(){const{levelDetails:t}=this;return null===t?1:t.drift}get edgeStalled(){const{levelDetails:t}=this;if(null===t)return 0;const e=3*(this.config.lowLatencyMode&&t.partTarget||t.targetduration);return Math.max(t.age-e,0)}get forwardBufferLength(){const{media:t,levelDetails:e}=this;if(!t||!e)return 0;const i=t.buffered.length;return(i?t.buffered.end(i-1):e.edge)-this.currentTime}destroy(){this.unregisterListeners(),this.onMediaDetaching(),this.levelDetails=null,this.hls=this.timeupdateHandler=null}registerListeners(){this.hls.on(Gt.MEDIA_ATTACHED,this.onMediaAttached,this),this.hls.on(Gt.MEDIA_DETACHING,this.onMediaDetaching,this),this.hls.on(Gt.MANIFEST_LOADING,this.onManifestLoading,this),this.hls.on(Gt.LEVEL_UPDATED,this.onLevelUpdated,this),this.hls.on(Gt.ERROR,this.onError,this)}unregisterListeners(){this.hls.off(Gt.MEDIA_ATTACHED,this.onMediaAttached,this),this.hls.off(Gt.MEDIA_DETACHING,this.onMediaDetaching,this),this.hls.off(Gt.MANIFEST_LOADING,this.onManifestLoading,this),this.hls.off(Gt.LEVEL_UPDATED,this.onLevelUpdated,this),this.hls.off(Gt.ERROR,this.onError,this)}onMediaAttached(t,e){this.media=e.media,this.media.addEventListener("timeupdate",this.timeupdateHandler)}onMediaDetaching(){this.media&&(this.media.removeEventListener("timeupdate",this.timeupdateHandler),this.media=null)}onManifestLoading(){this.levelDetails=null,this._latency=null,this.stallCount=0}onLevelUpdated(t,{details:e}){this.levelDetails=e,e.advanced&&this.timeupdate(),!e.live&&this.media&&this.media.removeEventListener("timeupdate",this.timeupdateHandler)}onError(t,e){var i;e.details===Kt.BUFFER_STALLED_ERROR&&(this.stallCount++,null!=(i=this.levelDetails)&&i.live&&Xt.warn("[playback-rate-controller]: Stall detected, adjusting target latency"))}timeupdate(){const{media:t,levelDetails:e}=this;if(!t||!e)return;this.currentTime=t.currentTime;const i=this.computeLatency();if(null===i)return;this._latency=i;const{lowLatencyMode:s,maxLiveSyncPlaybackRate:r}=this.config;if(!s||1===r||!e.live)return;const n=this.targetLatency;if(null===n)return;const a=i-n,o=Math.min(this.maxLatency,n+e.targetduration),l=a<o;if(l&&a>.05&&this.forwardBufferLength>1){const e=Math.min(2,Math.max(1,r)),i=Math.round(2/(1+Math.exp(-.75*a-this.edgeStalled))*20)/20;t.playbackRate=Math.min(e,Math.max(1,i))}else 1!==t.playbackRate&&0!==t.playbackRate&&(t.playbackRate=1)}estimateLiveEdge(){const{levelDetails:t}=this;return null===t?null:t.edge+t.age}computeLatency(){const t=this.estimateLiveEdge();return null===t?null:t-this.currentTime}}const Cs=["NONE","TYPE-0","TYPE-1",null];function _s(t){return Cs.indexOf(t)>-1}const Os=["SDR","PQ","HLG"];function xs(t){return!!t&&Os.indexOf(t)>-1}var Ps={No:"",Yes:"YES",v2:"v2"};function Ms(t){const{canSkipUntil:e,canSkipDateRanges:i,age:s}=t,r=s<e/2;return e&&r?i?Ps.v2:Ps.Yes:Ps.No}class Fs{constructor(t,e,i){this.msn=void 0,this.part=void 0,this.skip=void 0,this.msn=t,this.part=e,this.skip=i}addDirectives(t){const e=new self.URL(t);return void 0!==this.msn&&e.searchParams.set("_HLS_msn",this.msn.toString()),void 0!==this.part&&e.searchParams.set("_HLS_part",this.part.toString()),this.skip&&e.searchParams.set("_HLS_skip",this.skip),e.href}}class Ns{constructor(t){this._attrs=void 0,this.audioCodec=void 0,this.bitrate=void 0,this.codecSet=void 0,this.url=void 0,this.frameRate=void 0,this.height=void 0,this.id=void 0,this.name=void 0,this.videoCodec=void 0,this.width=void 0,this.details=void 0,this.fragmentError=0,this.loadError=0,this.loaded=void 0,this.realBitrate=0,this.supportedPromise=void 0,this.supportedResult=void 0,this._avgBitrate=0,this._audioGroups=void 0,this._subtitleGroups=void 0,this._urlId=0,this.url=[t.url],this._attrs=[t.attrs],this.bitrate=t.bitrate,t.details&&(this.details=t.details),this.id=t.id||0,this.name=t.name,this.width=t.width||0,this.height=t.height||0,this.frameRate=t.attrs.optionalFloat("FRAME-RATE",0),this._avgBitrate=t.attrs.decimalInteger("AVERAGE-BANDWIDTH"),this.audioCodec=t.audioCodec,this.videoCodec=t.videoCodec,this.codecSet=[t.videoCodec,t.audioCodec].filter(t=>!!t).map(t=>t.substring(0,4)).join(","),this.addGroupId("audio",t.attrs.AUDIO),this.addGroupId("text",t.attrs.SUBTITLES)}get maxBitrate(){return Math.max(this.realBitrate,this.bitrate)}get averageBitrate(){return this._avgBitrate||this.realBitrate||this.bitrate}get attrs(){return this._attrs[0]}get codecs(){return this.attrs.CODECS||""}get pathwayId(){return this.attrs["PATHWAY-ID"]||"."}get videoRange(){return this.attrs["VIDEO-RANGE"]||"SDR"}get score(){return this.attrs.optionalFloat("SCORE",0)}get uri(){return this.url[0]||""}hasAudioGroup(t){return Us(this._audioGroups,t)}hasSubtitleGroup(t){return Us(this._subtitleGroups,t)}get audioGroups(){return this._audioGroups}get subtitleGroups(){return this._subtitleGroups}addGroupId(t,e){if(e)if("audio"===t){let t=this._audioGroups;t||(t=this._audioGroups=[]),-1===t.indexOf(e)&&t.push(e)}else if("text"===t){let t=this._subtitleGroups;t||(t=this._subtitleGroups=[]),-1===t.indexOf(e)&&t.push(e)}}get urlId(){return 0}set urlId(t){}get audioGroupIds(){return this.audioGroups?[this.audioGroupId]:void 0}get textGroupIds(){return this.subtitleGroups?[this.textGroupId]:void 0}get audioGroupId(){var t;return null==(t=this.audioGroups)?void 0:t[0]}get textGroupId(){var t;return null==(t=this.subtitleGroups)?void 0:t[0]}addFallback(){}}function Us(t,e){return!(!e||!t)&&-1!==t.indexOf(e)}function Bs(t,e){const i=e.startPTS;if(Ut(i)){let s,r=0;e.sn>t.sn?(r=i-t.start,s=t):(r=t.start-i,s=e),s.duration!==r&&(s.duration=r)}else if(e.sn>t.sn){const i=t.cc===e.cc;i&&t.minEndPTS?e.start=t.start+(t.minEndPTS-t.start):e.start=t.start+t.duration}else e.start=Math.max(t.start-e.duration,0)}function $s(t,e,i,s,r,n){const a=s-i;a<=0&&(Xt.warn("Fragment should have a positive duration",e),s=i+e.duration,n=r+e.duration);let o=i,l=s;const h=e.startPTS,c=e.endPTS;if(Ut(h)){const t=Math.abs(h-i);Ut(e.deltaPTS)?e.deltaPTS=Math.max(t,e.deltaPTS):e.deltaPTS=t,o=Math.max(i,h),i=Math.min(i,h),r=Math.min(r,e.startDTS),l=Math.min(s,c),s=Math.max(s,c),n=Math.max(n,e.endDTS)}const d=i-e.start;0!==e.start&&(e.start=i),e.duration=s-e.start,e.startPTS=i,e.maxStartPTS=o,e.startDTS=r,e.endPTS=s,e.minEndPTS=l,e.endDTS=n;const u=e.sn;if(!t||u<t.startSN||u>t.endSN)return 0;let f;const g=u-t.startSN,m=t.fragments;for(m[g]=e,f=g;f>0;f--)Bs(m[f],m[f-1]);for(f=g;f<m.length-1;f++)Bs(m[f],m[f+1]);return t.fragmentHint&&Bs(m[m.length-1],t.fragmentHint),t.PTSKnown=t.alignedSliding=!0,d}function Gs(t,e){let i=null;const s=t.fragments;for(let l=s.length-1;l>=0;l--){const t=s[l].initSegment;if(t){i=t;break}}t.fragmentHint&&delete t.fragmentHint.endPTS;let r,n=0;if(Vs(t,e,(t,s)=>{t.relurl&&(n=t.cc-s.cc),Ut(t.startPTS)&&Ut(t.endPTS)&&(s.start=s.startPTS=t.startPTS,s.startDTS=t.startDTS,s.maxStartPTS=t.maxStartPTS,s.endPTS=t.endPTS,s.endDTS=t.endDTS,s.minEndPTS=t.minEndPTS,s.duration=t.endPTS-t.startPTS,s.duration&&(r=s),e.PTSKnown=e.alignedSliding=!0),s.elementaryStreams=t.elementaryStreams,s.loader=t.loader,s.stats=t.stats,t.initSegment&&(s.initSegment=t.initSegment,i=t.initSegment)}),i){const t=e.fragmentHint?e.fragments.concat(e.fragmentHint):e.fragments;t.forEach(t=>{var e;!t||t.initSegment&&t.initSegment.relurl!==(null==(e=i)?void 0:e.relurl)||(t.initSegment=i)})}if(e.skippedSegments)if(e.deltaUpdateFailed=e.fragments.some(t=>!t),e.deltaUpdateFailed){Xt.warn("[level-helper] Previous playlist missing segments skipped in delta playlist");for(let t=e.skippedSegments;t--;)e.fragments.shift();e.startSN=e.fragments[0].sn,e.startCC=e.fragments[0].cc}else e.canSkipDateRanges&&(e.dateRanges=js(t.dateRanges,e.dateRanges,e.recentlyRemovedDateranges));const a=e.fragments;if(n){Xt.warn("discontinuity sliding from playlist, take drift into account");for(let t=0;t<a.length;t++)a[t].cc+=n}e.skippedSegments&&(e.startCC=e.fragments[0].cc),Ks(t.partList,e.partList,(t,e)=>{e.elementaryStreams=t.elementaryStreams,e.stats=t.stats}),r?$s(e,r,r.startPTS,r.endPTS,r.startDTS,r.endDTS):Hs(t,e),a.length&&(e.totalduration=e.edge-a[0].start),e.driftStartTime=t.driftStartTime,e.driftStart=t.driftStart;const o=e.advancedDateTime;if(e.advanced&&o){const t=e.edge;e.driftStart||(e.driftStartTime=o,e.driftStart=t),e.driftEndTime=o,e.driftEnd=t}else e.driftEndTime=t.driftEndTime,e.driftEnd=t.driftEnd,e.advancedDateTime=t.advancedDateTime}function js(t,e,i){const s=Nt({},t);return i&&i.forEach(t=>{delete s[t]}),Object.keys(e).forEach(t=>{const i=new ie(e[t].attr,s[t]);i.isValid?s[t]=i:Xt.warn(`Ignoring invalid Playlist Delta Update DATERANGE tag: "${JSON.stringify(e[t].attr)}"`)}),s}function Ks(t,e,i){if(t&&e){let s=0;for(let r=0,n=t.length;r<=n;r++){const n=t[r],a=e[r+s];n&&a&&n.index===a.index&&n.fragment.sn===a.fragment.sn?i(n,a):s--}}}function Vs(t,e,i){const s=e.skippedSegments,r=Math.max(t.startSN,e.startSN)-e.startSN,n=(t.fragmentHint?1:0)+(s?e.endSN:Math.min(t.endSN,e.endSN))-e.startSN,a=e.startSN-t.startSN,o=e.fragmentHint?e.fragments.concat(e.fragmentHint):e.fragments,l=t.fragmentHint?t.fragments.concat(t.fragmentHint):t.fragments;for(let h=r;h<=n;h++){const t=l[a+h];let r=o[h];s&&!r&&h<s&&(r=e.fragments[h]=t),t&&r&&i(t,r)}}function Hs(t,e){const i=e.startSN+e.skippedSegments-t.startSN,s=t.fragments;i<0||i>=s.length||qs(e,s[i].start)}function qs(t,e){if(e){const i=t.fragments;for(let s=t.skippedSegments;s<i.length;s++)i[s].start+=e;t.fragmentHint&&(t.fragmentHint.start+=e)}}function Ys(t,e=1/0){let i=1e3*t.targetduration;if(t.updated){const s=t.fragments,r=4;if(s.length&&i*r>e){const t=1e3*s[s.length-1].duration;t<i&&(i=t)}}else i/=2;return Math.round(i)}function Ws(t,e,i){if(null==t||!t.details)return null;const s=t.details;let r=s.fragments[e-s.startSN];return r||(r=s.fragmentHint,r&&r.sn===e?r:e<s.startSN&&i&&i.sn===e?i:null)}function zs(t,e,i){var s;return null!=t&&t.details?Xs(null==(s=t.details)?void 0:s.partList,e,i):null}function Xs(t,e,i){if(t)for(let s=t.length;s--;){const r=t[s];if(r.index===i&&r.fragment.sn===e)return r}return null}function Qs(t){t.forEach((t,e)=>{const{details:i}=t;null!=i&&i.fragments&&i.fragments.forEach(t=>{t.level=e})})}function Js(t){switch(t.details){case Kt.FRAG_LOAD_TIMEOUT:case Kt.KEY_LOAD_TIMEOUT:case Kt.LEVEL_LOAD_TIMEOUT:case Kt.MANIFEST_LOAD_TIMEOUT:return!0}return!1}function Zs(t,e){const i=Js(e);return t.default[(i?"timeout":"error")+"Retry"]}function tr(t,e){const i="linear"===t.backoff?1:Math.pow(2,e);return Math.min(i*t.retryDelayMs,t.maxRetryDelayMs)}function er(t){return xt(xt({},t),{errorRetry:null,timeoutRetry:null})}function ir(t,e,i,s){if(!t)return!1;const r=null==s?void 0:s.code,n=e<t.maxNumRetry&&(sr(r)||!!i);return t.shouldRetry?t.shouldRetry(t,e,i,s,n):n}function sr(t){return 0===t&&!1===navigator.onLine||!!t&&(t<400||t>499)}const rr={search:function(t,e){let i=0,s=t.length-1,r=null,n=null;while(i<=s){r=(i+s)/2|0,n=t[r];const a=e(n);if(a>0)i=r+1;else{if(!(a<0))return n;s=r-1}}return null}};function nr(t,e,i){if(null===e||!Array.isArray(t)||!t.length||!Ut(e))return null;const s=t[0].programDateTime;if(e<(s||0))return null;const r=t[t.length-1].endProgramDateTime;if(e>=(r||0))return null;i=i||0;for(let n=0;n<t.length;++n){const s=t[n];if(hr(e,i,s))return s}return null}function ar(t,e,i=0,s=0,r=.005){let n=null;if(t){n=e[t.sn-e[0].sn+1]||null;const s=t.endDTS-i;s>0&&s<15e-7&&(i+=15e-7)}else 0===i&&0===e[0].start&&(n=e[0]);if(n&&((!t||t.level===n.level)&&0===lr(i,s,n)||or(n,t,Math.min(r,s))))return n;const a=rr.search(e,lr.bind(null,i,s));return!a||a===t&&n?n:a}function or(t,e,i){if(e&&0===e.start&&e.level<t.level&&(e.endPTS||0)>0){const s=e.tagList.reduce((t,e)=>("INF"===e[0]&&(t+=parseFloat(e[1])),t),i);return t.start<=s}return!1}function lr(t=0,e=0,i){if(i.start<=t&&i.start+i.duration>t)return 0;const s=Math.min(e,i.duration+(i.deltaPTS?i.deltaPTS:0));return i.start+i.duration-s<=t?1:i.start-s>t&&i.start?-1:0}function hr(t,e,i){const s=1e3*Math.min(e,i.duration+(i.deltaPTS?i.deltaPTS:0)),r=i.endProgramDateTime||0;return r-s>t}function cr(t,e){return rr.search(t,t=>t.cc<e?1:t.cc>e?-1:0)}var dr={DoNothing:0,SendEndCallback:1,SendAlternateToPenaltyBox:2,RemoveAlternatePermanently:3,InsertDiscontinuity:4,RetryRequest:5},ur={None:0,MoveAllAlternatesMatchingHost:1,MoveAllAlternatesMatchingHDCP:2,SwitchToSDR:4};class fr{constructor(t){this.hls=void 0,this.playlistError=0,this.penalizedRenditions={},this.log=void 0,this.warn=void 0,this.error=void 0,this.hls=t,this.log=Xt.log.bind(Xt,"[info]:"),this.warn=Xt.warn.bind(Xt,"[warning]:"),this.error=Xt.error.bind(Xt,"[error]:"),this.registerListeners()}registerListeners(){const t=this.hls;t.on(Gt.ERROR,this.onError,this),t.on(Gt.MANIFEST_LOADING,this.onManifestLoading,this),t.on(Gt.LEVEL_UPDATED,this.onLevelUpdated,this)}unregisterListeners(){const t=this.hls;t&&(t.off(Gt.ERROR,this.onError,this),t.off(Gt.ERROR,this.onErrorOut,this),t.off(Gt.MANIFEST_LOADING,this.onManifestLoading,this),t.off(Gt.LEVEL_UPDATED,this.onLevelUpdated,this))}destroy(){this.unregisterListeners(),this.hls=null,this.penalizedRenditions={}}startLoad(t){}stopLoad(){this.playlistError=0}getVariantLevelIndex(t){return(null==t?void 0:t.type)===cs.MAIN?t.level:this.hls.loadLevel}onManifestLoading(){this.playlistError=0,this.penalizedRenditions={}}onLevelUpdated(){this.playlistError=0}onError(t,e){var i,s;if(e.fatal)return;const r=this.hls,n=e.context;switch(e.details){case Kt.FRAG_LOAD_ERROR:case Kt.FRAG_LOAD_TIMEOUT:case Kt.KEY_LOAD_ERROR:case Kt.KEY_LOAD_TIMEOUT:return void(e.errorAction=this.getFragRetryOrSwitchAction(e));case Kt.FRAG_PARSING_ERROR:if(null!=(i=e.frag)&&i.gap)return void(e.errorAction={action:dr.DoNothing,flags:ur.None});case Kt.FRAG_GAP:case Kt.FRAG_DECRYPT_ERROR:return e.errorAction=this.getFragRetryOrSwitchAction(e),void(e.errorAction.action=dr.SendAlternateToPenaltyBox);case Kt.LEVEL_EMPTY_ERROR:case Kt.LEVEL_PARSING_ERROR:{var a,o;const t=e.parent===cs.MAIN?e.level:r.loadLevel;e.details===Kt.LEVEL_EMPTY_ERROR&&null!=(a=e.context)&&null!=(o=a.levelDetails)&&o.live?e.errorAction=this.getPlaylistRetryOrSwitchAction(e,t):(e.levelRetry=!1,e.errorAction=this.getLevelSwitchAction(e,t))}return;case Kt.LEVEL_LOAD_ERROR:case Kt.LEVEL_LOAD_TIMEOUT:return void("number"===typeof(null==n?void 0:n.level)&&(e.errorAction=this.getPlaylistRetryOrSwitchAction(e,n.level)));case Kt.AUDIO_TRACK_LOAD_ERROR:case Kt.AUDIO_TRACK_LOAD_TIMEOUT:case Kt.SUBTITLE_LOAD_ERROR:case Kt.SUBTITLE_TRACK_LOAD_TIMEOUT:if(n){const t=r.levels[r.loadLevel];if(t&&(n.type===hs.AUDIO_TRACK&&t.hasAudioGroup(n.groupId)||n.type===hs.SUBTITLE_TRACK&&t.hasSubtitleGroup(n.groupId)))return e.errorAction=this.getPlaylistRetryOrSwitchAction(e,r.loadLevel),e.errorAction.action=dr.SendAlternateToPenaltyBox,void(e.errorAction.flags=ur.MoveAllAlternatesMatchingHost)}return;case Kt.KEY_SYSTEM_STATUS_OUTPUT_RESTRICTED:{const t=r.levels[r.loadLevel],i=null==t?void 0:t.attrs["HDCP-LEVEL"];i?e.errorAction={action:dr.SendAlternateToPenaltyBox,flags:ur.MoveAllAlternatesMatchingHDCP,hdcpLevel:i}:this.keySystemError(e)}return;case Kt.BUFFER_ADD_CODEC_ERROR:case Kt.REMUX_ALLOC_ERROR:case Kt.BUFFER_APPEND_ERROR:return void(e.errorAction=this.getLevelSwitchAction(e,null!=(s=e.level)?s:r.loadLevel));case Kt.INTERNAL_EXCEPTION:case Kt.BUFFER_APPENDING_ERROR:case Kt.BUFFER_FULL_ERROR:case Kt.LEVEL_SWITCH_ERROR:case Kt.BUFFER_STALLED_ERROR:case Kt.BUFFER_SEEK_OVER_HOLE:case Kt.BUFFER_NUDGE_ON_STALL:return void(e.errorAction={action:dr.DoNothing,flags:ur.None})}e.type===jt.KEY_SYSTEM_ERROR&&this.keySystemError(e)}keySystemError(t){const e=this.getVariantLevelIndex(t.frag);t.levelRetry=!1,t.errorAction=this.getLevelSwitchAction(t,e)}getPlaylistRetryOrSwitchAction(t,e){const i=this.hls,s=Zs(i.config.playlistLoadPolicy,t),r=this.playlistError++,n=ir(s,r,Js(t),t.response);if(n)return{action:dr.RetryRequest,flags:ur.None,retryConfig:s,retryCount:r};const a=this.getLevelSwitchAction(t,e);return s&&(a.retryConfig=s,a.retryCount=r),a}getFragRetryOrSwitchAction(t){const e=this.hls,i=this.getVariantLevelIndex(t.frag),s=e.levels[i],{fragLoadPolicy:r,keyLoadPolicy:n}=e.config,a=Zs(t.details.startsWith("key")?n:r,t),o=e.levels.reduce((t,e)=>t+e.fragmentError,0);if(s){t.details!==Kt.FRAG_GAP&&s.fragmentError++;const e=ir(a,o,Js(t),t.response);if(e)return{action:dr.RetryRequest,flags:ur.None,retryConfig:a,retryCount:o}}const l=this.getLevelSwitchAction(t,i);return a&&(l.retryConfig=a,l.retryCount=o),l}getLevelSwitchAction(t,e){const i=this.hls;null!==e&&void 0!==e||(e=i.loadLevel);const s=this.hls.levels[e];if(s){var r,n;const e=t.details;s.loadError++,e===Kt.BUFFER_APPEND_ERROR&&s.fragmentError++;let l=-1;const{levels:h,loadLevel:c,minAutoLevel:d,maxAutoLevel:u}=i;i.autoLevelEnabled||(i.loadLevel=-1);const f=null==(r=t.frag)?void 0:r.type,g=f===cs.AUDIO&&e===Kt.FRAG_PARSING_ERROR||"audio"===t.sourceBufferName&&(e===Kt.BUFFER_ADD_CODEC_ERROR||e===Kt.BUFFER_APPEND_ERROR),m=g&&h.some(({audioCodec:t})=>s.audioCodec!==t),p="video"===t.sourceBufferName&&(e===Kt.BUFFER_ADD_CODEC_ERROR||e===Kt.BUFFER_APPEND_ERROR),v=p&&h.some(({codecSet:t,audioCodec:e})=>s.codecSet!==t&&s.audioCodec===e),{type:y,groupId:E}=null!=(n=t.context)?n:{};for(let i=h.length;i--;){const r=(i+c)%h.length;if(r!==c&&r>=d&&r<=u&&0===h[r].loadError){var a,o;const i=h[r];if(e===Kt.FRAG_GAP&&f===cs.MAIN&&t.frag){const e=h[r].details;if(e){const i=ar(t.frag,e.fragments,t.frag.start);if(null!=i&&i.gap)continue}}else{if(y===hs.AUDIO_TRACK&&i.hasAudioGroup(E)||y===hs.SUBTITLE_TRACK&&i.hasSubtitleGroup(E))continue;if(f===cs.AUDIO&&null!=(a=s.audioGroups)&&a.some(t=>i.hasAudioGroup(t))||f===cs.SUBTITLE&&null!=(o=s.subtitleGroups)&&o.some(t=>i.hasSubtitleGroup(t))||m&&s.audioCodec===i.audioCodec||!m&&s.audioCodec!==i.audioCodec||v&&s.codecSet===i.codecSet)continue}l=r;break}}if(l>-1&&i.loadLevel!==l)return t.levelRetry=!0,this.playlistError=0,{action:dr.SendAlternateToPenaltyBox,flags:ur.None,nextAutoLevel:l}}return{action:dr.SendAlternateToPenaltyBox,flags:ur.MoveAllAlternatesMatchingHost}}onErrorOut(t,e){var i;switch(null==(i=e.errorAction)?void 0:i.action){case dr.DoNothing:break;case dr.SendAlternateToPenaltyBox:this.sendAlternateToPenaltyBox(e),e.errorAction.resolved||e.details===Kt.FRAG_GAP?/MediaSource readyState: ended/.test(e.error.message)&&(this.warn(`MediaSource ended after "${e.sourceBufferName}" sourceBuffer append error. Attempting to recover from media error.`),this.hls.recoverMediaError()):e.fatal=!0;break;case dr.RetryRequest:break}e.fatal&&this.hls.stopLoad()}sendAlternateToPenaltyBox(t){const e=this.hls,i=t.errorAction;if(!i)return;const{flags:s,hdcpLevel:r,nextAutoLevel:n}=i;switch(s){case ur.None:this.switchLevel(t,n);break;case ur.MoveAllAlternatesMatchingHDCP:r&&(e.maxHdcpLevel=Cs[Cs.indexOf(r)-1],i.resolved=!0),this.warn(`Restricting playback to HDCP-LEVEL of "${e.maxHdcpLevel}" or lower`);break}i.resolved||this.switchLevel(t,n)}switchLevel(t,e){void 0!==e&&t.errorAction&&(this.warn(`switching to level ${e} after ${t.details}`),this.hls.nextAutoLevel=e,t.errorAction.resolved=!0,this.hls.nextLoadLevel=this.hls.nextAutoLevel)}}class gr{constructor(t,e){this.hls=void 0,this.timer=-1,this.requestScheduled=-1,this.canLoad=!1,this.log=void 0,this.warn=void 0,this.log=Xt.log.bind(Xt,e+":"),this.warn=Xt.warn.bind(Xt,e+":"),this.hls=t}destroy(){this.clearTimer(),this.hls=this.log=this.warn=null}clearTimer(){-1!==this.timer&&(self.clearTimeout(this.timer),this.timer=-1)}startLoad(){this.canLoad=!0,this.requestScheduled=-1,this.loadPlaylist()}stopLoad(){this.canLoad=!1,this.clearTimer()}switchParams(t,e,i){const s=null==e?void 0:e.renditionReports;if(s){let n=-1;for(let i=0;i<s.length;i++){const a=s[i];let o;try{o=new self.URL(a.URI,e.url).href}catch(r){Xt.warn("Could not construct new URL for Rendition Report: "+r),o=a.URI||""}if(o===t){n=i;break}o===t.substring(0,o.length)&&(n=i)}if(-1!==n){const t=s[n],r=parseInt(t["LAST-MSN"])||(null==e?void 0:e.lastPartSn);let a=parseInt(t["LAST-PART"])||(null==e?void 0:e.lastPartIndex);if(this.hls.config.lowLatencyMode){const t=Math.min(e.age-e.partTarget,e.targetduration);a>=0&&t>e.partTarget&&(a+=1)}const o=i&&Ms(i);return new Fs(r,a>=0?a:void 0,o)}}}loadPlaylist(t){-1===this.requestScheduled&&(this.requestScheduled=self.performance.now())}shouldLoadPlaylist(t){return this.canLoad&&!!t&&!!t.url&&(!t.details||t.details.live)}shouldReloadPlaylist(t){return-1===this.timer&&-1===this.requestScheduled&&this.shouldLoadPlaylist(t)}playlistLoaded(t,e,i){const{details:s,stats:r}=e,n=self.performance.now(),a=r.loading.first?Math.max(0,n-r.loading.first):0;if(s.advancedDateTime=Date.now()-a,s.live||null!=i&&i.live){if(s.reloaded(i),i&&this.log(`live playlist ${t} ${s.advanced?"REFRESHED "+s.lastPartSn+"-"+s.lastPartIndex:s.updated?"UPDATED":"MISSED"}`),i&&s.fragments.length>0&&Gs(i,s),!this.canLoad||!s.live)return;let a,o=void 0,l=void 0;if(s.canBlockReload&&s.endSN&&s.advanced){const t=this.hls.config.lowLatencyMode,r=s.lastPartSn,n=s.endSN,h=s.lastPartIndex,c=-1!==h,d=r===n,u=t?0:h;c?(o=d?n+1:r,l=d?u:h+1):o=n+1;const f=s.age,g=f+s.ageHeader;let m=Math.min(g-s.partTarget,1.5*s.targetduration);if(m>0){if(i&&m>i.tuneInGoal)this.warn(`CDN Tune-in goal increased from: ${i.tuneInGoal} to: ${m} with playlist age: ${s.age}`),m=0;else{const t=Math.floor(m/s.targetduration);if(o+=t,void 0!==l){const t=Math.round(m%s.targetduration/s.partTarget);l+=t}this.log(`CDN Tune-in age: ${s.ageHeader}s last advanced ${f.toFixed(2)}s goal: ${m} skip sn ${t} to part ${l}`)}s.tuneInGoal=m}if(a=this.getDeliveryDirectives(s,e.deliveryDirectives,o,l),t||!d)return void this.loadPlaylist(a)}else(s.canBlockReload||s.canSkipUntil)&&(a=this.getDeliveryDirectives(s,e.deliveryDirectives,o,l));const h=this.hls.mainForwardBufferInfo,c=h?h.end-h.len:0,d=1e3*(s.edge-c),u=Ys(s,d);s.updated&&n>this.requestScheduled+u&&(this.requestScheduled=r.loading.start),void 0!==o&&s.canBlockReload?this.requestScheduled=r.loading.first+u-(1e3*s.partTarget||1e3):-1===this.requestScheduled||this.requestScheduled+u<n?this.requestScheduled=n:this.requestScheduled-n<=0&&(this.requestScheduled+=u);let f=this.requestScheduled-n;f=Math.max(0,f),this.log(`reload live playlist ${t} in ${Math.round(f)} ms`),this.timer=self.setTimeout(()=>this.loadPlaylist(a),f)}else this.clearTimer()}getDeliveryDirectives(t,e,i,s){let r=Ms(t);return null!=e&&e.skip&&t.deltaUpdateFailed&&(i=e.msn,s=e.part,r=Ps.No),new Fs(i,s,r)}checkRetry(t){const e=t.details,i=Js(t),s=t.errorAction,{action:r,retryCount:n=0,retryConfig:a}=s||{},o=!!s&&!!a&&(r===dr.RetryRequest||!s.resolved&&r===dr.SendAlternateToPenaltyBox);if(o){var l;if(this.requestScheduled=-1,n>=a.maxNumRetry)return!1;if(i&&null!=(l=t.context)&&l.deliveryDirectives)this.warn(`Retrying playlist loading ${n+1}/${a.maxNumRetry} after "${e}" without delivery-directives`),this.loadPlaylist();else{const t=tr(a,n);this.timer=self.setTimeout(()=>this.loadPlaylist(),t),this.warn(`Retrying playlist loading ${n+1}/${a.maxNumRetry} after "${e}" in ${t}ms`)}t.levelRetry=!0,s.resolved=!0}return o}}class mr{constructor(t,e=0,i=0){this.halfLife=void 0,this.alpha_=void 0,this.estimate_=void 0,this.totalWeight_=void 0,this.halfLife=t,this.alpha_=t?Math.exp(Math.log(.5)/t):0,this.estimate_=e,this.totalWeight_=i}sample(t,e){const i=Math.pow(this.alpha_,t);this.estimate_=e*(1-i)+i*this.estimate_,this.totalWeight_+=t}getTotalWeight(){return this.totalWeight_}getEstimate(){if(this.alpha_){const t=1-Math.pow(this.alpha_,this.totalWeight_);if(t)return this.estimate_/t}return this.estimate_}}class pr{constructor(t,e,i,s=100){this.defaultEstimate_=void 0,this.minWeight_=void 0,this.minDelayMs_=void 0,this.slow_=void 0,this.fast_=void 0,this.defaultTTFB_=void 0,this.ttfb_=void 0,this.defaultEstimate_=i,this.minWeight_=.001,this.minDelayMs_=50,this.slow_=new mr(t),this.fast_=new mr(e),this.defaultTTFB_=s,this.ttfb_=new mr(t)}update(t,e){const{slow_:i,fast_:s,ttfb_:r}=this;i.halfLife!==t&&(this.slow_=new mr(t,i.getEstimate(),i.getTotalWeight())),s.halfLife!==e&&(this.fast_=new mr(e,s.getEstimate(),s.getTotalWeight())),r.halfLife!==t&&(this.ttfb_=new mr(t,r.getEstimate(),r.getTotalWeight()))}sample(t,e){t=Math.max(t,this.minDelayMs_);const i=8*e,s=t/1e3,r=i/s;this.fast_.sample(s,r),this.slow_.sample(s,r)}sampleTTFB(t){const e=t/1e3,i=Math.sqrt(2)*Math.exp(-Math.pow(e,2)/2);this.ttfb_.sample(i,Math.max(t,5))}canEstimate(){return this.fast_.getTotalWeight()>=this.minWeight_}getEstimate(){return this.canEstimate()?Math.min(this.fast_.getEstimate(),this.slow_.getEstimate()):this.defaultEstimate_}getEstimateTTFB(){return this.ttfb_.getTotalWeight()>=this.minWeight_?this.ttfb_.getEstimate():this.defaultTTFB_}destroy(){}}const vr={supported:!0,configurations:[],decodingInfoResults:[{supported:!0,powerEfficient:!0,smooth:!0}]},yr={};function Er(t,e,i,s,r,n){const a=t.audioCodec?t.audioGroups:null,o=null==n?void 0:n.audioCodec,l=null==n?void 0:n.channels,h=l?parseInt(l):o?1/0:2;let c=null;if(null!=a&&a.length)try{c=1===a.length&&a[0]?e.groups[a[0]].channels:a.reduce((t,i)=>{if(i){const s=e.groups[i];if(!s)throw new Error(`Audio track group ${i} not found`);Object.keys(s.channels).forEach(e=>{t[e]=(t[e]||0)+s.channels[e]})}return t},{2:0})}catch(d){return!0}return void 0!==t.videoCodec&&(t.width>1920&&t.height>1088||t.height>1920&&t.width>1088||t.frameRate>Math.max(s,30)||"SDR"!==t.videoRange&&t.videoRange!==i||t.bitrate>Math.max(r,8e6))||!!c&&Ut(h)&&Object.keys(c).some(t=>parseInt(t)>h)}function Tr(t,e,i){const s=t.videoCodec,r=t.audioCodec;if(!s||!r||!i)return Promise.resolve(vr);const n={width:t.width,height:t.height,bitrate:Math.ceil(Math.max(.9*t.bitrate,t.averageBitrate)),framerate:t.frameRate||30},a=t.videoRange;"SDR"!==a&&(n.transferFunction=a.toLowerCase());const o=s.split(",").map(t=>({type:"media-source",video:xt(xt({},n),{},{contentType:$i(t,"video")})}));return r&&t.audioGroups&&t.audioGroups.forEach(t=>{var i;t&&(null==(i=e.groups[t])||i.tracks.forEach(e=>{if(e.groupId===t){const t=e.channels||"",i=parseFloat(t);Ut(i)&&i>2&&o.push.apply(o,r.split(",").map(t=>({type:"media-source",audio:{contentType:$i(t,"audio"),channels:""+i}})))}}))}),Promise.all(o.map(t=>{const e=Sr(t);return yr[e]||(yr[e]=i.decodingInfo(t))})).then(t=>({supported:!t.some(t=>!t.supported),configurations:o,decodingInfoResults:t})).catch(t=>({supported:!1,configurations:o,decodingInfoResults:[],error:t}))}function Sr(t){const{audio:e,video:i}=t,s=i||e;if(s){const t=s.contentType.split('"')[1];if(i)return`r${i.height}x${i.width}f${Math.ceil(i.framerate)}${i.transferFunction||"sd"}_${t}_${Math.ceil(i.bitrate/1e5)}`;if(e)return`c${e.channels}${e.spatialRendering?"s":"n"}_${t}`}return""}function br(){if("function"===typeof matchMedia){const t=matchMedia("(dynamic-range: high)"),e=matchMedia("bad query");if(t.media!==e.media)return!0===t.matches}return!1}function Ar(t,e){let i=!1,s=[];return t&&(i="SDR"!==t,s=[t]),e&&(s=e.allowedVideoRanges||Os.slice(0),i=void 0!==e.preferHDR?e.preferHDR:br(),s=i?s.filter(t=>"SDR"!==t):["SDR"]),{preferHDR:i,allowedVideoRanges:s}}function Lr(t,e,i,s,r){const n=Object.keys(t),a=null==s?void 0:s.channels,o=null==s?void 0:s.audioCodec,l=a&&2===parseInt(a);let h=!0,c=!1,d=1/0,u=1/0,f=1/0,g=0,m=[];const{preferHDR:p,allowedVideoRanges:v}=Ar(e,r);for(let S=n.length;S--;){const e=t[n[S]];h=e.channels[2]>0,d=Math.min(d,e.minHeight),u=Math.min(u,e.minFramerate),f=Math.min(f,e.minBitrate);const i=v.filter(t=>e.videoRanges[t]>0);i.length>0&&(c=!0,m=i)}d=Ut(d)?d:0,u=Ut(u)?u:0;const y=Math.max(1080,d),E=Math.max(30,u);f=Ut(f)?f:i,i=Math.max(f,i),c||(e=void 0,m=[]);const T=n.reduce((e,s)=>{const r=t[s];if(s===e)return e;if(r.minBitrate>i)return Rr(s,`min bitrate of ${r.minBitrate} > current estimate of ${i}`),e;if(!r.hasDefaultAudio)return Rr(s,"no renditions with default or auto-select sound found"),e;if(o&&s.indexOf(o.substring(0,4))%5!==0)return Rr(s,`audio codec preference "${o}" not found`),e;if(a&&!l){if(!r.channels[a])return Rr(s,`no renditions with ${a} channel sound found (channels options: ${Object.keys(r.channels)})`),e}else if((!o||l)&&h&&0===r.channels["2"])return Rr(s,"no renditions with stereo sound found"),e;return r.minHeight>y?(Rr(s,`min resolution of ${r.minHeight} > maximum of ${y}`),e):r.minFramerate>E?(Rr(s,`min framerate of ${r.minFramerate} > maximum of ${E}`),e):m.some(t=>r.videoRanges[t]>0)?r.maxScore<g?(Rr(s,`max score of ${r.maxScore} < selected max of ${g}`),e):e&&(ji(s)>=ji(e)||r.fragmentError>t[e].fragmentError)?e:(g=r.maxScore,s):(Rr(s,`no variants with VIDEO-RANGE of ${JSON.stringify(m)} found`),e)},void 0);return{codecSet:T,videoRanges:m,preferHDR:p,minFramerate:u,minBitrate:f}}function Rr(t,e){Xt.log(`[abr] start candidates with "${t}" ignored because ${e}`)}function Dr(t){return t.reduce((t,e)=>{let i=t.groups[e.groupId];i||(i=t.groups[e.groupId]={tracks:[],channels:{2:0},hasDefault:!1,hasAutoSelect:!1}),i.tracks.push(e);const s=e.channels||"2";return i.channels[s]=(i.channels[s]||0)+1,i.hasDefault=i.hasDefault||e.default,i.hasAutoSelect=i.hasAutoSelect||e.autoselect,i.hasDefault&&(t.hasDefaultAudio=!0),i.hasAutoSelect&&(t.hasAutoSelectAudio=!0),t},{hasDefaultAudio:!1,hasAutoSelectAudio:!1,groups:{}})}function Ir(t,e,i,s){return t.slice(i,s+1).reduce((t,i)=>{if(!i.codecSet)return t;const s=i.audioGroups;let r=t[i.codecSet];r||(t[i.codecSet]=r={minBitrate:1/0,minHeight:1/0,minFramerate:1/0,maxScore:0,videoRanges:{SDR:0},channels:{2:0},hasDefaultAudio:!s,fragmentError:0}),r.minBitrate=Math.min(r.minBitrate,i.bitrate);const n=Math.min(i.height,i.width);return r.minHeight=Math.min(r.minHeight,n),r.minFramerate=Math.min(r.minFramerate,i.frameRate),r.maxScore=Math.max(r.maxScore,i.score),r.fragmentError+=i.fragmentError,r.videoRanges[i.videoRange]=(r.videoRanges[i.videoRange]||0)+1,s&&s.forEach(t=>{if(!t)return;const i=e.groups[t];i&&(r.hasDefaultAudio=r.hasDefaultAudio||e.hasDefaultAudio?i.hasDefault:i.hasAutoSelect||!e.hasDefaultAudio&&!e.hasAutoSelectAudio,Object.keys(i.channels).forEach(t=>{r.channels[t]=(r.channels[t]||0)+i.channels[t]}))}),t},{})}function kr(t,e,i){if("attrs"in t){const i=e.indexOf(t);if(-1!==i)return i}for(let s=0;s<e.length;s++){const r=e[s];if(wr(t,r,i))return s}return-1}function wr(t,e,i){const{groupId:s,name:r,lang:n,assocLang:a,characteristics:o,default:l}=t,h=t.forced;return(void 0===s||e.groupId===s)&&(void 0===r||e.name===r)&&(void 0===n||e.lang===n)&&(void 0===n||e.assocLang===a)&&(void 0===l||e.default===l)&&(void 0===h||e.forced===h)&&(void 0===o||Cr(o,e.characteristics))&&(void 0===i||i(t,e))}function Cr(t,e=""){const i=t.split(","),s=e.split(",");return i.length===s.length&&!i.some(t=>-1===s.indexOf(t))}function _r(t,e){const{audioCodec:i,channels:s}=t;return(void 0===i||(e.audioCodec||"").substring(0,4)===i.substring(0,4))&&(void 0===s||s===(e.channels||"2"))}function Or(t,e,i,s,r){const n=e[s],a=e.reduce((t,e,i)=>{const s=e.uri,r=t[s]||(t[s]=[]);return r.push(i),t},{}),o=a[n.uri];o.length>1&&(s=Math.max.apply(Math,o));const l=n.videoRange,h=n.frameRate,c=n.codecSet.substring(0,4),d=xr(e,s,e=>{if(e.videoRange!==l||e.frameRate!==h||e.codecSet.substring(0,4)!==c)return!1;const s=e.audioGroups,n=i.filter(t=>!s||-1!==s.indexOf(t.groupId));return kr(t,n,r)>-1});return d>-1?d:xr(e,s,e=>{const s=e.audioGroups,n=i.filter(t=>!s||-1!==s.indexOf(t.groupId));return kr(t,n,r)>-1})}function xr(t,e,i){for(let s=e;s;s--)if(i(t[s]))return s;for(let s=e+1;s<t.length;s++)if(i(t[s]))return s;return-1}class Pr{constructor(t){this.hls=void 0,this.lastLevelLoadSec=0,this.lastLoadedFragLevel=-1,this.firstSelection=-1,this._nextAutoLevel=-1,this.nextAutoLevelKey="",this.audioTracksByGroup=null,this.codecTiers=null,this.timer=-1,this.fragCurrent=null,this.partCurrent=null,this.bitrateTestDelay=0,this.bwEstimator=void 0,this._abandonRulesCheck=()=>{const{fragCurrent:t,partCurrent:e,hls:i}=this,{autoLevelEnabled:s,media:r}=i;if(!t||!r)return;const n=performance.now(),a=e?e.stats:t.stats,o=e?e.duration:t.duration,l=n-a.loading.start,h=i.minAutoLevel;if(a.aborted||a.loaded&&a.loaded===a.total||t.level<=h)return this.clearTimer(),void(this._nextAutoLevel=-1);if(!s||r.paused||!r.playbackRate||!r.readyState)return;const c=i.mainForwardBufferInfo;if(null===c)return;const d=this.bwEstimator.getEstimateTTFB(),u=Math.abs(r.playbackRate);if(l<=Math.max(d,o/(2*u)*1e3))return;const f=c.len/u,g=a.loading.first?a.loading.first-a.loading.start:-1,m=a.loaded&&g>-1,p=this.getBwEstimate(),v=i.levels,y=v[t.level],E=a.total||Math.max(a.loaded,Math.round(o*y.averageBitrate/8));let T=m?l-g:l;T<1&&m&&(T=Math.min(l,8*a.loaded/p));const S=m?1e3*a.loaded/T:0,b=S?(E-a.loaded)/S:8*E/p+d/1e3;if(b<=f)return;const A=S?8*S:p;let L,R=Number.POSITIVE_INFINITY;for(L=t.level-1;L>h;L--){const t=v[L].maxBitrate;if(R=this.getTimeToLoadFrag(d/1e3,A,o*t,!v[L].details),R<f)break}if(R>=b)return;if(R>10*o)return;i.nextLoadLevel=i.nextAutoLevel=L,m?this.bwEstimator.sample(l-Math.min(d,g),a.loaded):this.bwEstimator.sampleTTFB(l);const D=v[L].maxBitrate;this.getBwEstimate()*this.hls.config.abrBandWidthUpFactor>D&&this.resetEstimator(D),this.clearTimer(),Xt.warn(`[abr] Fragment ${t.sn}${e?" part "+e.index:""} of level ${t.level} is loading too slowly;\n      Time to underbuffer: ${f.toFixed(3)} s\n      Estimated load time for current fragment: ${b.toFixed(3)} s\n      Estimated load time for down switch fragment: ${R.toFixed(3)} s\n      TTFB estimate: ${0|g} ms\n      Current BW estimate: ${Ut(p)?0|p:"Unknown"} bps\n      New BW estimate: ${0|this.getBwEstimate()} bps\n      Switching to level ${L} @ ${0|D} bps`),i.trigger(Gt.FRAG_LOAD_EMERGENCY_ABORTED,{frag:t,part:e,stats:a})},this.hls=t,this.bwEstimator=this.initEstimator(),this.registerListeners()}resetEstimator(t){t&&(Xt.log("setting initial bwe to "+t),this.hls.config.abrEwmaDefaultEstimate=t),this.firstSelection=-1,this.bwEstimator=this.initEstimator()}initEstimator(){const t=this.hls.config;return new pr(t.abrEwmaSlowVoD,t.abrEwmaFastVoD,t.abrEwmaDefaultEstimate)}registerListeners(){const{hls:t}=this;t.on(Gt.MANIFEST_LOADING,this.onManifestLoading,this),t.on(Gt.FRAG_LOADING,this.onFragLoading,this),t.on(Gt.FRAG_LOADED,this.onFragLoaded,this),t.on(Gt.FRAG_BUFFERED,this.onFragBuffered,this),t.on(Gt.LEVEL_SWITCHING,this.onLevelSwitching,this),t.on(Gt.LEVEL_LOADED,this.onLevelLoaded,this),t.on(Gt.LEVELS_UPDATED,this.onLevelsUpdated,this),t.on(Gt.MAX_AUTO_LEVEL_UPDATED,this.onMaxAutoLevelUpdated,this),t.on(Gt.ERROR,this.onError,this)}unregisterListeners(){const{hls:t}=this;t&&(t.off(Gt.MANIFEST_LOADING,this.onManifestLoading,this),t.off(Gt.FRAG_LOADING,this.onFragLoading,this),t.off(Gt.FRAG_LOADED,this.onFragLoaded,this),t.off(Gt.FRAG_BUFFERED,this.onFragBuffered,this),t.off(Gt.LEVEL_SWITCHING,this.onLevelSwitching,this),t.off(Gt.LEVEL_LOADED,this.onLevelLoaded,this),t.off(Gt.LEVELS_UPDATED,this.onLevelsUpdated,this),t.off(Gt.MAX_AUTO_LEVEL_UPDATED,this.onMaxAutoLevelUpdated,this),t.off(Gt.ERROR,this.onError,this))}destroy(){this.unregisterListeners(),this.clearTimer(),this.hls=this._abandonRulesCheck=null,this.fragCurrent=this.partCurrent=null}onManifestLoading(t,e){this.lastLoadedFragLevel=-1,this.firstSelection=-1,this.lastLevelLoadSec=0,this.fragCurrent=this.partCurrent=null,this.onLevelsUpdated(),this.clearTimer()}onLevelsUpdated(){this.lastLoadedFragLevel>-1&&this.fragCurrent&&(this.lastLoadedFragLevel=this.fragCurrent.level),this._nextAutoLevel=-1,this.onMaxAutoLevelUpdated(),this.codecTiers=null,this.audioTracksByGroup=null}onMaxAutoLevelUpdated(){this.firstSelection=-1,this.nextAutoLevelKey=""}onFragLoading(t,e){const i=e.frag;if(!this.ignoreFragment(i)){var s;if(!i.bitrateTest)this.fragCurrent=i,this.partCurrent=null!=(s=e.part)?s:null;this.clearTimer(),this.timer=self.setInterval(this._abandonRulesCheck,100)}}onLevelSwitching(t,e){this.clearTimer()}onError(t,e){if(!e.fatal)switch(e.details){case Kt.BUFFER_ADD_CODEC_ERROR:case Kt.BUFFER_APPEND_ERROR:this.lastLoadedFragLevel=-1,this.firstSelection=-1;break;case Kt.FRAG_LOAD_TIMEOUT:{const t=e.frag,{fragCurrent:i,partCurrent:s}=this;if(t&&i&&t.sn===i.sn&&t.level===i.level){const e=performance.now(),i=s?s.stats:t.stats,r=e-i.loading.start,n=i.loading.first?i.loading.first-i.loading.start:-1,a=i.loaded&&n>-1;if(a){const t=this.bwEstimator.getEstimateTTFB();this.bwEstimator.sample(r-Math.min(t,n),i.loaded)}else this.bwEstimator.sampleTTFB(r)}break}}}getTimeToLoadFrag(t,e,i,s){const r=t+i/e,n=s?this.lastLevelLoadSec:0;return r+n}onLevelLoaded(t,e){const i=this.hls.config,{loading:s}=e.stats,r=s.end-s.start;Ut(r)&&(this.lastLevelLoadSec=r/1e3),e.details.live?this.bwEstimator.update(i.abrEwmaSlowLive,i.abrEwmaFastLive):this.bwEstimator.update(i.abrEwmaSlowVoD,i.abrEwmaFastVoD)}onFragLoaded(t,{frag:e,part:i}){const s=i?i.stats:e.stats;if(e.type===cs.MAIN&&this.bwEstimator.sampleTTFB(s.loading.first-s.loading.start),!this.ignoreFragment(e)){if(this.clearTimer(),e.level===this._nextAutoLevel&&(this._nextAutoLevel=-1),this.firstSelection=-1,this.hls.config.abrMaxWithRealBitrate){const t=i?i.duration:e.duration,r=this.hls.levels[e.level],n=(r.loaded?r.loaded.bytes:0)+s.loaded,a=(r.loaded?r.loaded.duration:0)+t;r.loaded={bytes:n,duration:a},r.realBitrate=Math.round(8*n/a)}if(e.bitrateTest){const t={stats:s,frag:e,part:i,id:e.type};this.onFragBuffered(Gt.FRAG_BUFFERED,t),e.bitrateTest=!1}else this.lastLoadedFragLevel=e.level}}onFragBuffered(t,e){const{frag:i,part:s}=e,r=null!=s&&s.stats.loaded?s.stats:i.stats;if(r.aborted)return;if(this.ignoreFragment(i))return;const n=r.parsing.end-r.loading.start-Math.min(r.loading.first-r.loading.start,this.bwEstimator.getEstimateTTFB());this.bwEstimator.sample(n,r.loaded),r.bwEstimate=this.getBwEstimate(),i.bitrateTest?this.bitrateTestDelay=n/1e3:this.bitrateTestDelay=0}ignoreFragment(t){return t.type!==cs.MAIN||"initSegment"===t.sn}clearTimer(){this.timer>-1&&(self.clearInterval(this.timer),this.timer=-1)}get firstAutoLevel(){const{maxAutoLevel:t,minAutoLevel:e}=this.hls,i=this.getBwEstimate(),s=this.hls.config.maxStarvationDelay,r=this.findBestLevel(i,e,t,0,s,1,1);if(r>-1)return r;const n=this.hls.firstLevel,a=Math.min(Math.max(n,e),t);return Xt.warn(`[abr] Could not find best starting auto level. Defaulting to first in playlist ${n} clamped to ${a}`),a}get forcedAutoLevel(){return this.nextAutoLevelKey?-1:this._nextAutoLevel}get nextAutoLevel(){const t=this.forcedAutoLevel,e=this.bwEstimator,i=e.canEstimate(),s=this.lastLoadedFragLevel>-1;if(-1!==t&&(!i||!s||this.nextAutoLevelKey===this.getAutoLevelKey()))return t;const r=i&&s?this.getNextABRAutoLevel():this.firstAutoLevel;if(-1!==t){const e=this.hls.levels;if(e.length>Math.max(t,r)&&e[t].loadError<=e[r].loadError)return t}return this._nextAutoLevel=r,this.nextAutoLevelKey=this.getAutoLevelKey(),r}getAutoLevelKey(){return`${this.getBwEstimate()}_${this.getStarvationDelay().toFixed(2)}`}getNextABRAutoLevel(){const{fragCurrent:t,partCurrent:e,hls:i}=this,{maxAutoLevel:s,config:r,minAutoLevel:n}=i,a=e?e.duration:t?t.duration:0,o=this.getBwEstimate(),l=this.getStarvationDelay();let h=r.abrBandWidthFactor,c=r.abrBandWidthUpFactor;if(l){const t=this.findBestLevel(o,n,s,l,0,h,c);if(t>=0)return t}let d=a?Math.min(a,r.maxStarvationDelay):r.maxStarvationDelay;if(!l){const t=this.bitrateTestDelay;if(t){const e=a?Math.min(a,r.maxLoadingDelay):r.maxLoadingDelay;d=e-t,Xt.info(`[abr] bitrate test took ${Math.round(1e3*t)}ms, set first fragment max fetchDuration to ${Math.round(1e3*d)} ms`),h=c=1}}const u=this.findBestLevel(o,n,s,l,d,h,c);if(Xt.info(`[abr] ${l?"rebuffering expected":"buffer is empty"}, optimal quality level ${u}`),u>-1)return u;const f=i.levels[n],g=i.levels[i.loadLevel];return(null==f?void 0:f.bitrate)<(null==g?void 0:g.bitrate)?n:i.loadLevel}getStarvationDelay(){const t=this.hls,e=t.media;if(!e)return 1/0;const i=e&&0!==e.playbackRate?Math.abs(e.playbackRate):1,s=t.mainForwardBufferInfo;return(s?s.len:0)/i}getBwEstimate(){return this.bwEstimator.canEstimate()?this.bwEstimator.getEstimate():this.hls.config.abrEwmaDefaultEstimate}findBestLevel(t,e,i,s,r,n,a){var o;const l=s+r,h=this.lastLoadedFragLevel,c=-1===h?this.hls.firstLevel:h,{fragCurrent:d,partCurrent:u}=this,{levels:f,allAudioTracks:g,loadLevel:m,config:p}=this.hls;if(1===f.length)return 0;const v=f[c],y=!(null==v||null==(o=v.details)||!o.live),E=-1===m||-1===h;let T,S="SDR",b=(null==v?void 0:v.frameRate)||0;const{audioPreference:A,videoPreference:L}=p,R=this.audioTracksByGroup||(this.audioTracksByGroup=Dr(g));if(E){if(-1!==this.firstSelection)return this.firstSelection;const s=this.codecTiers||(this.codecTiers=Ir(f,R,e,i)),r=Lr(s,S,t,A,L),{codecSet:n,videoRanges:a,minFramerate:o,minBitrate:l,preferHDR:h}=r;T=n,S=h?a[a.length-1]:a[0],b=o,t=Math.max(t,l),Xt.log("[abr] picked start tier "+JSON.stringify(r))}else T=null==v?void 0:v.codecSet,S=null==v?void 0:v.videoRange;const D=u?u.duration:d?d.duration:0,I=this.bwEstimator.getEstimateTTFB()/1e3,k=[];for(let C=i;C>=e;C--){var w;const e=f[C],o=C>c;if(!e)continue;if(p.useMediaCapabilities&&!e.supportedResult&&!e.supportedPromise){const i=navigator.mediaCapabilities;"function"===typeof(null==i?void 0:i.decodingInfo)&&Er(e,R,S,b,t,A)?(e.supportedPromise=Tr(e,R,i),e.supportedPromise.then(t=>{if(!this.hls)return;e.supportedResult=t;const i=this.hls.levels,s=i.indexOf(e);t.error?Xt.warn(`[abr] MediaCapabilities decodingInfo error: "${t.error}" for level ${s} ${JSON.stringify(t)}`):t.supported||(Xt.warn(`[abr] Unsupported MediaCapabilities decodingInfo result for level ${s} ${JSON.stringify(t)}`),s>-1&&i.length>1&&(Xt.log("[abr] Removing unsupported level "+s),this.hls.removeLevel(s)))})):e.supportedResult=vr}if(T&&e.codecSet!==T||S&&e.videoRange!==S||o&&b>e.frameRate||!o&&b>0&&b<e.frameRate||e.supportedResult&&(null==(w=e.supportedResult.decodingInfoResults)||!w[0].smooth)){k.push(C);continue}const d=e.details,g=(u?null==d?void 0:d.partTarget:null==d?void 0:d.averagetargetduration)||D;let L;L=o?a*t:n*t;const _=D&&s>=2*D&&0===r?f[C].averageBitrate:f[C].maxBitrate,O=this.getTimeToLoadFrag(I,L,_*g,void 0===d),x=L>=_&&(C===h||0===e.loadError&&0===e.fragmentError)&&(O<=I||!Ut(O)||y&&!this.bitrateTestDelay||O<l);if(x){const t=this.forcedAutoLevel;return C===m||-1!==t&&t===m||(k.length&&Xt.trace(`[abr] Skipped level(s) ${k.join(",")} of ${i} max with CODECS and VIDEO-RANGE:"${f[k[0]].codecs}" ${f[k[0]].videoRange}; not compatible with "${v.codecs}" ${S}`),Xt.info(`[abr] switch candidate:${c}->${C} adjustedbw(${Math.round(L)})-bitrate=${Math.round(L-_)} ttfb:${I.toFixed(1)} avgDuration:${g.toFixed(1)} maxFetchDuration:${l.toFixed(1)} fetchDuration:${O.toFixed(1)} firstSelection:${E} codecSet:${T} videoRange:${S} hls.loadLevel:${m}`)),E&&(this.firstSelection=C),C}}return-1}set nextAutoLevel(t){const{maxAutoLevel:e,minAutoLevel:i}=this.hls,s=Math.min(Math.max(t,i),e);this._nextAutoLevel!==s&&(this.nextAutoLevelKey="",this._nextAutoLevel=s)}}class Mr{constructor(){this._boundTick=void 0,this._tickTimer=null,this._tickInterval=null,this._tickCallCount=0,this._boundTick=this.tick.bind(this)}destroy(){this.onHandlerDestroying(),this.onHandlerDestroyed()}onHandlerDestroying(){this.clearNextTick(),this.clearInterval()}onHandlerDestroyed(){}hasInterval(){return!!this._tickInterval}hasNextTick(){return!!this._tickTimer}setInterval(t){return!this._tickInterval&&(this._tickCallCount=0,this._tickInterval=self.setInterval(this._boundTick,t),!0)}clearInterval(){return!!this._tickInterval&&(self.clearInterval(this._tickInterval),this._tickInterval=null,!0)}clearNextTick(){return!!this._tickTimer&&(self.clearTimeout(this._tickTimer),this._tickTimer=null,!0)}tick(){this._tickCallCount++,1===this._tickCallCount&&(this.doTick(),this._tickCallCount>1&&this.tickImmediate(),this._tickCallCount=0)}tickImmediate(){this.clearNextTick(),this._tickTimer=self.setTimeout(this._boundTick,0)}doTick(){}}var Fr={NOT_LOADED:"NOT_LOADED",APPENDING:"APPENDING",PARTIAL:"PARTIAL",OK:"OK"};class Nr{constructor(t){this.activePartLists=Object.create(null),this.endListFragments=Object.create(null),this.fragments=Object.create(null),this.timeRanges=Object.create(null),this.bufferPadding=.2,this.hls=void 0,this.hasGaps=!1,this.hls=t,this._registerListeners()}_registerListeners(){const{hls:t}=this;t.on(Gt.BUFFER_APPENDED,this.onBufferAppended,this),t.on(Gt.FRAG_BUFFERED,this.onFragBuffered,this),t.on(Gt.FRAG_LOADED,this.onFragLoaded,this)}_unregisterListeners(){const{hls:t}=this;t.off(Gt.BUFFER_APPENDED,this.onBufferAppended,this),t.off(Gt.FRAG_BUFFERED,this.onFragBuffered,this),t.off(Gt.FRAG_LOADED,this.onFragLoaded,this)}destroy(){this._unregisterListeners(),this.fragments=this.activePartLists=this.endListFragments=this.timeRanges=null}getAppendedFrag(t,e){const i=this.activePartLists[e];if(i)for(let s=i.length;s--;){const e=i[s];if(!e)break;const r=e.end;if(e.start<=t&&null!==r&&t<=r)return e}return this.getBufferedFrag(t,e)}getBufferedFrag(t,e){const{fragments:i}=this,s=Object.keys(i);for(let r=s.length;r--;){const n=i[s[r]];if((null==n?void 0:n.body.type)===e&&n.buffered){const e=n.body;if(e.start<=t&&t<=e.end)return e}}return null}detectEvictedFragments(t,e,i,s){this.timeRanges&&(this.timeRanges[t]=e);const r=(null==s?void 0:s.fragment.sn)||-1;Object.keys(this.fragments).forEach(s=>{const n=this.fragments[s];if(!n)return;if(r>=n.body.sn)return;if(!n.buffered&&!n.loaded)return void(n.body.type===i&&this.removeFragment(n.body));const a=n.range[t];a&&a.time.some(t=>{const i=!this.isTimeBuffered(t.startPTS,t.endPTS,e);return i&&this.removeFragment(n.body),i})})}detectPartialFragments(t){const e=this.timeRanges,{frag:i,part:s}=t;if(!e||"initSegment"===i.sn)return;const r=Br(i),n=this.fragments[r];if(!n||n.buffered&&i.gap)return;const a=!i.relurl;if(Object.keys(e).forEach(t=>{const r=i.elementaryStreams[t];if(!r)return;const o=e[t],l=a||!0===r.partial;n.range[t]=this.getBufferedTimes(i,s,l,o)}),n.loaded=null,Object.keys(n.range).length){n.buffered=!0;const t=n.body.endList=i.endList||n.body.endList;t&&(this.endListFragments[n.body.type]=n),Ur(n)||this.removeParts(i.sn-1,i.type)}else this.removeFragment(n.body)}removeParts(t,e){const i=this.activePartLists[e];i&&(this.activePartLists[e]=i.filter(e=>e.fragment.sn>=t))}fragBuffered(t,e){const i=Br(t);let s=this.fragments[i];!s&&e&&(s=this.fragments[i]={body:t,appendedPTS:null,loaded:null,buffered:!1,range:Object.create(null)},t.gap&&(this.hasGaps=!0)),s&&(s.loaded=null,s.buffered=!0)}getBufferedTimes(t,e,i,s){const r={time:[],partial:i},n=t.start,a=t.end,o=t.minEndPTS||a,l=t.maxStartPTS||n;for(let h=0;h<s.length;h++){const t=s.start(h)-this.bufferPadding,e=s.end(h)+this.bufferPadding;if(l>=t&&o<=e){r.time.push({startPTS:Math.max(n,s.start(h)),endPTS:Math.min(a,s.end(h))});break}if(n<e&&a>t){const t=Math.max(n,s.start(h)),e=Math.min(a,s.end(h));e>t&&(r.partial=!0,r.time.push({startPTS:t,endPTS:e}))}else if(a<=t)break}return r}getPartialFragment(t){let e,i,s,r=null,n=0;const{bufferPadding:a,fragments:o}=this;return Object.keys(o).forEach(l=>{const h=o[l];h&&Ur(h)&&(i=h.body.start-a,s=h.body.end+a,t>=i&&t<=s&&(e=Math.min(t-i,s-t),n<=e&&(r=h.body,n=e)))}),r}isEndListAppended(t){const e=this.endListFragments[t];return void 0!==e&&(e.buffered||Ur(e))}getState(t){const e=Br(t),i=this.fragments[e];return i?i.buffered?Ur(i)?Fr.PARTIAL:Fr.OK:Fr.APPENDING:Fr.NOT_LOADED}isTimeBuffered(t,e,i){let s,r;for(let n=0;n<i.length;n++){if(s=i.start(n)-this.bufferPadding,r=i.end(n)+this.bufferPadding,t>=s&&e<=r)return!0;if(e<=s)return!1}return!1}onFragLoaded(t,e){const{frag:i,part:s}=e;if("initSegment"===i.sn||i.bitrateTest)return;const r=s?null:e,n=Br(i);this.fragments[n]={body:i,appendedPTS:null,loaded:r,buffered:!1,range:Object.create(null)}}onBufferAppended(t,e){const{frag:i,part:s,timeRanges:r}=e;if("initSegment"===i.sn)return;const n=i.type;if(s){let t=this.activePartLists[n];t||(this.activePartLists[n]=t=[]),t.push(s)}this.timeRanges=r,Object.keys(r).forEach(t=>{const e=r[t];this.detectEvictedFragments(t,e,n,s)})}onFragBuffered(t,e){this.detectPartialFragments(e)}hasFragment(t){const e=Br(t);return!!this.fragments[e]}hasParts(t){var e;return!(null==(e=this.activePartLists[t])||!e.length)}removeFragmentsInRange(t,e,i,s,r){s&&!this.hasGaps||Object.keys(this.fragments).forEach(n=>{const a=this.fragments[n];if(!a)return;const o=a.body;o.type!==i||s&&!o.gap||o.start<e&&o.end>t&&(a.buffered||r)&&this.removeFragment(o)})}removeFragment(t){const e=Br(t);t.stats.loaded=0,t.clearElementaryStreamInfo();const i=this.activePartLists[t.type];if(i){const e=t.sn;this.activePartLists[t.type]=i.filter(t=>t.fragment.sn!==e)}delete this.fragments[e],t.endList&&delete this.endListFragments[t.type]}removeAllFragments(){this.fragments=Object.create(null),this.endListFragments=Object.create(null),this.activePartLists=Object.create(null),this.hasGaps=!1}}function Ur(t){var e,i,s;return t.buffered&&(t.body.gap||(null==(e=t.range.video)?void 0:e.partial)||(null==(i=t.range.audio)?void 0:i.partial)||(null==(s=t.range.audiovideo)?void 0:s.partial))}function Br(t){return`${t.type}_${t.level}_${t.sn}`}const $r={length:0,start:()=>0,end:()=>0};class Gr{static isBuffered(t,e){try{if(t){const i=Gr.getBuffered(t);for(let t=0;t<i.length;t++)if(e>=i.start(t)&&e<=i.end(t))return!0}}catch(i){}return!1}static bufferInfo(t,e,i){try{if(t){const s=Gr.getBuffered(t),r=[];let n;for(n=0;n<s.length;n++)r.push({start:s.start(n),end:s.end(n)});return this.bufferedInfo(r,e,i)}}catch(s){}return{len:0,start:e,end:e,nextStart:void 0}}static bufferedInfo(t,e,i){e=Math.max(0,e),t.sort((function(t,e){const i=t.start-e.start;return i||e.end-t.end}));let s=[];if(i)for(let l=0;l<t.length;l++){const e=s.length;if(e){const r=s[e-1].end;t[l].start-r<i?t[l].end>r&&(s[e-1].end=t[l].end):s.push(t[l])}else s.push(t[l])}else s=t;let r,n=0,a=e,o=e;for(let l=0;l<s.length;l++){const t=s[l].start,h=s[l].end;if(e+i>=t&&e<h)a=t,o=h,n=o-e;else if(e+i<t){r=t;break}}return{len:n,start:a||0,end:o||0,nextStart:r}}static getBuffered(t){try{return t.buffered}catch(e){return Xt.log("failed to get media.buffered",e),$r}}}class jr{constructor(t,e,i,s=0,r=-1,n=!1){this.level=void 0,this.sn=void 0,this.part=void 0,this.id=void 0,this.size=void 0,this.partial=void 0,this.transmuxing=Kr(),this.buffering={audio:Kr(),video:Kr(),audiovideo:Kr()},this.level=t,this.sn=e,this.id=i,this.size=s,this.part=r,this.partial=n}}function Kr(){return{start:0,executeStart:0,executeEnd:0,end:0}}function Vr(t,e){for(let s=0,r=t.length;s<r;s++){var i;if((null==(i=t[s])?void 0:i.cc)===e)return t[s]}return null}function Hr(t,e,i){return!(!e||!(i.endCC>i.startCC||t&&t.cc<i.startCC))}function qr(t,e){const i=t.fragments,s=e.fragments;if(!s.length||!i.length)return void Xt.log("No fragments to align");const r=Vr(i,s[0].cc);if(r&&(!r||r.startPTS))return r;Xt.log("No frag in previous level to align on")}function Yr(t,e){if(t){const i=t.start+e;t.start=t.startPTS=i,t.endPTS=i+t.duration}}function Wr(t,e){const i=e.fragments;for(let s=0,r=i.length;s<r;s++)Yr(i[s],t);e.fragmentHint&&Yr(e.fragmentHint,t),e.alignedSliding=!0}function zr(t,e,i){e&&(Xr(t,i,e),!i.alignedSliding&&e&&Qr(i,e),i.alignedSliding||!e||i.skippedSegments||Hs(e,i))}function Xr(t,e,i){if(Hr(t,i,e)){const t=qr(i,e);t&&Ut(t.start)&&(Xt.log("Adjusting PTS using last level due to CC increase within current level "+e.url),Wr(t.start,e))}}function Qr(t,e){if(!t.hasProgramDateTime||!e.hasProgramDateTime)return;const i=t.fragments,s=e.fragments;if(!i.length||!s.length)return;let r,n;const a=Math.min(e.endCC,t.endCC);e.startCC<a&&t.startCC<a&&(r=Vr(s,a),n=Vr(i,a)),r&&n||(r=s[Math.floor(s.length/2)],n=Vr(i,r.cc)||i[Math.floor(i.length/2)]);const o=r.programDateTime,l=n.programDateTime;if(!o||!l)return;const h=(l-o)/1e3-(n.start-r.start);Wr(h,t)}const Jr=Math.pow(2,17);class Zr{constructor(t){this.config=void 0,this.loader=null,this.partLoadTimeout=-1,this.config=t}destroy(){this.loader&&(this.loader.destroy(),this.loader=null)}abort(){this.loader&&this.loader.abort()}load(t,e){const i=t.url;if(!i)return Promise.reject(new sn({type:jt.NETWORK_ERROR,details:Kt.FRAG_LOAD_ERROR,fatal:!1,frag:t,error:new Error("Fragment does not have a "+(i?"part list":"url")),networkDetails:null}));this.abort();const s=this.config,r=s.fLoader,n=s.loader;return new Promise((a,o)=>{if(this.loader&&this.loader.destroy(),t.gap){if(t.tagList.some(t=>"GAP"===t[0]))return void o(en(t));t.gap=!1}const l=this.loader=t.loader=r?new r(s):new n(s),h=tn(t),c=er(s.fragLoadPolicy.default),d={loadPolicy:c,timeout:c.maxLoadTimeMs,maxRetry:0,retryDelay:0,maxRetryDelay:0,highWaterMark:"initSegment"===t.sn?1/0:Jr};t.stats=l.stats,l.load(h,d,{onSuccess:(e,i,s,r)=>{this.resetLoader(t,l);let n=e.data;s.resetIV&&t.decryptdata&&(t.decryptdata.iv=new Uint8Array(n.slice(0,16)),n=n.slice(16)),a({frag:t,part:null,payload:n,networkDetails:r})},onError:(e,s,r,n)=>{this.resetLoader(t,l),o(new sn({type:jt.NETWORK_ERROR,details:Kt.FRAG_LOAD_ERROR,fatal:!1,frag:t,response:xt({url:i,data:void 0},e),error:new Error(`HTTP Error ${e.code} ${e.text}`),networkDetails:r,stats:n}))},onAbort:(e,i,s)=>{this.resetLoader(t,l),o(new sn({type:jt.NETWORK_ERROR,details:Kt.INTERNAL_ABORTED,fatal:!1,frag:t,error:new Error("Aborted"),networkDetails:s,stats:e}))},onTimeout:(e,i,s)=>{this.resetLoader(t,l),o(new sn({type:jt.NETWORK_ERROR,details:Kt.FRAG_LOAD_TIMEOUT,fatal:!1,frag:t,error:new Error(`Timeout after ${d.timeout}ms`),networkDetails:s,stats:e}))},onProgress:(i,s,r,n)=>{e&&e({frag:t,part:null,payload:r,networkDetails:n})}})})}loadPart(t,e,i){this.abort();const s=this.config,r=s.fLoader,n=s.loader;return new Promise((a,o)=>{if(this.loader&&this.loader.destroy(),t.gap||e.gap)return void o(en(t,e));const l=this.loader=t.loader=r?new r(s):new n(s),h=tn(t,e),c=er(s.fragLoadPolicy.default),d={loadPolicy:c,timeout:c.maxLoadTimeMs,maxRetry:0,retryDelay:0,maxRetryDelay:0,highWaterMark:Jr};e.stats=l.stats,l.load(h,d,{onSuccess:(s,r,n,o)=>{this.resetLoader(t,l),this.updateStatsFromPart(t,e);const h={frag:t,part:e,payload:s.data,networkDetails:o};i(h),a(h)},onError:(i,s,r,n)=>{this.resetLoader(t,l),o(new sn({type:jt.NETWORK_ERROR,details:Kt.FRAG_LOAD_ERROR,fatal:!1,frag:t,part:e,response:xt({url:h.url,data:void 0},i),error:new Error(`HTTP Error ${i.code} ${i.text}`),networkDetails:r,stats:n}))},onAbort:(i,s,r)=>{t.stats.aborted=e.stats.aborted,this.resetLoader(t,l),o(new sn({type:jt.NETWORK_ERROR,details:Kt.INTERNAL_ABORTED,fatal:!1,frag:t,part:e,error:new Error("Aborted"),networkDetails:r,stats:i}))},onTimeout:(i,s,r)=>{this.resetLoader(t,l),o(new sn({type:jt.NETWORK_ERROR,details:Kt.FRAG_LOAD_TIMEOUT,fatal:!1,frag:t,part:e,error:new Error(`Timeout after ${d.timeout}ms`),networkDetails:r,stats:i}))}})})}updateStatsFromPart(t,e){const i=t.stats,s=e.stats,r=s.total;if(i.loaded+=s.loaded,r){const s=Math.round(t.duration/e.duration),n=Math.min(Math.round(i.loaded/r),s),a=s-n,o=a*Math.round(i.loaded/n);i.total=i.loaded+o}else i.total=Math.max(i.loaded,i.total);const n=i.loading,a=s.loading;n.start?n.first+=a.first-a.start:(n.start=a.start,n.first=a.first),n.end=a.end}resetLoader(t,e){t.loader=null,this.loader===e&&(self.clearTimeout(this.partLoadTimeout),this.loader=null),e.destroy()}}function tn(t,e=null){const i=e||t,s={frag:t,part:e,responseType:"arraybuffer",url:i.url,headers:{},rangeStart:0,rangeEnd:0},r=i.byteRangeStartOffset,n=i.byteRangeEndOffset;if(Ut(r)&&Ut(n)){var a;let e=r,i=n;if("initSegment"===t.sn&&"AES-128"===(null==(a=t.decryptdata)?void 0:a.method)){const t=n-r;t%16&&(i=n+(16-t%16)),0!==r&&(s.resetIV=!0,e=r-16)}s.rangeStart=e,s.rangeEnd=i}return s}function en(t,e){const i=new Error(`GAP ${t.gap?"tag":"attribute"} found`),s={type:jt.MEDIA_ERROR,details:Kt.FRAG_GAP,fatal:!1,frag:t,error:i,networkDetails:null};return e&&(s.part=e),(e||t).stats.aborted=!0,new sn(s)}class sn extends Error{constructor(t){super(t.error.message),this.data=void 0,this.data=t}}class rn{constructor(t,e){this.subtle=void 0,this.aesIV=void 0,this.subtle=t,this.aesIV=e}decrypt(t,e){return this.subtle.decrypt({name:"AES-CBC",iv:this.aesIV},e,t)}}class nn{constructor(t,e){this.subtle=void 0,this.key=void 0,this.subtle=t,this.key=e}expandKey(){return this.subtle.importKey("raw",this.key,{name:"AES-CBC"},!1,["encrypt","decrypt"])}}function an(t){const e=t.byteLength,i=e&&new DataView(t.buffer).getUint8(e-1);return i?De(t,0,e-i):t}class on{constructor(){this.rcon=[0,1,2,4,8,16,32,64,128,27,54],this.subMix=[new Uint32Array(256),new Uint32Array(256),new Uint32Array(256),new Uint32Array(256)],this.invSubMix=[new Uint32Array(256),new Uint32Array(256),new Uint32Array(256),new Uint32Array(256)],this.sBox=new Uint32Array(256),this.invSBox=new Uint32Array(256),this.key=new Uint32Array(0),this.ksRows=0,this.keySize=0,this.keySchedule=void 0,this.invKeySchedule=void 0,this.initTable()}uint8ArrayToUint32Array_(t){const e=new DataView(t),i=new Uint32Array(4);for(let s=0;s<4;s++)i[s]=e.getUint32(4*s);return i}initTable(){const t=this.sBox,e=this.invSBox,i=this.subMix,s=i[0],r=i[1],n=i[2],a=i[3],o=this.invSubMix,l=o[0],h=o[1],c=o[2],d=o[3],u=new Uint32Array(256);let f=0,g=0,m=0;for(m=0;m<256;m++)u[m]=m<128?m<<1:m<<1^283;for(m=0;m<256;m++){let i=g^g<<1^g<<2^g<<3^g<<4;i=i>>>8^255&i^99,t[f]=i,e[i]=f;const o=u[f],m=u[o],p=u[m];let v=257*u[i]^16843008*i;s[f]=v<<24|v>>>8,r[f]=v<<16|v>>>16,n[f]=v<<8|v>>>24,a[f]=v,v=16843009*p^65537*m^257*o^16843008*f,l[i]=v<<24|v>>>8,h[i]=v<<16|v>>>16,c[i]=v<<8|v>>>24,d[i]=v,f?(f=o^u[u[u[p^o]]],g^=u[u[g]]):f=g=1}}expandKey(t){const e=this.uint8ArrayToUint32Array_(t);let i=!0,s=0;while(s<e.length&&i)i=e[s]===this.key[s],s++;if(i)return;this.key=e;const r=this.keySize=e.length;if(4!==r&&6!==r&&8!==r)throw new Error("Invalid aes key size="+r);const n=this.ksRows=4*(r+6+1);let a,o;const l=this.keySchedule=new Uint32Array(n),h=this.invKeySchedule=new Uint32Array(n),c=this.sBox,d=this.rcon,u=this.invSubMix,f=u[0],g=u[1],m=u[2],p=u[3];let v,y;for(a=0;a<n;a++)a<r?v=l[a]=e[a]:(y=v,a%r===0?(y=y<<8|y>>>24,y=c[y>>>24]<<24|c[y>>>16&255]<<16|c[y>>>8&255]<<8|c[255&y],y^=d[a/r|0]<<24):r>6&&a%r===4&&(y=c[y>>>24]<<24|c[y>>>16&255]<<16|c[y>>>8&255]<<8|c[255&y]),l[a]=v=(l[a-r]^y)>>>0);for(o=0;o<n;o++)a=n-o,y=3&o?l[a]:l[a-4],h[o]=o<4||a<=4?y:f[c[y>>>24]]^g[c[y>>>16&255]]^m[c[y>>>8&255]]^p[c[255&y]],h[o]=h[o]>>>0}networkToHostOrderSwap(t){return t<<24|(65280&t)<<8|(16711680&t)>>8|t>>>24}decrypt(t,e,i){const s=this.keySize+6,r=this.invKeySchedule,n=this.invSBox,a=this.invSubMix,o=a[0],l=a[1],h=a[2],c=a[3],d=this.uint8ArrayToUint32Array_(i);let u=d[0],f=d[1],g=d[2],m=d[3];const p=new Int32Array(t),v=new Int32Array(p.length);let y,E,T,S,b,A,L,R,D,I,k,w,C,_;const O=this.networkToHostOrderSwap;while(e<p.length){for(D=O(p[e]),I=O(p[e+1]),k=O(p[e+2]),w=O(p[e+3]),b=D^r[0],A=w^r[1],L=k^r[2],R=I^r[3],C=4,_=1;_<s;_++)y=o[b>>>24]^l[A>>16&255]^h[L>>8&255]^c[255&R]^r[C],E=o[A>>>24]^l[L>>16&255]^h[R>>8&255]^c[255&b]^r[C+1],T=o[L>>>24]^l[R>>16&255]^h[b>>8&255]^c[255&A]^r[C+2],S=o[R>>>24]^l[b>>16&255]^h[A>>8&255]^c[255&L]^r[C+3],b=y,A=E,L=T,R=S,C+=4;y=n[b>>>24]<<24^n[A>>16&255]<<16^n[L>>8&255]<<8^n[255&R]^r[C],E=n[A>>>24]<<24^n[L>>16&255]<<16^n[R>>8&255]<<8^n[255&b]^r[C+1],T=n[L>>>24]<<24^n[R>>16&255]<<16^n[b>>8&255]<<8^n[255&A]^r[C+2],S=n[R>>>24]<<24^n[b>>16&255]<<16^n[A>>8&255]<<8^n[255&L]^r[C+3],v[e]=O(y^u),v[e+1]=O(S^f),v[e+2]=O(T^g),v[e+3]=O(E^m),u=D,f=I,g=k,m=w,e+=4}return v.buffer}}const ln=16;class hn{constructor(t,{removePKCS7Padding:e=!0}={}){if(this.logEnabled=!0,this.removePKCS7Padding=void 0,this.subtle=null,this.softwareDecrypter=null,this.key=null,this.fastAesKey=null,this.remainderData=null,this.currentIV=null,this.currentResult=null,this.useSoftware=void 0,this.useSoftware=t.enableSoftwareAES,this.removePKCS7Padding=e,e)try{const t=self.crypto;t&&(this.subtle=t.subtle||t.webkitSubtle)}catch(i){}this.useSoftware=!this.subtle}destroy(){this.subtle=null,this.softwareDecrypter=null,this.key=null,this.fastAesKey=null,this.remainderData=null,this.currentIV=null,this.currentResult=null}isSync(){return this.useSoftware}flush(){const{currentResult:t,remainderData:e}=this;if(!t||e)return this.reset(),null;const i=new Uint8Array(t);return this.reset(),this.removePKCS7Padding?an(i):i}reset(){this.currentResult=null,this.currentIV=null,this.remainderData=null,this.softwareDecrypter&&(this.softwareDecrypter=null)}decrypt(t,e,i){return this.useSoftware?new Promise((s,r)=>{this.softwareDecrypt(new Uint8Array(t),e,i);const n=this.flush();n?s(n.buffer):r(new Error("[softwareDecrypt] Failed to decrypt data"))}):this.webCryptoDecrypt(new Uint8Array(t),e,i)}softwareDecrypt(t,e,i){const{currentIV:s,currentResult:r,remainderData:n}=this;this.logOnce("JS AES decrypt"),n&&(t=mi(n,t),this.remainderData=null);const a=this.getValidChunk(t);if(!a.length)return null;s&&(i=s);let o=this.softwareDecrypter;o||(o=this.softwareDecrypter=new on),o.expandKey(e);const l=r;return this.currentResult=o.decrypt(a.buffer,0,i),this.currentIV=De(a,-16).buffer,l||null}webCryptoDecrypt(t,e,i){if(this.key!==e||!this.fastAesKey){if(!this.subtle)return Promise.resolve(this.onWebCryptoError(t,e,i));this.key=e,this.fastAesKey=new nn(this.subtle,e)}return this.fastAesKey.expandKey().then(e=>{if(!this.subtle)return Promise.reject(new Error("web crypto not initialized"));this.logOnce("WebCrypto AES decrypt");const s=new rn(this.subtle,new Uint8Array(i));return s.decrypt(t.buffer,e)}).catch(s=>(Xt.warn(`[decrypter]: WebCrypto Error, disable WebCrypto API, ${s.name}: ${s.message}`),this.onWebCryptoError(t,e,i)))}onWebCryptoError(t,e,i){this.useSoftware=!0,this.logEnabled=!0,this.softwareDecrypt(t,e,i);const s=this.flush();if(s)return s.buffer;throw new Error("WebCrypto and softwareDecrypt: failed to decrypt data")}getValidChunk(t){let e=t;const i=t.length-t.length%ln;return i!==t.length&&(e=De(t,0,i),this.remainderData=De(t,i)),e}logOnce(t){this.logEnabled&&(Xt.log("[decrypter]: "+t),this.logEnabled=!1)}}const cn={toString:function(t){let e="";const i=t.length;for(let s=0;s<i;s++)e+=`[${t.start(s).toFixed(3)}-${t.end(s).toFixed(3)}]`;return e}},dn={STOPPED:"STOPPED",IDLE:"IDLE",KEY_LOADING:"KEY_LOADING",FRAG_LOADING:"FRAG_LOADING",FRAG_LOADING_WAITING_RETRY:"FRAG_LOADING_WAITING_RETRY",WAITING_TRACK:"WAITING_TRACK",PARSING:"PARSING",PARSED:"PARSED",ENDED:"ENDED",ERROR:"ERROR",WAITING_INIT_PTS:"WAITING_INIT_PTS",WAITING_LEVEL:"WAITING_LEVEL"};class un extends Mr{constructor(t,e,i,s,r){super(),this.hls=void 0,this.fragPrevious=null,this.fragCurrent=null,this.fragmentTracker=void 0,this.transmuxer=null,this._state=dn.STOPPED,this.playlistType=void 0,this.media=null,this.mediaBuffer=null,this.config=void 0,this.bitrateTest=!1,this.lastCurrentTime=0,this.nextLoadPosition=0,this.startPosition=0,this.startTimeOffset=null,this.loadedmetadata=!1,this.retryDate=0,this.levels=null,this.fragmentLoader=void 0,this.keyLoader=void 0,this.levelLastLoaded=null,this.startFragRequested=!1,this.decrypter=void 0,this.initPTS=[],this.onvseeking=null,this.onvended=null,this.logPrefix="",this.log=void 0,this.warn=void 0,this.playlistType=r,this.logPrefix=s,this.log=Xt.log.bind(Xt,s+":"),this.warn=Xt.warn.bind(Xt,s+":"),this.hls=t,this.fragmentLoader=new Zr(t.config),this.keyLoader=i,this.fragmentTracker=e,this.config=t.config,this.decrypter=new hn(t.config),t.on(Gt.MANIFEST_LOADED,this.onManifestLoaded,this)}doTick(){this.onTickEnd()}onTickEnd(){}startLoad(t){}stopLoad(){this.fragmentLoader.abort(),this.keyLoader.abort(this.playlistType);const t=this.fragCurrent;null!=t&&t.loader&&(t.abortRequests(),this.fragmentTracker.removeFragment(t)),this.resetTransmuxer(),this.fragCurrent=null,this.fragPrevious=null,this.clearInterval(),this.clearNextTick(),this.state=dn.STOPPED}_streamEnded(t,e){if(e.live||t.nextStart||!t.end||!this.media)return!1;const i=e.partList;if(null!=i&&i.length){const t=i[i.length-1],e=Gr.isBuffered(this.media,t.start+t.duration/2);return e}const s=e.fragments[e.fragments.length-1].type;return this.fragmentTracker.isEndListAppended(s)}getLevelDetails(){var t;if(this.levels&&null!==this.levelLastLoaded)return null==(t=this.levelLastLoaded)?void 0:t.details}onMediaAttached(t,e){const i=this.media=this.mediaBuffer=e.media;this.onvseeking=this.onMediaSeeking.bind(this),this.onvended=this.onMediaEnded.bind(this),i.addEventListener("seeking",this.onvseeking),i.addEventListener("ended",this.onvended);const s=this.config;this.levels&&s.autoStartLoad&&this.state===dn.STOPPED&&this.startLoad(s.startPosition)}onMediaDetaching(){const t=this.media;null!=t&&t.ended&&(this.log("MSE detaching and video ended, reset startPosition"),this.startPosition=this.lastCurrentTime=0),t&&this.onvseeking&&this.onvended&&(t.removeEventListener("seeking",this.onvseeking),t.removeEventListener("ended",this.onvended),this.onvseeking=this.onvended=null),this.keyLoader&&this.keyLoader.detach(),this.media=this.mediaBuffer=null,this.loadedmetadata=!1,this.fragmentTracker.removeAllFragments(),this.stopLoad()}onMediaSeeking(){const{config:t,fragCurrent:e,media:i,mediaBuffer:s,state:r}=this,n=i?i.currentTime:0,a=Gr.bufferInfo(s||i,n,t.maxBufferHole);if(this.log(`media seeking to ${Ut(n)?n.toFixed(3):n}, state: ${r}`),this.state===dn.ENDED)this.resetLoadingState();else if(e){const i=t.maxFragLookUpTolerance,s=e.start-i,r=e.start+e.duration+i;if(!a.len||r<a.start||s>a.end){const t=n>r;(n<s||t)&&(t&&e.loader&&(this.log("seeking outside of buffer while fragment load in progress, cancel fragment load"),e.abortRequests(),this.resetLoadingState()),this.fragPrevious=null)}}i&&(this.fragmentTracker.removeFragmentsInRange(n,1/0,this.playlistType,!0),this.lastCurrentTime=n),this.loadedmetadata||a.len||(this.nextLoadPosition=this.startPosition=n),this.tickImmediate()}onMediaEnded(){this.startPosition=this.lastCurrentTime=0}onManifestLoaded(t,e){this.startTimeOffset=e.startTimeOffset,this.initPTS=[]}onHandlerDestroying(){this.hls.off(Gt.MANIFEST_LOADED,this.onManifestLoaded,this),this.stopLoad(),super.onHandlerDestroying(),this.hls=null}onHandlerDestroyed(){this.state=dn.STOPPED,this.fragmentLoader&&this.fragmentLoader.destroy(),this.keyLoader&&this.keyLoader.destroy(),this.decrypter&&this.decrypter.destroy(),this.hls=this.log=this.warn=this.decrypter=this.keyLoader=this.fragmentLoader=this.fragmentTracker=null,super.onHandlerDestroyed()}loadFragment(t,e,i){this._loadFragForPlayback(t,e,i)}_loadFragForPlayback(t,e,i){const s=e=>{if(this.fragContextChanged(t))return this.warn(`Fragment ${t.sn}${e.part?" p: "+e.part.index:""} of level ${t.level} was dropped during download.`),void this.fragmentTracker.removeFragment(t);t.stats.chunkCount++,this._handleFragmentLoadProgress(e)};this._doFragLoad(t,e,i,s).then(e=>{if(!e)return;const i=this.state;this.fragContextChanged(t)?(i===dn.FRAG_LOADING||!this.fragCurrent&&i===dn.PARSING)&&(this.fragmentTracker.removeFragment(t),this.state=dn.IDLE):("payload"in e&&(this.log(`Loaded fragment ${t.sn} of level ${t.level}`),this.hls.trigger(Gt.FRAG_LOADED,e)),this._handleFragmentLoadComplete(e))}).catch(e=>{this.state!==dn.STOPPED&&this.state!==dn.ERROR&&(this.warn("Frag error: "+((null==e?void 0:e.message)||e)),this.resetFragmentLoading(t))})}clearTrackerIfNeeded(t){var e;const{fragmentTracker:i}=this,s=i.getState(t);if(s===Fr.APPENDING){const e=t.type,s=this.getFwdBufferInfo(this.mediaBuffer,e),r=Math.max(t.duration,s?s.len:this.config.maxBufferLength),n=this.backtrackFragment,a=n?t.sn-n.sn:0;(1===a||this.reduceMaxBufferLength(r))&&i.removeFragment(t)}else 0===(null==(e=this.mediaBuffer)?void 0:e.buffered.length)?i.removeAllFragments():i.hasParts(t.type)&&(i.detectPartialFragments({frag:t,part:null,stats:t.stats,id:t.type}),i.getState(t)===Fr.PARTIAL&&i.removeFragment(t))}checkLiveUpdate(t){if(t.updated&&!t.live){const e=t.fragments[t.fragments.length-1];this.fragmentTracker.detectPartialFragments({frag:e,part:null,stats:e.stats,id:e.type})}t.fragments[0]||(t.deltaUpdateFailed=!0)}flushMainBuffer(t,e,i=null){if(!(t-e))return;const s={startOffset:t,endOffset:e,type:i};this.hls.trigger(Gt.BUFFER_FLUSHING,s)}_loadInitSegment(t,e){this._doFragLoad(t,e).then(e=>{if(!e||this.fragContextChanged(t)||!this.levels)throw new Error("init load aborted");return e}).then(e=>{const{hls:i}=this,{payload:s}=e,r=t.decryptdata;if(s&&s.byteLength>0&&null!=r&&r.key&&r.iv&&"AES-128"===r.method){const n=self.performance.now();return this.decrypter.decrypt(new Uint8Array(s),r.key.buffer,r.iv.buffer).catch(e=>{throw i.trigger(Gt.ERROR,{type:jt.MEDIA_ERROR,details:Kt.FRAG_DECRYPT_ERROR,fatal:!1,error:e,reason:e.message,frag:t}),e}).then(s=>{const r=self.performance.now();return i.trigger(Gt.FRAG_DECRYPTED,{frag:t,payload:s,stats:{tstart:n,tdecrypt:r}}),e.payload=s,this.completeInitSegmentLoad(e)})}return this.completeInitSegmentLoad(e)}).catch(e=>{this.state!==dn.STOPPED&&this.state!==dn.ERROR&&(this.warn(e),this.resetFragmentLoading(t))})}completeInitSegmentLoad(t){const{levels:e}=this;if(!e)throw new Error("init load aborted, missing levels");const i=t.frag.stats;this.state=dn.IDLE,t.frag.data=new Uint8Array(t.payload),i.parsing.start=i.buffering.start=self.performance.now(),i.parsing.end=i.buffering.end=self.performance.now(),this.tick()}fragContextChanged(t){const{fragCurrent:e}=this;return!t||!e||t.sn!==e.sn||t.level!==e.level}fragBufferedComplete(t,e){var i,s,r,n;const a=this.mediaBuffer?this.mediaBuffer:this.media;if(this.log(`Buffered ${t.type} sn: ${t.sn}${e?" part: "+e.index:""} of ${this.playlistType===cs.MAIN?"level":"track"} ${t.level} (frag:[${(null!=(i=t.startPTS)?i:NaN).toFixed(3)}-${(null!=(s=t.endPTS)?s:NaN).toFixed(3)}] > buffer:${a?cn.toString(Gr.getBuffered(a)):"(detached)"})`),"initSegment"!==t.sn){var o;if(t.type!==cs.SUBTITLE){const e=t.elementaryStreams;if(!Object.keys(e).some(t=>!!e[t]))return void(this.state=dn.IDLE)}const e=null==(o=this.levels)?void 0:o[t.level];null!=e&&e.fragmentError&&(this.log(`Resetting level fragment error count of ${e.fragmentError} on frag buffered`),e.fragmentError=0)}this.state=dn.IDLE,a&&(!this.loadedmetadata&&t.type==cs.MAIN&&a.buffered.length&&(null==(r=this.fragCurrent)?void 0:r.sn)===(null==(n=this.fragPrevious)?void 0:n.sn)&&(this.loadedmetadata=!0,this.seekToStartPos()),this.tick())}seekToStartPos(){}_handleFragmentLoadComplete(t){const{transmuxer:e}=this;if(!e)return;const{frag:i,part:s,partsLoaded:r}=t,n=!r||0===r.length||r.some(t=>!t),a=new jr(i.level,i.sn,i.stats.chunkCount+1,0,s?s.index:-1,!n);e.flush(a)}_handleFragmentLoadProgress(t){}_doFragLoad(t,e,i=null,s){var r;const n=null==e?void 0:e.details;if(!this.levels||!n)throw new Error(`frag load aborted, missing level${n?"":" detail"}s`);let a=null;if(!t.encrypted||null!=(r=t.decryptdata)&&r.key?!t.encrypted&&n.encryptedFragments.length&&this.keyLoader.loadClear(t,n.encryptedFragments):(this.log(`Loading key for ${t.sn} of [${n.startSN}-${n.endSN}], ${"[stream-controller]"===this.logPrefix?"level":"track"} ${t.level}`),this.state=dn.KEY_LOADING,this.fragCurrent=t,a=this.keyLoader.load(t).then(t=>{if(!this.fragContextChanged(t.frag))return this.hls.trigger(Gt.KEY_LOADED,t),this.state===dn.KEY_LOADING&&(this.state=dn.IDLE),t}),this.hls.trigger(Gt.KEY_LOADING,{frag:t}),null===this.fragCurrent&&(a=Promise.reject(new Error("frag load aborted, context changed in KEY_LOADING")))),i=Math.max(t.start,i||0),this.config.lowLatencyMode&&"initSegment"!==t.sn){const r=n.partList;if(r&&s){i>t.end&&n.fragmentHint&&(t=n.fragmentHint);const o=this.getNextPart(r,t,i);if(o>-1){const l=r[o];let h;return this.log(`Loading part sn: ${t.sn} p: ${l.index} cc: ${t.cc} of playlist [${n.startSN}-${n.endSN}] parts [0-${o}-${r.length-1}] ${"[stream-controller]"===this.logPrefix?"level":"track"}: ${t.level}, target: ${parseFloat(i.toFixed(3))}`),this.nextLoadPosition=l.start+l.duration,this.state=dn.FRAG_LOADING,h=a?a.then(i=>!i||this.fragContextChanged(i.frag)?null:this.doFragPartsLoad(t,l,e,s)).catch(t=>this.handleFragLoadError(t)):this.doFragPartsLoad(t,l,e,s).catch(t=>this.handleFragLoadError(t)),this.hls.trigger(Gt.FRAG_LOADING,{frag:t,part:l,targetBufferTime:i}),null===this.fragCurrent?Promise.reject(new Error("frag load aborted, context changed in FRAG_LOADING parts")):h}if(!t.url||this.loadedEndOfParts(r,i))return Promise.resolve(null)}}this.log(`Loading fragment ${t.sn} cc: ${t.cc} ${n?"of ["+n.startSN+"-"+n.endSN+"] ":""}${"[stream-controller]"===this.logPrefix?"level":"track"}: ${t.level}, target: ${parseFloat(i.toFixed(3))}`),Ut(t.sn)&&!this.bitrateTest&&(this.nextLoadPosition=t.start+t.duration),this.state=dn.FRAG_LOADING;const o=this.config.progressive;let l;return l=o&&a?a.then(e=>!e||this.fragContextChanged(null==e?void 0:e.frag)?null:this.fragmentLoader.load(t,s)).catch(t=>this.handleFragLoadError(t)):Promise.all([this.fragmentLoader.load(t,o?s:void 0),a]).then(([t])=>(!o&&t&&s&&s(t),t)).catch(t=>this.handleFragLoadError(t)),this.hls.trigger(Gt.FRAG_LOADING,{frag:t,targetBufferTime:i}),null===this.fragCurrent?Promise.reject(new Error("frag load aborted, context changed in FRAG_LOADING")):l}doFragPartsLoad(t,e,i,s){return new Promise((r,n)=>{var a;const o=[],l=null==(a=i.details)?void 0:a.partList,h=e=>{this.fragmentLoader.loadPart(t,e,s).then(s=>{o[e.index]=s;const n=s.part;this.hls.trigger(Gt.FRAG_LOADED,s);const a=zs(i,t.sn,e.index+1)||Xs(l,t.sn,e.index+1);if(!a)return r({frag:t,part:n,partsLoaded:o});h(a)}).catch(n)};h(e)})}handleFragLoadError(t){if("data"in t){const e=t.data;t.data&&e.details===Kt.INTERNAL_ABORTED?this.handleFragLoadAborted(e.frag,e.part):this.hls.trigger(Gt.ERROR,e)}else this.hls.trigger(Gt.ERROR,{type:jt.OTHER_ERROR,details:Kt.INTERNAL_EXCEPTION,err:t,error:t,fatal:!0});return null}_handleTransmuxerFlush(t){const e=this.getCurrentContext(t);if(!e||this.state!==dn.PARSING)return void(this.fragCurrent||this.state===dn.STOPPED||this.state===dn.ERROR||(this.state=dn.IDLE));const{frag:i,part:s,level:r}=e,n=self.performance.now();i.stats.parsing.end=n,s&&(s.stats.parsing.end=n),this.updateLevelTiming(i,s,r,t.partial)}getCurrentContext(t){const{levels:e,fragCurrent:i}=this,{level:s,sn:r,part:n}=t;if(null==e||!e[s])return this.warn(`Levels object was unset while buffering fragment ${r} of level ${s}. The current chunk will not be buffered.`),null;const a=e[s],o=n>-1?zs(a,r,n):null,l=o?o.fragment:Ws(a,r,i);return l?(i&&i!==l&&(l.stats=i.stats),{frag:l,part:o,level:a}):null}bufferFragmentData(t,e,i,s,r){var n;if(!t||this.state!==dn.PARSING)return;const{data1:a,data2:o}=t;let l=a;if(a&&o&&(l=mi(a,o)),null==(n=l)||!n.length)return;const h={type:t.type,frag:e,part:i,chunkMeta:s,parent:e.type,data:l};if(this.hls.trigger(Gt.BUFFER_APPENDING,h),t.dropped&&t.independent&&!i){if(r)return;this.flushBufferGap(e)}}flushBufferGap(t){const e=this.media;if(!e)return;if(!Gr.isBuffered(e,e.currentTime))return void this.flushMainBuffer(0,t.start);const i=e.currentTime,s=Gr.bufferInfo(e,i,0),r=t.duration,n=Math.min(2*this.config.maxFragLookUpTolerance,.25*r),a=Math.max(Math.min(t.start-n,s.end-n),i+n);t.start-a>n&&this.flushMainBuffer(a,t.start)}getFwdBufferInfo(t,e){const i=this.getLoadPosition();return Ut(i)?this.getFwdBufferInfoAtPos(t,i,e):null}getFwdBufferInfoAtPos(t,e,i){const{config:{maxBufferHole:s}}=this,r=Gr.bufferInfo(t,e,s);if(0===r.len&&void 0!==r.nextStart){const n=this.fragmentTracker.getBufferedFrag(e,i);if(n&&r.nextStart<n.end)return Gr.bufferInfo(t,e,Math.max(r.nextStart,s))}return r}getMaxBufferLength(t){const{config:e}=this;let i;return i=t?Math.max(8*e.maxBufferSize/t,e.maxBufferLength):e.maxBufferLength,Math.min(i,e.maxMaxBufferLength)}reduceMaxBufferLength(t){const e=this.config,i=t||e.maxBufferLength,s=e.maxMaxBufferLength/2;return s>=i&&(e.maxMaxBufferLength=s,this.warn(`Reduce max buffer length to ${s}s`),!0)}getAppendedFrag(t,e=cs.MAIN){const i=this.fragmentTracker.getAppendedFrag(t,cs.MAIN);return i&&"fragment"in i?i.fragment:i}getNextFragment(t,e){const i=e.fragments,s=i.length;if(!s)return null;const{config:r}=this,n=i[0].start;let a;if(e.live){const o=r.initialLiveManifestSize;if(s<o)return this.warn(`Not enough fragments to start playback (have: ${s}, need: ${o})`),null;(!e.PTSKnown&&!this.startFragRequested&&-1===this.startPosition||t<n)&&(a=this.getInitialLiveFragment(e,i),this.startPosition=this.nextLoadPosition=a?this.hls.liveSyncPosition||a.start:t)}else t<=n&&(a=i[0]);if(!a){const i=r.lowLatencyMode?e.partEnd:e.fragmentEnd;a=this.getFragmentAtPosition(t,i,e)}return this.mapToInitFragWhenRequired(a)}isLoopLoading(t,e){const i=this.fragmentTracker.getState(t);return(i===Fr.OK||i===Fr.PARTIAL&&!!t.gap)&&this.nextLoadPosition>e}getNextFragmentLoopLoading(t,e,i,s,r){const n=t.gap,a=this.getNextFragment(this.nextLoadPosition,e);if(null===a)return a;if(t=a,n&&t&&!t.gap&&i.nextStart){const e=this.getFwdBufferInfoAtPos(this.mediaBuffer?this.mediaBuffer:this.media,i.nextStart,s);if(null!==e&&i.len+e.len>=r)return this.log(`buffer full after gaps in "${s}" playlist starting at sn: ${t.sn}`),null}return t}mapToInitFragWhenRequired(t){return null==t||!t.initSegment||null!=t&&t.initSegment.data||this.bitrateTest?t:t.initSegment}getNextPart(t,e,i){let s=-1,r=!1,n=!0;for(let a=0,o=t.length;a<o;a++){const o=t[a];if(n=n&&!o.independent,s>-1&&i<o.start)break;const l=o.loaded;l?s=-1:(r||o.independent||n)&&o.fragment===e&&(s=a),r=l}return s}loadedEndOfParts(t,e){const i=t[t.length-1];return i&&e>i.start&&i.loaded}getInitialLiveFragment(t,e){const i=this.fragPrevious;let s=null;if(i){if(t.hasProgramDateTime&&(this.log("Live playlist, switching playlist, load frag with same PDT: "+i.programDateTime),s=nr(e,i.endProgramDateTime,this.config.maxFragLookUpTolerance)),!s){const r=i.sn+1;if(r>=t.startSN&&r<=t.endSN){const n=e[r-t.startSN];i.cc===n.cc&&(s=n,this.log("Live playlist, switching playlist, load frag with next SN: "+s.sn))}s||(s=cr(e,i.cc),s&&this.log("Live playlist, switching playlist, load frag with same CC: "+s.sn))}}else{const e=this.hls.liveSyncPosition;null!==e&&(s=this.getFragmentAtPosition(e,this.bitrateTest?t.fragmentEnd:t.edge,t))}return s}getFragmentAtPosition(t,e,i){const{config:s}=this;let{fragPrevious:r}=this,{fragments:n,endSN:a}=i;const{fragmentHint:o}=i,{maxFragLookUpTolerance:l}=s,h=i.partList,c=!!(s.lowLatencyMode&&null!=h&&h.length&&o);let d;if(c&&o&&!this.bitrateTest&&(n=n.concat(o),a=o.sn),t<e){const i=t>e-l?0:l;d=ar(r,n,t,i)}else d=n[n.length-1];if(d){const t=d.sn-i.startSN,e=this.fragmentTracker.getState(d);if((e===Fr.OK||e===Fr.PARTIAL&&d.gap)&&(r=d),r&&d.sn===r.sn&&(!c||h[0].fragment.sn>d.sn)){const e=r&&d.level===r.level;if(e){const e=n[t+1];d=d.sn<a&&this.fragmentTracker.getState(e)!==Fr.OK?e:null}}}return d}synchronizeToLiveEdge(t){const{config:e,media:i}=this;if(!i)return;const s=this.hls.liveSyncPosition,r=i.currentTime,n=t.fragments[0].start,a=t.edge,o=r>=n-e.maxFragLookUpTolerance&&r<=a;if(null!==s&&i.duration>s&&(r<s||!o)){const n=void 0!==e.liveMaxLatencyDuration?e.liveMaxLatencyDuration:e.liveMaxLatencyDurationCount*t.targetduration;(!o&&i.readyState<4||r<a-n)&&(this.loadedmetadata||(this.nextLoadPosition=s),i.readyState&&(this.warn(`Playback: ${r.toFixed(3)} is located too far from the end of live sliding playlist: ${a}, reset currentTime to : ${s.toFixed(3)}`),i.currentTime=s))}}alignPlaylists(t,e,i){const s=t.fragments.length;if(!s)return this.warn("No fragments in live playlist"),0;const r=t.fragments[0].start,n=!e,a=t.alignedSliding&&Ut(r);if(n||!a&&!r){const{fragPrevious:r}=this;zr(r,i,t);const n=t.fragments[0].start;return this.log(`Live playlist sliding: ${n.toFixed(2)} start-sn: ${e?e.startSN:"na"}->${t.startSN} prev-sn: ${r?r.sn:"na"} fragments: ${s}`),n}return r}waitForCdnTuneIn(t){const e=3;return t.live&&t.canBlockReload&&t.partTarget&&t.tuneInGoal>Math.max(t.partHoldBack,t.partTarget*e)}setStartPosition(t,e){let i=this.startPosition;if(i<e&&(i=-1),-1===i||-1===this.lastCurrentTime){const s=null!==this.startTimeOffset,r=s?this.startTimeOffset:t.startTimeOffset;null!==r&&Ut(r)?(i=e+r,r<0&&(i+=t.totalduration),i=Math.min(Math.max(e,i),e+t.totalduration),this.log(`Start time offset ${r} found in ${s?"multivariant":"media"} playlist, adjust startPosition to ${i}`),this.startPosition=i):t.live?i=this.hls.liveSyncPosition||e:this.startPosition=i=0,this.lastCurrentTime=i}this.nextLoadPosition=i}getLoadPosition(){const{media:t}=this;let e=0;return this.loadedmetadata&&t?e=t.currentTime:this.nextLoadPosition&&(e=this.nextLoadPosition),e}handleFragLoadAborted(t,e){this.transmuxer&&"initSegment"!==t.sn&&t.stats.aborted&&(this.warn(`Fragment ${t.sn}${e?" part "+e.index:""} of level ${t.level} was aborted`),this.resetFragmentLoading(t))}resetFragmentLoading(t){this.fragCurrent&&(this.fragContextChanged(t)||this.state===dn.FRAG_LOADING_WAITING_RETRY)||(this.state=dn.IDLE)}onFragmentOrKeyLoadError(t,e){if(e.chunkMeta&&!e.frag){const t=this.getCurrentContext(e.chunkMeta);t&&(e.frag=t.frag)}const i=e.frag;if(!i||i.type!==t||!this.levels)return;var s;if(this.fragContextChanged(i))return void this.warn(`Frag load error must match current frag to retry ${i.url} > ${null==(s=this.fragCurrent)?void 0:s.url}`);const r=e.details===Kt.FRAG_GAP;r&&this.fragmentTracker.fragBuffered(i,!0);const n=e.errorAction,{action:a,retryCount:o=0,retryConfig:l}=n||{};if(n&&a===dr.RetryRequest&&l){this.resetStartWhenNotLoaded(this.levelLastLoaded);const s=tr(l,o);this.warn(`Fragment ${i.sn} of ${t} ${i.level} errored with ${e.details}, retrying loading ${o+1}/${l.maxNumRetry} in ${s}ms`),n.resolved=!0,this.retryDate=self.performance.now()+s,this.state=dn.FRAG_LOADING_WAITING_RETRY}else if(l&&n){if(this.resetFragmentErrors(t),!(o<l.maxNumRetry))return void Xt.warn(`${e.details} reached or exceeded max retry (${o})`);r||a===dr.RemoveAlternatePermanently||(n.resolved=!0)}else(null==n?void 0:n.action)===dr.SendAlternateToPenaltyBox?this.state=dn.WAITING_LEVEL:this.state=dn.ERROR;this.tickImmediate()}reduceLengthAndFlushBuffer(t){if(this.state===dn.PARSING||this.state===dn.PARSED){const e=t.parent,i=this.getFwdBufferInfo(this.mediaBuffer,e),s=i&&i.len>.5;s&&this.reduceMaxBufferLength(i.len);const r=!s;return r&&this.warn(`Buffer full error while media.currentTime is not buffered, flush ${e} buffer`),t.frag&&(this.fragmentTracker.removeFragment(t.frag),this.nextLoadPosition=t.frag.start),this.resetLoadingState(),r}return!1}resetFragmentErrors(t){t===cs.AUDIO&&(this.fragCurrent=null),this.loadedmetadata||(this.startFragRequested=!1),this.state!==dn.STOPPED&&(this.state=dn.IDLE)}afterBufferFlushed(t,e,i){if(!t)return;const s=Gr.getBuffered(t);this.fragmentTracker.detectEvictedFragments(e,s,i),this.state===dn.ENDED&&this.resetLoadingState()}resetLoadingState(){this.log("Reset loading state"),this.fragCurrent=null,this.fragPrevious=null,this.state=dn.IDLE}resetStartWhenNotLoaded(t){if(!this.loadedmetadata){this.startFragRequested=!1;const e=t?t.details:null;null!=e&&e.live?(this.startPosition=-1,this.setStartPosition(e,0),this.resetLoadingState()):this.nextLoadPosition=this.startPosition}}resetWhenMissingContext(t){this.warn(`The loading context changed while buffering fragment ${t.sn} of level ${t.level}. This chunk will not be buffered.`),this.removeUnbufferedFrags(),this.resetStartWhenNotLoaded(this.levelLastLoaded),this.resetLoadingState()}removeUnbufferedFrags(t=0){this.fragmentTracker.removeFragmentsInRange(t,1/0,this.playlistType,!1,!0)}updateLevelTiming(t,e,i,s){var r;const n=i.details;if(!n)return void this.warn("level.details undefined");const a=Object.keys(t.elementaryStreams).reduce((e,r)=>{const a=t.elementaryStreams[r];if(a){const o=a.endPTS-a.startPTS;if(o<=0)return this.warn(`Could not parse fragment ${t.sn} ${r} duration reliably (${o})`),e||!1;const l=s?0:$s(n,t,a.startPTS,a.endPTS,a.startDTS,a.endDTS);return this.hls.trigger(Gt.LEVEL_PTS_UPDATED,{details:n,level:i,drift:l,type:r,frag:t,start:a.startPTS,end:a.endPTS}),!0}return e},!1);if(!a&&null===(null==(r=this.transmuxer)?void 0:r.error)){const e=new Error(`Found no media in fragment ${t.sn} of level ${t.level} resetting transmuxer to fallback to playlist timing`);if(0===i.fragmentError&&(i.fragmentError++,t.gap=!0,this.fragmentTracker.removeFragment(t),this.fragmentTracker.fragBuffered(t,!0)),this.warn(e.message),this.hls.trigger(Gt.ERROR,{type:jt.MEDIA_ERROR,details:Kt.FRAG_PARSING_ERROR,fatal:!1,error:e,frag:t,reason:`Found no media in msn ${t.sn} of level "${i.url}"`}),!this.hls)return;this.resetTransmuxer()}this.state=dn.PARSED,this.hls.trigger(Gt.FRAG_PARSED,{frag:t,part:e})}resetTransmuxer(){this.transmuxer&&(this.transmuxer.destroy(),this.transmuxer=null)}recoverWorkerError(t){"demuxerWorker"===t.event&&(this.fragmentTracker.removeAllFragments(),this.resetTransmuxer(),this.resetStartWhenNotLoaded(this.levelLastLoaded),this.resetLoadingState())}set state(t){const e=this._state;e!==t&&(this._state=t,this.log(`${e}->${t}`))}get state(){return this._state}}class fn{constructor(){this.chunks=[],this.dataLength=0}push(t){this.chunks.push(t),this.dataLength+=t.length}flush(){const{chunks:t,dataLength:e}=this;let i;return t.length?(i=1===t.length?t[0]:gn(t,e),this.reset(),i):new Uint8Array(0)}reset(){this.chunks.length=0,this.dataLength=0}}function gn(t,e){const i=new Uint8Array(e);let s=0;for(let r=0;r<t.length;r++){const e=t[r];i.set(e,s),s+=e.length}return i}function mn(){return"function"===typeof __HLS_WORKER_BUNDLE__}function pn(){const t=new self.Blob([`var exports={};var module={exports:exports};function define(f){f()};define.amd=true;(${__HLS_WORKER_BUNDLE__.toString()})(true);`],{type:"text/javascript"}),e=self.URL.createObjectURL(t),i=new self.Worker(e);return{worker:i,objectURL:e}}function vn(t){const e=new self.URL(t,self.location.href).href,i=new self.Worker(e);return{worker:i,scriptURL:e}}function yn(t="",e=9e4){return{type:t,id:-1,pid:-1,inputTimeScale:e,sequenceNumber:-1,samples:[],dropped:0}}class En{constructor(){this._audioTrack=void 0,this._id3Track=void 0,this.frameIndex=0,this.cachedData=null,this.basePTS=null,this.initPTS=null,this.lastPTS=null}resetInitSegment(t,e,i,s){this._id3Track={type:"id3",id:3,pid:-1,inputTimeScale:9e4,sequenceNumber:0,samples:[],dropped:0}}resetTimeStamp(t){this.initPTS=t,this.resetContiguity()}resetContiguity(){this.basePTS=null,this.lastPTS=null,this.frameIndex=0}canParse(t,e){return!1}appendFrame(t,e,i){}demux(t,e){this.cachedData&&(t=mi(this.cachedData,t),this.cachedData=null);let i,s=we(t,0),r=s?s.length:0;const n=this._audioTrack,a=this._id3Track,o=s?Oe(s):void 0,l=t.length;(null===this.basePTS||0===this.frameIndex&&Ut(o))&&(this.basePTS=Tn(o,e,this.initPTS),this.lastPTS=this.basePTS),null===this.lastPTS&&(this.lastPTS=this.basePTS),s&&s.length>0&&a.samples.push({pts:this.lastPTS,dts:this.lastPTS,data:s,type:Ss.audioId3,duration:Number.POSITIVE_INFINITY});while(r<l){if(this.canParse(t,r)){const e=this.appendFrame(n,t,r);e?(this.frameIndex++,this.lastPTS=e.sample.pts,r+=e.length,i=r):r=l}else _e(t,r)?(s=we(t,r),a.samples.push({pts:this.lastPTS,dts:this.lastPTS,data:s,type:Ss.audioId3,duration:Number.POSITIVE_INFINITY}),r+=s.length,i=r):r++;if(r===l&&i!==l){const e=De(t,i);this.cachedData?this.cachedData=mi(this.cachedData,e):this.cachedData=e}}return{audioTrack:n,videoTrack:yn(),id3Track:a,textTrack:yn()}}demuxSampleAes(t,e,i){return Promise.reject(new Error(`[${this}] This demuxer does not support Sample-AES decryption`))}flush(t){const e=this.cachedData;return e&&(this.cachedData=null,this.demux(e,0)),{audioTrack:this._audioTrack,videoTrack:yn(),id3Track:this._id3Track,textTrack:yn()}}destroy(){}}const Tn=(t,e,i)=>{if(Ut(t))return 90*t;const s=i?9e4*i.baseTime/i.timescale:0;return 9e4*e+s};function Sn(t,e,i,s){let r,n,a,o;const l=navigator.userAgent.toLowerCase(),h=s,c=[96e3,88200,64e3,48e3,44100,32e3,24e3,22050,16e3,12e3,11025,8e3,7350];r=1+((192&e[i+2])>>>6);const d=(60&e[i+2])>>>2;if(!(d>c.length-1))return a=(1&e[i+2])<<2,a|=(192&e[i+3])>>>6,Xt.log(`manifest codec:${s}, ADTS type:${r}, samplingIndex:${d}`),/firefox/i.test(l)?d>=6?(r=5,o=new Array(4),n=d-3):(r=2,o=new Array(2),n=d):-1!==l.indexOf("android")?(r=2,o=new Array(2),n=d):(r=5,o=new Array(4),s&&(-1!==s.indexOf("mp4a.40.29")||-1!==s.indexOf("mp4a.40.5"))||!s&&d>=6?n=d-3:((s&&-1!==s.indexOf("mp4a.40.2")&&(d>=6&&1===a||/vivaldi/i.test(l))||!s&&1===a)&&(r=2,o=new Array(2)),n=d)),o[0]=r<<3,o[0]|=(14&d)>>1,o[1]|=(1&d)<<7,o[1]|=a<<3,5===r&&(o[1]|=(14&n)>>1,o[2]=(1&n)<<7,o[2]|=8,o[3]=0),{config:o,samplerate:c[d],channelCount:a,codec:"mp4a.40."+r,manifestCodec:h};{const e=new Error("invalid ADTS sampling index:"+d);t.emit(Gt.ERROR,Gt.ERROR,{type:jt.MEDIA_ERROR,details:Kt.FRAG_PARSING_ERROR,fatal:!0,error:e,reason:e.message})}}function bn(t,e){return 255===t[e]&&240===(246&t[e+1])}function An(t,e){return 1&t[e+1]?7:9}function Ln(t,e){return(3&t[e+3])<<11|t[e+4]<<3|(224&t[e+5])>>>5}function Rn(t,e){return e+5<t.length}function Dn(t,e){return e+1<t.length&&bn(t,e)}function In(t,e){return Rn(t,e)&&bn(t,e)&&Ln(t,e)<=t.length-e}function kn(t,e){if(Dn(t,e)){const i=An(t,e);if(e+i>=t.length)return!1;const s=Ln(t,e);if(s<=i)return!1;const r=e+s;return r===t.length||Dn(t,r)}return!1}function wn(t,e,i,s,r){if(!t.samplerate){const n=Sn(e,i,s,r);if(!n)return;t.config=n.config,t.samplerate=n.samplerate,t.channelCount=n.channelCount,t.codec=n.codec,t.manifestCodec=n.manifestCodec,Xt.log(`parsed codec:${t.codec}, rate:${n.samplerate}, channels:${n.channelCount}`)}}function Cn(t){return 9216e4/t}function _n(t,e){const i=An(t,e);if(e+i<=t.length){const s=Ln(t,e)-i;if(s>0)return{headerLength:i,frameLength:s}}}function On(t,e,i,s,r){const n=Cn(t.samplerate),a=s+r*n,o=_n(e,i);let l;if(o){const{frameLength:s,headerLength:r}=o,n=r+s,h=Math.max(0,i+n-e.length);h?(l=new Uint8Array(n-r),l.set(e.subarray(i+r,e.length),0)):l=e.subarray(i+r,i+n);const c={unit:l,pts:a};return h||t.samples.push(c),{sample:c,length:n,missing:h}}const h=e.length-i;l=new Uint8Array(h),l.set(e.subarray(i,e.length),0);const c={unit:l,pts:a};return{sample:c,length:h,missing:-1}}let xn=null;const Pn=[32,64,96,128,160,192,224,256,288,320,352,384,416,448,32,48,56,64,80,96,112,128,160,192,224,256,320,384,32,40,48,56,64,80,96,112,128,160,192,224,256,320,32,48,56,64,80,96,112,128,144,160,176,192,224,256,8,16,24,32,40,48,56,64,80,96,112,128,144,160],Mn=[44100,48e3,32e3,22050,24e3,16e3,11025,12e3,8e3],Fn=[[0,72,144,12],[0,0,0,0],[0,72,144,12],[0,144,144,12]],Nn=[0,1,1,4];function Un(t,e,i,s,r){if(i+24>e.length)return;const n=Bn(e,i);if(n&&i+n.frameLength<=e.length){const a=9e4*n.samplesPerFrame/n.sampleRate,o=s+r*a,l={unit:e.subarray(i,i+n.frameLength),pts:o,dts:o};return t.config=[],t.channelCount=n.channelCount,t.samplerate=n.sampleRate,t.samples.push(l),{sample:l,length:n.frameLength,missing:0}}}function Bn(t,e){const i=t[e+1]>>3&3,s=t[e+1]>>1&3,r=t[e+2]>>4&15,n=t[e+2]>>2&3;if(1!==i&&0!==r&&15!==r&&3!==n){const a=t[e+2]>>1&1,o=t[e+3]>>6,l=3===i?3-s:3===s?3:4,h=1e3*Pn[14*l+r-1],c=3===i?0:2===i?1:2,d=Mn[3*c+n],u=3===o?1:2,f=Fn[i][s],g=Nn[s],m=8*f*g,p=Math.floor(f*h/d+a)*g;if(null===xn){const t=navigator.userAgent||"",e=t.match(/Chrome\/(\d+)/i);xn=e?parseInt(e[1]):0}const v=!!xn&&xn<=87;return v&&2===s&&h>=224e3&&0===o&&(t[e+3]=128|t[e+3]),{sampleRate:d,channelCount:u,frameLength:p,samplesPerFrame:m}}}function $n(t,e){return 255===t[e]&&224===(224&t[e+1])&&0!==(6&t[e+1])}function Gn(t,e){return e+1<t.length&&$n(t,e)}function jn(t,e){const i=4;return $n(t,e)&&i<=t.length-e}function Kn(t,e){if(e+1<t.length&&$n(t,e)){const i=4,s=Bn(t,e);let r=i;null!=s&&s.frameLength&&(r=s.frameLength);const n=e+r;return n===t.length||Gn(t,n)}return!1}class Vn extends En{constructor(t,e){super(),this.observer=void 0,this.config=void 0,this.observer=t,this.config=e}resetInitSegment(t,e,i,s){super.resetInitSegment(t,e,i,s),this._audioTrack={container:"audio/adts",type:"audio",id:2,pid:-1,sequenceNumber:0,segmentCodec:"aac",samples:[],manifestCodec:e,duration:s,inputTimeScale:9e4,dropped:0}}static probe(t){if(!t)return!1;const e=we(t,0);let i=(null==e?void 0:e.length)||0;if(Kn(t,i))return!1;for(let s=t.length;i<s;i++)if(kn(t,i))return Xt.log("ADTS sync word found !"),!0;return!1}canParse(t,e){return In(t,e)}appendFrame(t,e,i){wn(t,this.observer,e,i,t.manifestCodec);const s=On(t,e,i,this.basePTS,this.frameIndex);if(s&&0===s.missing)return s}}const Hn=/\/emsg[-/]ID3/i;class qn{constructor(t,e){this.remainderData=null,this.timeOffset=0,this.config=void 0,this.videoTrack=void 0,this.audioTrack=void 0,this.id3Track=void 0,this.txtTrack=void 0,this.config=e}resetTimeStamp(){}resetInitSegment(t,e,i,s){const r=this.videoTrack=yn("video",1),n=this.audioTrack=yn("audio",1),a=this.txtTrack=yn("text",1);if(this.id3Track=yn("id3",1),this.timeOffset=0,null==t||!t.byteLength)return;const o=si(t);if(o.video){const{id:t,timescale:e,codec:i}=o.video;r.id=t,r.timescale=a.timescale=e,r.codec=i}if(o.audio){const{id:t,timescale:e,codec:i}=o.audio;n.id=t,n.timescale=e,n.codec=i}a.id=Ye.text,r.sampleDuration=0,r.duration=n.duration=s}resetContiguity(){this.remainderData=null}static probe(t){return ti(t)}demux(t,e){this.timeOffset=e;let i=t;const s=this.videoTrack,r=this.txtTrack;if(this.config.progressive){this.remainderData&&(i=mi(this.remainderData,t));const e=gi(i);this.remainderData=e.remainder,s.samples=e.valid||new Uint8Array}else s.samples=i;const n=this.extractID3Track(s,e);return r.samples=pi(e,s),{videoTrack:s,audioTrack:this.audioTrack,id3Track:n,textTrack:this.txtTrack}}flush(){const t=this.timeOffset,e=this.videoTrack,i=this.txtTrack;e.samples=this.remainderData||new Uint8Array,this.remainderData=null;const s=this.extractID3Track(e,this.timeOffset);return i.samples=pi(t,e),{videoTrack:e,audioTrack:yn(),id3Track:s,textTrack:yn()}}extractID3Track(t,e){const i=this.id3Track;if(t.samples.length){const s=ei(t.samples,["emsg"]);s&&s.forEach(t=>{const s=Si(t);if(Hn.test(s.schemeIdUri)){const t=Ut(s.presentationTime)?s.presentationTime/s.timeScale:e+s.presentationTimeDelta/s.timeScale;let r=4294967295===s.eventDuration?Number.POSITIVE_INFINITY:s.eventDuration/s.timeScale;r<=.001&&(r=Number.POSITIVE_INFINITY);const n=s.payload;i.samples.push({data:n,len:n.byteLength,dts:t,pts:t,type:Ss.emsg,duration:r})}})}return i}demuxSampleAes(t,e,i){return Promise.reject(new Error("The MP4 demuxer does not support SAMPLE-AES decryption"))}destroy(){}}const Yn=(t,e)=>{let i=0,s=5;e+=s;const r=new Uint32Array(1),n=new Uint32Array(1),a=new Uint8Array(1);while(s>0){a[0]=t[e];const o=Math.min(s,8),l=8-o;n[0]=4278190080>>>24+l<<l,r[0]=(a[0]&n[0])>>l,i=i?i<<o|r[0]:r[0],e+=1,s-=o}return i};class Wn extends En{constructor(t){super(),this.observer=void 0,this.observer=t}resetInitSegment(t,e,i,s){super.resetInitSegment(t,e,i,s),this._audioTrack={container:"audio/ac-3",type:"audio",id:2,pid:-1,sequenceNumber:0,segmentCodec:"ac3",samples:[],manifestCodec:e,duration:s,inputTimeScale:9e4,dropped:0}}canParse(t,e){return e+64<t.length}appendFrame(t,e,i){const s=zn(t,e,i,this.basePTS,this.frameIndex);if(-1!==s){const e=t.samples[t.samples.length-1];return{sample:e,length:s,missing:0}}}static probe(t){if(!t)return!1;const e=we(t,0);if(!e)return!1;const i=e.length;return 11===t[i]&&119===t[i+1]&&void 0!==Oe(e)&&Yn(t,i)<16}}function zn(t,e,i,s,r){if(i+8>e.length)return-1;if(11!==e[i]||119!==e[i+1])return-1;const n=e[i+4]>>6;if(n>=3)return-1;const a=[48e3,44100,32e3],o=a[n],l=63&e[i+4],h=[64,69,96,64,70,96,80,87,120,80,88,120,96,104,144,96,105,144,112,121,168,112,122,168,128,139,192,128,140,192,160,174,240,160,175,240,192,208,288,192,209,288,224,243,336,224,244,336,256,278,384,256,279,384,320,348,480,320,349,480,384,417,576,384,418,576,448,487,672,448,488,672,512,557,768,512,558,768,640,696,960,640,697,960,768,835,1152,768,836,1152,896,975,1344,896,976,1344,1024,1114,1536,1024,1115,1536,1152,1253,1728,1152,1254,1728,1280,1393,1920,1280,1394,1920],c=2*h[3*l+n];if(i+c>e.length)return-1;const d=e[i+6]>>5;let u=0;2===d?u+=2:(1&d&&1!==d&&(u+=2),4&d&&(u+=2));const f=(e[i+6]<<8|e[i+7])>>12-u&1,g=[2,1,2,3,3,4,4,5],m=g[d]+f,p=e[i+5]>>3,v=7&e[i+5],y=new Uint8Array([n<<6|p<<1|v>>2,(3&v)<<6|d<<3|f<<2|l>>4,l<<4&224]),E=1536/o*9e4,T=s+r*E,S=e.subarray(i,i+c);return t.config=y,t.channelCount=m,t.samplerate=o,t.samples.push({unit:S,pts:T}),c}class Xn{constructor(){this.VideoSample=null}createVideoSample(t,e,i,s){return{key:t,frame:!1,pts:e,dts:i,units:[],debug:s,length:0}}getLastNalUnit(t){var e;let i,s=this.VideoSample;if(s&&0!==s.units.length||(s=t[t.length-1]),null!=(e=s)&&e.units){const t=s.units;i=t[t.length-1]}return i}pushAccessUnit(t,e){if(t.units.length&&t.frame){if(void 0===t.pts){const i=e.samples,s=i.length;if(!s)return void e.dropped++;{const e=i[s-1];t.pts=e.pts,t.dts=e.dts}}e.samples.push(t)}t.debug.length&&Xt.log(t.pts+"/"+t.dts+":"+t.debug)}}class Qn{constructor(t){this.data=void 0,this.bytesAvailable=void 0,this.word=void 0,this.bitsAvailable=void 0,this.data=t,this.bytesAvailable=t.byteLength,this.word=0,this.bitsAvailable=0}loadWord(){const t=this.data,e=this.bytesAvailable,i=t.byteLength-e,s=new Uint8Array(4),r=Math.min(4,e);if(0===r)throw new Error("no bytes available");s.set(t.subarray(i,i+r)),this.word=new DataView(s.buffer).getUint32(0),this.bitsAvailable=8*r,this.bytesAvailable-=r}skipBits(t){let e;t=Math.min(t,8*this.bytesAvailable+this.bitsAvailable),this.bitsAvailable>t?(this.word<<=t,this.bitsAvailable-=t):(t-=this.bitsAvailable,e=t>>3,t-=e<<3,this.bytesAvailable-=e,this.loadWord(),this.word<<=t,this.bitsAvailable-=t)}readBits(t){let e=Math.min(this.bitsAvailable,t);const i=this.word>>>32-e;if(t>32&&Xt.error("Cannot read more than 32 bits at a time"),this.bitsAvailable-=e,this.bitsAvailable>0)this.word<<=e;else{if(!(this.bytesAvailable>0))throw new Error("no bits available");this.loadWord()}return e=t-e,e>0&&this.bitsAvailable?i<<e|this.readBits(e):i}skipLZ(){let t;for(t=0;t<this.bitsAvailable;++t)if(0!==(this.word&2147483648>>>t))return this.word<<=t,this.bitsAvailable-=t,t;return this.loadWord(),t+this.skipLZ()}skipUEG(){this.skipBits(1+this.skipLZ())}skipEG(){this.skipBits(1+this.skipLZ())}readUEG(){const t=this.skipLZ();return this.readBits(t+1)-1}readEG(){const t=this.readUEG();return 1&t?1+t>>>1:-1*(t>>>1)}readBoolean(){return 1===this.readBits(1)}readUByte(){return this.readBits(8)}readUShort(){return this.readBits(16)}readUInt(){return this.readBits(32)}skipScalingList(t){let e,i=8,s=8;for(let r=0;r<t;r++)0!==s&&(e=this.readEG(),s=(i+e+256)%256),i=0===s?i:s}readSPS(){let t,e,i,s=0,r=0,n=0,a=0;const o=this.readUByte.bind(this),l=this.readBits.bind(this),h=this.readUEG.bind(this),c=this.readBoolean.bind(this),d=this.skipBits.bind(this),u=this.skipEG.bind(this),f=this.skipUEG.bind(this),g=this.skipScalingList.bind(this);o();const m=o();if(l(5),d(3),o(),f(),100===m||110===m||122===m||244===m||44===m||83===m||86===m||118===m||128===m){const t=h();if(3===t&&d(1),f(),f(),d(1),c())for(e=3!==t?8:12,i=0;i<e;i++)c()&&g(i<6?16:64)}f();const p=h();if(0===p)h();else if(1===p)for(d(1),u(),u(),t=h(),i=0;i<t;i++)u();f(),d(1);const v=h(),y=h(),E=l(1);0===E&&d(1),d(1),c()&&(s=h(),r=h(),n=h(),a=h());let T=[1,1];if(c()&&c()){const t=o();switch(t){case 1:T=[1,1];break;case 2:T=[12,11];break;case 3:T=[10,11];break;case 4:T=[16,11];break;case 5:T=[40,33];break;case 6:T=[24,11];break;case 7:T=[20,11];break;case 8:T=[32,11];break;case 9:T=[80,33];break;case 10:T=[18,11];break;case 11:T=[15,11];break;case 12:T=[64,33];break;case 13:T=[160,99];break;case 14:T=[4,3];break;case 15:T=[3,2];break;case 16:T=[2,1];break;case 255:T=[o()<<8|o(),o()<<8|o()];break}}return{width:Math.ceil(16*(v+1)-2*s-2*r),height:(2-E)*(y+1)*16-(E?2:4)*(n+a),pixelRatio:T}}readSliceType(){return this.readUByte(),this.readUEG(),this.readUEG()}}class Jn extends Xn{parseAVCPES(t,e,i,s,r){const n=this.parseAVCNALu(t,i.data);let a,o=this.VideoSample,l=!1;i.data=null,o&&n.length&&!t.audFound&&(this.pushAccessUnit(o,t),o=this.VideoSample=this.createVideoSample(!1,i.pts,i.dts,"")),n.forEach(s=>{var n;switch(s.type){case 1:{let e=!1;a=!0;const r=s.data;if(l&&r.length>4){const t=new Qn(r).readSliceType();2!==t&&4!==t&&7!==t&&9!==t||(e=!0)}var h;if(e)null!=(h=o)&&h.frame&&!o.key&&(this.pushAccessUnit(o,t),o=this.VideoSample=null);o||(o=this.VideoSample=this.createVideoSample(!0,i.pts,i.dts,"")),o.frame=!0,o.key=e;break}case 5:a=!0,null!=(n=o)&&n.frame&&!o.key&&(this.pushAccessUnit(o,t),o=this.VideoSample=null),o||(o=this.VideoSample=this.createVideoSample(!0,i.pts,i.dts,"")),o.key=!0,o.frame=!0;break;case 6:a=!0,Ei(s.data,1,i.pts,e.samples);break;case 7:{var c,d;a=!0,l=!0;const e=s.data,i=new Qn(e),n=i.readSPS();if(!t.sps||t.width!==n.width||t.height!==n.height||(null==(c=t.pixelRatio)?void 0:c[0])!==n.pixelRatio[0]||(null==(d=t.pixelRatio)?void 0:d[1])!==n.pixelRatio[1]){t.width=n.width,t.height=n.height,t.pixelRatio=n.pixelRatio,t.sps=[e],t.duration=r;const i=e.subarray(1,4);let s="avc1.";for(let t=0;t<3;t++){let e=i[t].toString(16);e.length<2&&(e="0"+e),s+=e}t.codec=s}break}case 8:a=!0,t.pps=[s.data];break;case 9:a=!0,t.audFound=!0,o&&this.pushAccessUnit(o,t),o=this.VideoSample=this.createVideoSample(!1,i.pts,i.dts,"");break;case 12:a=!0;break;default:a=!1,o&&(o.debug+="unknown NAL "+s.type+" ");break}if(o&&a){const t=o.units;t.push(s)}}),s&&o&&(this.pushAccessUnit(o,t),this.VideoSample=null)}parseAVCNALu(t,e){const i=e.byteLength;let s=t.naluState||0;const r=s,n=[];let a,o,l,h=0,c=-1,d=0;-1===s&&(c=0,d=31&e[0],s=0,h=1);while(h<i)if(a=e[h++],s)if(1!==s)if(a)if(1===a){if(o=h-s-1,c>=0){const t={data:e.subarray(c,o),type:d};n.push(t)}else{const i=this.getLastNalUnit(t.samples);i&&(r&&h<=4-r&&i.state&&(i.data=i.data.subarray(0,i.data.byteLength-r)),o>0&&(i.data=mi(i.data,e.subarray(0,o)),i.state=0))}h<i?(l=31&e[h],c=h,d=l,s=0):s=-1}else s=0;else s=3;else s=a?0:2;else s=a?0:1;if(c>=0&&s>=0){const t={data:e.subarray(c,i),type:d,state:s};n.push(t)}if(0===n.length){const i=this.getLastNalUnit(t.samples);i&&(i.data=mi(i.data,e))}return t.naluState=s,n}}class Zn{constructor(t,e,i){this.keyData=void 0,this.decrypter=void 0,this.keyData=i,this.decrypter=new hn(e,{removePKCS7Padding:!1})}decryptBuffer(t){return this.decrypter.decrypt(t,this.keyData.key.buffer,this.keyData.iv.buffer)}decryptAacSample(t,e,i){const s=t[e].unit;if(s.length<=16)return;const r=s.subarray(16,s.length-s.length%16),n=r.buffer.slice(r.byteOffset,r.byteOffset+r.length);this.decryptBuffer(n).then(r=>{const n=new Uint8Array(r);s.set(n,16),this.decrypter.isSync()||this.decryptAacSamples(t,e+1,i)})}decryptAacSamples(t,e,i){for(;;e++){if(e>=t.length)return void i();if(!(t[e].unit.length<32)&&(this.decryptAacSample(t,e,i),!this.decrypter.isSync()))return}}getAvcEncryptedData(t){const e=16*Math.floor((t.length-48)/160)+16,i=new Int8Array(e);let s=0;for(let r=32;r<t.length-16;r+=160,s+=16)i.set(t.subarray(r,r+16),s);return i}getAvcDecryptedUnit(t,e){const i=new Uint8Array(e);let s=0;for(let r=32;r<t.length-16;r+=160,s+=16)t.set(i.subarray(s,s+16),r);return t}decryptAvcSample(t,e,i,s,r){const n=Ti(r.data),a=this.getAvcEncryptedData(n);this.decryptBuffer(a.buffer).then(a=>{r.data=this.getAvcDecryptedUnit(n,a),this.decrypter.isSync()||this.decryptAvcSamples(t,e,i+1,s)})}decryptAvcSamples(t,e,i,s){if(t instanceof Uint8Array)throw new Error("Cannot decrypt samples of type Uint8Array");for(;;e++,i=0){if(e>=t.length)return void s();const r=t[e].units;for(;;i++){if(i>=r.length)break;const n=r[i];if(!(n.data.length<=48||1!==n.type&&5!==n.type)&&(this.decryptAvcSample(t,e,i,s,n),!this.decrypter.isSync()))return}}}}const ta=188;class ea{constructor(t,e,i){this.observer=void 0,this.config=void 0,this.typeSupported=void 0,this.sampleAes=null,this.pmtParsed=!1,this.audioCodec=void 0,this.videoCodec=void 0,this._duration=0,this._pmtId=-1,this._videoTrack=void 0,this._audioTrack=void 0,this._id3Track=void 0,this._txtTrack=void 0,this.aacOverFlow=null,this.remainderData=null,this.videoParser=void 0,this.observer=t,this.config=e,this.typeSupported=i,this.videoParser=new Jn}static probe(t){const e=ea.syncOffset(t);return e>0&&Xt.warn("MPEG2-TS detected but first sync word found @ offset "+e),-1!==e}static syncOffset(t){const e=t.length;let i=Math.min(5*ta,e-ta)+1,s=0;while(s<i){let r=!1,n=-1,a=0;for(let o=s;o<e;o+=ta){if(71!==t[o]||e-o!==ta&&71!==t[o+ta]){if(a)return-1;break}if(a++,-1===n&&(n=o,0!==n&&(i=Math.min(n+99*ta,t.length-ta)+1)),r||(r=0===ia(t,o)),r&&a>1&&(0===n&&a>2||o+ta>i))return n}s++}return-1}static createTrack(t,e){return{container:"video"===t||"audio"===t?"video/mp2t":void 0,type:t,id:Ye[t],pid:-1,inputTimeScale:9e4,sequenceNumber:0,samples:[],dropped:0,duration:"audio"===t?e:void 0}}resetInitSegment(t,e,i,s){this.pmtParsed=!1,this._pmtId=-1,this._videoTrack=ea.createTrack("video"),this._audioTrack=ea.createTrack("audio",s),this._id3Track=ea.createTrack("id3"),this._txtTrack=ea.createTrack("text"),this._audioTrack.segmentCodec="aac",this.aacOverFlow=null,this.remainderData=null,this.audioCodec=e,this.videoCodec=i,this._duration=s}resetTimeStamp(){}resetContiguity(){const{_audioTrack:t,_videoTrack:e,_id3Track:i}=this;t&&(t.pesData=null),e&&(e.pesData=null),i&&(i.pesData=null),this.aacOverFlow=null,this.remainderData=null}demux(t,e,i=!1,s=!1){let r;i||(this.sampleAes=null);const n=this._videoTrack,a=this._audioTrack,o=this._id3Track,l=this._txtTrack;let h=n.pid,c=n.pesData,d=a.pid,u=o.pid,f=a.pesData,g=o.pesData,m=null,p=this.pmtParsed,v=this._pmtId,y=t.length;if(this.remainderData&&(t=mi(this.remainderData,t),y=t.length,this.remainderData=null),y<ta&&!s)return this.remainderData=t,{audioTrack:a,videoTrack:n,id3Track:o,textTrack:l};const E=Math.max(0,ea.syncOffset(t));y-=(y-E)%ta,y<t.byteLength&&!s&&(this.remainderData=new Uint8Array(t.buffer,y,t.buffer.byteLength-y));let T=0;for(let b=E;b<y;b+=ta)if(71===t[b]){const e=!!(64&t[b+1]),s=ia(t,b),y=(48&t[b+3])>>4;let T;if(y>1){if(T=b+5+t[b+4],T===b+ta)continue}else T=b+4;switch(s){case h:e&&(c&&(r=oa(c))&&this.videoParser.parseAVCPES(n,l,r,!1,this._duration),c={data:[],size:0}),c&&(c.data.push(t.subarray(T,b+ta)),c.size+=b+ta-T);break;case d:if(e){if(f&&(r=oa(f)))switch(a.segmentCodec){case"aac":this.parseAACPES(a,r);break;case"mp3":this.parseMPEGPES(a,r);break;case"ac3":this.parseAC3PES(a,r);break}f={data:[],size:0}}f&&(f.data.push(t.subarray(T,b+ta)),f.size+=b+ta-T);break;case u:e&&(g&&(r=oa(g))&&this.parseID3PES(o,r),g={data:[],size:0}),g&&(g.data.push(t.subarray(T,b+ta)),g.size+=b+ta-T);break;case 0:e&&(T+=t[T]+1),v=this._pmtId=sa(t,T);break;case v:{e&&(T+=t[T]+1);const s=ra(t,T,this.typeSupported,i,this.observer);h=s.videoPid,h>0&&(n.pid=h,n.segmentCodec=s.segmentVideoCodec),d=s.audioPid,d>0&&(a.pid=d,a.segmentCodec=s.segmentAudioCodec),u=s.id3Pid,u>0&&(o.pid=u),null===m||p||(Xt.warn(`MPEG-TS PMT found at ${b} after unknown PID '${m}'. Backtracking to sync byte @${E} to parse all TS packets.`),m=null,b=E-188),p=this.pmtParsed=!0;break}case 17:case 8191:break;default:m=s;break}}else T++;T>0&&na(this.observer,new Error(`Found ${T} TS packet/s that do not start with 0x47`)),n.pesData=c,a.pesData=f,o.pesData=g;const S={audioTrack:a,videoTrack:n,id3Track:o,textTrack:l};return s&&this.extractRemainingSamples(S),S}flush(){const{remainderData:t}=this;let e;return this.remainderData=null,e=t?this.demux(t,-1,!1,!0):{videoTrack:this._videoTrack,audioTrack:this._audioTrack,id3Track:this._id3Track,textTrack:this._txtTrack},this.extractRemainingSamples(e),this.sampleAes?this.decrypt(e,this.sampleAes):e}extractRemainingSamples(t){const{audioTrack:e,videoTrack:i,id3Track:s,textTrack:r}=t,n=i.pesData,a=e.pesData,o=s.pesData;let l;if(n&&(l=oa(n))?(this.videoParser.parseAVCPES(i,r,l,!0,this._duration),i.pesData=null):i.pesData=n,a&&(l=oa(a))){switch(e.segmentCodec){case"aac":this.parseAACPES(e,l);break;case"mp3":this.parseMPEGPES(e,l);break;case"ac3":this.parseAC3PES(e,l);break}e.pesData=null}else null!=a&&a.size&&Xt.log("last AAC PES packet truncated,might overlap between fragments"),e.pesData=a;o&&(l=oa(o))?(this.parseID3PES(s,l),s.pesData=null):s.pesData=o}demuxSampleAes(t,e,i){const s=this.demux(t,i,!0,!this.config.progressive),r=this.sampleAes=new Zn(this.observer,this.config,e);return this.decrypt(s,r)}decrypt(t,e){return new Promise(i=>{const{audioTrack:s,videoTrack:r}=t;s.samples&&"aac"===s.segmentCodec?e.decryptAacSamples(s.samples,0,()=>{r.samples?e.decryptAvcSamples(r.samples,0,0,()=>{i(t)}):i(t)}):r.samples&&e.decryptAvcSamples(r.samples,0,0,()=>{i(t)})})}destroy(){this._duration=0}parseAACPES(t,e){let i=0;const s=this.aacOverFlow;let r,n,a,o=e.data;if(s){this.aacOverFlow=null;const e=s.missing,r=s.sample.unit.byteLength;if(-1===e)o=mi(s.sample.unit,o);else{const n=r-e;s.sample.unit.set(o.subarray(0,e),n),t.samples.push(s.sample),i=s.missing}}for(r=i,n=o.length;r<n-1;r++)if(Dn(o,r))break;if(r!==i){let t;const e=r<n-1;if(t=e?"AAC PES did not start with ADTS header,offset:"+r:"No ADTS header found in AAC PES",na(this.observer,new Error(t),e),!e)return}if(wn(t,this.observer,o,r,this.audioCodec),void 0!==e.pts)a=e.pts;else{if(!s)return void Xt.warn("[tsdemuxer]: AAC PES unknown PTS");{const e=Cn(t.samplerate);a=s.sample.pts+e}}let l,h=0;while(r<n){if(l=On(t,o,r,a,h),r+=l.length,l.missing){this.aacOverFlow=l;break}for(h++;r<n-1;r++)if(Dn(o,r))break}}parseMPEGPES(t,e){const i=e.data,s=i.length;let r=0,n=0;const a=e.pts;if(void 0!==a)while(n<s)if(Gn(i,n)){const e=Un(t,i,n,a,r);if(!e)break;n+=e.length,r++}else n++;else Xt.warn("[tsdemuxer]: MPEG PES unknown PTS")}parseAC3PES(t,e){{const i=e.data,s=e.pts;if(void 0===s)return void Xt.warn("[tsdemuxer]: AC3 PES unknown PTS");const r=i.length;let n,a=0,o=0;while(o<r&&(n=zn(t,i,o,s,a++))>0)o+=n}}parseID3PES(t,e){if(void 0===e.pts)return void Xt.warn("[tsdemuxer]: ID3 PES unknown PTS");const i=Nt({},e,{type:this._videoTrack?Ss.emsg:Ss.audioId3,duration:Number.POSITIVE_INFINITY});t.samples.push(i)}}function ia(t,e){return((31&t[e+1])<<8)+t[e+2]}function sa(t,e){return(31&t[e+10])<<8|t[e+11]}function ra(t,e,i,s,r){const n={audioPid:-1,videoPid:-1,id3Pid:-1,segmentVideoCodec:"avc",segmentAudioCodec:"aac"},a=(15&t[e+1])<<8|t[e+2],o=e+3+a-4,l=(15&t[e+10])<<8|t[e+11];e+=12+l;while(e<o){const a=ia(t,e),o=(15&t[e+3])<<8|t[e+4];switch(t[e]){case 207:if(!s){aa("ADTS AAC");break}case 15:-1===n.audioPid&&(n.audioPid=a);break;case 21:-1===n.id3Pid&&(n.id3Pid=a);break;case 219:if(!s){aa("H.264");break}case 27:-1===n.videoPid&&(n.videoPid=a,n.segmentVideoCodec="avc");break;case 3:case 4:i.mpeg||i.mp3?-1===n.audioPid&&(n.audioPid=a,n.segmentAudioCodec="mp3"):Xt.log("MPEG audio found, not supported in this browser");break;case 193:if(!s){aa("AC-3");break}case 129:i.ac3?-1===n.audioPid&&(n.audioPid=a,n.segmentAudioCodec="ac3"):Xt.log("AC-3 audio found, not supported in this browser");break;case 6:if(-1===n.audioPid&&o>0){let s=e+5,r=o;while(r>2){const e=t[s];switch(e){case 106:!0!==i.ac3?Xt.log("AC-3 audio found, not supported in this browser for now"):(n.audioPid=a,n.segmentAudioCodec="ac3");break}const o=t[s+1]+2;s+=o,r-=o}}break;case 194:case 135:return na(r,new Error("Unsupported EC-3 in M2TS found")),n;case 36:return na(r,new Error("Unsupported HEVC in M2TS found")),n}e+=o+5}return n}function na(t,e,i){Xt.warn("parsing error: "+e.message),t.emit(Gt.ERROR,Gt.ERROR,{type:jt.MEDIA_ERROR,details:Kt.FRAG_PARSING_ERROR,fatal:!1,levelRetry:i,error:e,reason:e.message})}function aa(t){Xt.log(t+" with AES-128-CBC encryption found in unencrypted stream")}function oa(t){let e,i,s,r,n,a=0;const o=t.data;if(!t||0===t.size)return null;while(o[0].length<19&&o.length>1)o[0]=mi(o[0],o[1]),o.splice(1,1);e=o[0];const l=(e[0]<<16)+(e[1]<<8)+e[2];if(1===l){if(i=(e[4]<<8)+e[5],i&&i>t.size-6)return null;const l=e[7];192&l&&(r=536870912*(14&e[9])+4194304*(255&e[10])+16384*(254&e[11])+128*(255&e[12])+(254&e[13])/2,64&l?(n=536870912*(14&e[14])+4194304*(255&e[15])+16384*(254&e[16])+128*(255&e[17])+(254&e[18])/2,r-n>54e5&&(Xt.warn(Math.round((r-n)/9e4)+"s delta between PTS and DTS, align them"),r=n)):n=r),s=e[8];let h=s+9;if(t.size<=h)return null;t.size-=h;const c=new Uint8Array(t.size);for(let t=0,i=o.length;t<i;t++){e=o[t];let i=e.byteLength;if(h){if(h>i){h-=i;continue}e=e.subarray(h),i-=h,h=0}c.set(e,a),a+=i}return i&&(i-=s+3),{data:c,pts:r,dts:n,len:i}}return null}class la extends En{resetInitSegment(t,e,i,s){super.resetInitSegment(t,e,i,s),this._audioTrack={container:"audio/mpeg",type:"audio",id:2,pid:-1,sequenceNumber:0,segmentCodec:"mp3",samples:[],manifestCodec:e,duration:s,inputTimeScale:9e4,dropped:0}}static probe(t){if(!t)return!1;const e=we(t,0);let i=(null==e?void 0:e.length)||0;if(e&&11===t[i]&&119===t[i+1]&&void 0!==Oe(e)&&Yn(t,i)<=16)return!1;for(let s=t.length;i<s;i++)if(Kn(t,i))return Xt.log("MPEG Audio sync word found !"),!0;return!1}canParse(t,e){return jn(t,e)}appendFrame(t,e,i){if(null!==this.basePTS)return Un(t,e,i,this.basePTS,this.frameIndex)}}class ha{static getSilentFrame(t,e){switch(t){case"mp4a.40.2":if(1===e)return new Uint8Array([0,200,0,128,35,128]);if(2===e)return new Uint8Array([33,0,73,144,2,25,0,35,128]);if(3===e)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,142]);if(4===e)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,128,44,128,8,2,56]);if(5===e)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,130,48,4,153,0,33,144,2,56]);if(6===e)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,130,48,4,153,0,33,144,2,0,178,0,32,8,224]);break;default:if(1===e)return new Uint8Array([1,64,34,128,163,78,230,128,186,8,0,0,0,28,6,241,193,10,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,94]);if(2===e)return new Uint8Array([1,64,34,128,163,94,230,128,186,8,0,0,0,0,149,0,6,241,161,10,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,94]);if(3===e)return new Uint8Array([1,64,34,128,163,94,230,128,186,8,0,0,0,0,149,0,6,241,161,10,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,94]);break}}}const ca=Math.pow(2,32)-1;class da{static init(){let t;for(t in da.types={avc1:[],avcC:[],btrt:[],dinf:[],dref:[],esds:[],ftyp:[],hdlr:[],mdat:[],mdhd:[],mdia:[],mfhd:[],minf:[],moof:[],moov:[],mp4a:[],".mp3":[],dac3:[],"ac-3":[],mvex:[],mvhd:[],pasp:[],sdtp:[],stbl:[],stco:[],stsc:[],stsd:[],stsz:[],stts:[],tfdt:[],tfhd:[],traf:[],trak:[],trun:[],trex:[],tkhd:[],vmhd:[],smhd:[]},da.types)da.types.hasOwnProperty(t)&&(da.types[t]=[t.charCodeAt(0),t.charCodeAt(1),t.charCodeAt(2),t.charCodeAt(3)]);const e=new Uint8Array([0,0,0,0,0,0,0,0,118,105,100,101,0,0,0,0,0,0,0,0,0,0,0,0,86,105,100,101,111,72,97,110,100,108,101,114,0]),i=new Uint8Array([0,0,0,0,0,0,0,0,115,111,117,110,0,0,0,0,0,0,0,0,0,0,0,0,83,111,117,110,100,72,97,110,100,108,101,114,0]);da.HDLR_TYPES={video:e,audio:i};const s=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,12,117,114,108,32,0,0,0,1]),r=new Uint8Array([0,0,0,0,0,0,0,0]);da.STTS=da.STSC=da.STCO=r,da.STSZ=new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0]),da.VMHD=new Uint8Array([0,0,0,1,0,0,0,0,0,0,0,0]),da.SMHD=new Uint8Array([0,0,0,0,0,0,0,0]),da.STSD=new Uint8Array([0,0,0,0,0,0,0,1]);const n=new Uint8Array([105,115,111,109]),a=new Uint8Array([97,118,99,49]),o=new Uint8Array([0,0,0,1]);da.FTYP=da.box(da.types.ftyp,n,o,n,a),da.DINF=da.box(da.types.dinf,da.box(da.types.dref,s))}static box(t,...e){let i=8,s=e.length;const r=s;while(s--)i+=e[s].byteLength;const n=new Uint8Array(i);for(n[0]=i>>24&255,n[1]=i>>16&255,n[2]=i>>8&255,n[3]=255&i,n.set(t,4),s=0,i=8;s<r;s++)n.set(e[s],i),i+=e[s].byteLength;return n}static hdlr(t){return da.box(da.types.hdlr,da.HDLR_TYPES[t])}static mdat(t){return da.box(da.types.mdat,t)}static mdhd(t,e){e*=t;const i=Math.floor(e/(ca+1)),s=Math.floor(e%(ca+1));return da.box(da.types.mdhd,new Uint8Array([1,0,0,0,0,0,0,0,0,0,0,2,0,0,0,0,0,0,0,3,t>>24&255,t>>16&255,t>>8&255,255&t,i>>24,i>>16&255,i>>8&255,255&i,s>>24,s>>16&255,s>>8&255,255&s,85,196,0,0]))}static mdia(t){return da.box(da.types.mdia,da.mdhd(t.timescale,t.duration),da.hdlr(t.type),da.minf(t))}static mfhd(t){return da.box(da.types.mfhd,new Uint8Array([0,0,0,0,t>>24,t>>16&255,t>>8&255,255&t]))}static minf(t){return"audio"===t.type?da.box(da.types.minf,da.box(da.types.smhd,da.SMHD),da.DINF,da.stbl(t)):da.box(da.types.minf,da.box(da.types.vmhd,da.VMHD),da.DINF,da.stbl(t))}static moof(t,e,i){return da.box(da.types.moof,da.mfhd(t),da.traf(i,e))}static moov(t){let e=t.length;const i=[];while(e--)i[e]=da.trak(t[e]);return da.box.apply(null,[da.types.moov,da.mvhd(t[0].timescale,t[0].duration)].concat(i).concat(da.mvex(t)))}static mvex(t){let e=t.length;const i=[];while(e--)i[e]=da.trex(t[e]);return da.box.apply(null,[da.types.mvex,...i])}static mvhd(t,e){e*=t;const i=Math.floor(e/(ca+1)),s=Math.floor(e%(ca+1)),r=new Uint8Array([1,0,0,0,0,0,0,0,0,0,0,2,0,0,0,0,0,0,0,3,t>>24&255,t>>16&255,t>>8&255,255&t,i>>24,i>>16&255,i>>8&255,255&i,s>>24,s>>16&255,s>>8&255,255&s,0,1,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,255,255,255,255]);return da.box(da.types.mvhd,r)}static sdtp(t){const e=t.samples||[],i=new Uint8Array(4+e.length);let s,r;for(s=0;s<e.length;s++)r=e[s].flags,i[s+4]=r.dependsOn<<4|r.isDependedOn<<2|r.hasRedundancy;return da.box(da.types.sdtp,i)}static stbl(t){return da.box(da.types.stbl,da.stsd(t),da.box(da.types.stts,da.STTS),da.box(da.types.stsc,da.STSC),da.box(da.types.stsz,da.STSZ),da.box(da.types.stco,da.STCO))}static avc1(t){let e,i,s,r=[],n=[];for(e=0;e<t.sps.length;e++)i=t.sps[e],s=i.byteLength,r.push(s>>>8&255),r.push(255&s),r=r.concat(Array.prototype.slice.call(i));for(e=0;e<t.pps.length;e++)i=t.pps[e],s=i.byteLength,n.push(s>>>8&255),n.push(255&s),n=n.concat(Array.prototype.slice.call(i));const a=da.box(da.types.avcC,new Uint8Array([1,r[3],r[4],r[5],255,224|t.sps.length].concat(r).concat([t.pps.length]).concat(n))),o=t.width,l=t.height,h=t.pixelRatio[0],c=t.pixelRatio[1];return da.box(da.types.avc1,new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,o>>8&255,255&o,l>>8&255,255&l,0,72,0,0,0,72,0,0,0,0,0,0,0,1,18,100,97,105,108,121,109,111,116,105,111,110,47,104,108,115,46,106,115,0,0,0,0,0,0,0,0,0,0,0,0,0,0,24,17,17]),a,da.box(da.types.btrt,new Uint8Array([0,28,156,128,0,45,198,192,0,45,198,192])),da.box(da.types.pasp,new Uint8Array([h>>24,h>>16&255,h>>8&255,255&h,c>>24,c>>16&255,c>>8&255,255&c])))}static esds(t){const e=t.config.length;return new Uint8Array([0,0,0,0,3,23+e,0,1,0,4,15+e,64,21,0,0,0,0,0,0,0,0,0,0,0,5].concat([e]).concat(t.config).concat([6,1,2]))}static audioStsd(t){const e=t.samplerate;return new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,t.channelCount,0,16,0,0,0,0,e>>8&255,255&e,0,0])}static mp4a(t){return da.box(da.types.mp4a,da.audioStsd(t),da.box(da.types.esds,da.esds(t)))}static mp3(t){return da.box(da.types[".mp3"],da.audioStsd(t))}static ac3(t){return da.box(da.types["ac-3"],da.audioStsd(t),da.box(da.types.dac3,t.config))}static stsd(t){return"audio"===t.type?"mp3"===t.segmentCodec&&"mp3"===t.codec?da.box(da.types.stsd,da.STSD,da.mp3(t)):"ac3"===t.segmentCodec?da.box(da.types.stsd,da.STSD,da.ac3(t)):da.box(da.types.stsd,da.STSD,da.mp4a(t)):da.box(da.types.stsd,da.STSD,da.avc1(t))}static tkhd(t){const e=t.id,i=t.duration*t.timescale,s=t.width,r=t.height,n=Math.floor(i/(ca+1)),a=Math.floor(i%(ca+1));return da.box(da.types.tkhd,new Uint8Array([1,0,0,7,0,0,0,0,0,0,0,2,0,0,0,0,0,0,0,3,e>>24&255,e>>16&255,e>>8&255,255&e,0,0,0,0,n>>24,n>>16&255,n>>8&255,255&n,a>>24,a>>16&255,a>>8&255,255&a,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,s>>8&255,255&s,0,0,r>>8&255,255&r,0,0]))}static traf(t,e){const i=da.sdtp(t),s=t.id,r=Math.floor(e/(ca+1)),n=Math.floor(e%(ca+1));return da.box(da.types.traf,da.box(da.types.tfhd,new Uint8Array([0,0,0,0,s>>24,s>>16&255,s>>8&255,255&s])),da.box(da.types.tfdt,new Uint8Array([1,0,0,0,r>>24,r>>16&255,r>>8&255,255&r,n>>24,n>>16&255,n>>8&255,255&n])),da.trun(t,i.length+16+20+8+16+8+8),i)}static trak(t){return t.duration=t.duration||4294967295,da.box(da.types.trak,da.tkhd(t),da.mdia(t))}static trex(t){const e=t.id;return da.box(da.types.trex,new Uint8Array([0,0,0,0,e>>24,e>>16&255,e>>8&255,255&e,0,0,0,1,0,0,0,0,0,0,0,0,0,1,0,1]))}static trun(t,e){const i=t.samples||[],s=i.length,r=12+16*s,n=new Uint8Array(r);let a,o,l,h,c,d;for(e+=8+r,n.set(["video"===t.type?1:0,0,15,1,s>>>24&255,s>>>16&255,s>>>8&255,255&s,e>>>24&255,e>>>16&255,e>>>8&255,255&e],0),a=0;a<s;a++)o=i[a],l=o.duration,h=o.size,c=o.flags,d=o.cts,n.set([l>>>24&255,l>>>16&255,l>>>8&255,255&l,h>>>24&255,h>>>16&255,h>>>8&255,255&h,c.isLeading<<2|c.dependsOn,c.isDependedOn<<6|c.hasRedundancy<<4|c.paddingValue<<1|c.isNonSync,61440&c.degradPrio,15&c.degradPrio,d>>>24&255,d>>>16&255,d>>>8&255,255&d],12+16*a);return da.box(da.types.trun,n)}static initSegment(t){da.types||da.init();const e=da.moov(t),i=mi(da.FTYP,e);return i}}da.types=void 0,da.HDLR_TYPES=void 0,da.STTS=void 0,da.STSC=void 0,da.STCO=void 0,da.STSZ=void 0,da.VMHD=void 0,da.SMHD=void 0,da.STSD=void 0,da.FTYP=void 0,da.DINF=void 0;const ua=9e4;function fa(t,e,i=1,s=!1){const r=t*e*i;return s?Math.round(r):r}function ga(t,e,i=1,s=!1){return fa(t,e,1/i,s)}function ma(t,e=!1){return fa(t,1e3,1/ua,e)}function pa(t,e=1){return fa(t,ua,1/e)}const va=1e4,ya=1024,Ea=1152,Ta=1536;let Sa,ba=null,Aa=null;class La{constructor(t,e,i,s=""){if(this.observer=void 0,this.config=void 0,this.typeSupported=void 0,this.ISGenerated=!1,this._initPTS=null,this._initDTS=null,this.nextAvcDts=null,this.nextAudioPts=null,this.videoSampleDuration=null,this.isAudioContiguous=!1,this.isVideoContiguous=!1,this.videoTrackConfig=void 0,this.observer=t,this.config=e,this.typeSupported=i,this.ISGenerated=!1,null===ba){const t=navigator.userAgent||"",e=t.match(/Chrome\/(\d+)/i);ba=e?parseInt(e[1]):0}if(null===Aa){const t=navigator.userAgent.match(/Safari\/(\d+)/i);Aa=t?parseInt(t[1]):0}}destroy(){this.config=this.videoTrackConfig=this._initPTS=this._initDTS=null}resetTimeStamp(t){Xt.log("[mp4-remuxer]: initPTS & initDTS reset"),this._initPTS=this._initDTS=t}resetNextTimestamp(){Xt.log("[mp4-remuxer]: reset next timestamp"),this.isVideoContiguous=!1,this.isAudioContiguous=!1}resetInitSegment(){Xt.log("[mp4-remuxer]: ISGenerated flag reset"),this.ISGenerated=!1,this.videoTrackConfig=void 0}getVideoStartPts(t){let e=!1;const i=t.reduce((t,i)=>{const s=i.pts-t;return s<-4294967296?(e=!0,Ra(t,i.pts)):s>0?t:i.pts},t[0].pts);return e&&Xt.debug("PTS rollover detected"),i}remux(t,e,i,s,r,n,a,o){let l,h,c,d,u,f,g=r,m=r;const p=t.pid>-1,v=e.pid>-1,y=e.samples.length,E=t.samples.length>0,T=a&&y>0||y>1,S=(!p||E)&&(!v||T)||this.ISGenerated||a;if(S){if(this.ISGenerated){var b,A,L,R;const t=this.videoTrackConfig;!t||e.width===t.width&&e.height===t.height&&(null==(b=e.pixelRatio)?void 0:b[0])===(null==(A=t.pixelRatio)?void 0:A[0])&&(null==(L=e.pixelRatio)?void 0:L[1])===(null==(R=t.pixelRatio)?void 0:R[1])||this.resetInitSegment()}else c=this.generateIS(t,e,r,n);const i=this.isVideoContiguous;let s,a=-1;if(T&&(a=Da(e.samples),!i&&this.config.forceKeyFrameOnDiscontinuity))if(f=!0,a>0){Xt.warn(`[mp4-remuxer]: Dropped ${a} out of ${y} video samples due to a missing keyframe`);const t=this.getVideoStartPts(e.samples);e.samples=e.samples.slice(a),e.dropped+=a,m+=(e.samples[0].pts-t)/e.inputTimeScale,s=m}else-1===a&&(Xt.warn(`[mp4-remuxer]: No keyframe found out of ${y} video samples`),f=!1);if(this.ISGenerated){if(E&&T){const i=this.getVideoStartPts(e.samples),s=Ra(t.samples[0].pts,i)-i,r=s/e.inputTimeScale;g+=Math.max(0,r),m+=Math.max(0,-r)}if(E){if(t.samplerate||(Xt.warn("[mp4-remuxer]: regenerate InitSegment as audio detected"),c=this.generateIS(t,e,r,n)),h=this.remuxAudio(t,g,this.isAudioContiguous,n,v||T||o===cs.AUDIO?m:void 0),T){const s=h?h.endPTS-h.startPTS:0;e.inputTimeScale||(Xt.warn("[mp4-remuxer]: regenerate InitSegment as video detected"),c=this.generateIS(t,e,r,n)),l=this.remuxVideo(e,m,i,s)}}else T&&(l=this.remuxVideo(e,m,i,0));l&&(l.firstKeyFrame=a,l.independent=-1!==a,l.firstKeyFramePTS=s)}}return this.ISGenerated&&this._initPTS&&this._initDTS&&(i.samples.length&&(u=Ia(i,r,this._initPTS,this._initDTS)),s.samples.length&&(d=ka(s,r,this._initPTS))),{audio:h,video:l,initSegment:c,independent:f,text:d,id3:u}}generateIS(t,e,i,s){const r=t.samples,n=e.samples,a=this.typeSupported,o={},l=this._initPTS;let h,c,d,u=!l||s,f="audio/mp4";if(u&&(h=c=1/0),t.config&&r.length){switch(t.timescale=t.samplerate,t.segmentCodec){case"mp3":a.mpeg?(f="audio/mpeg",t.codec=""):a.mp3&&(t.codec="mp3");break;case"ac3":t.codec="ac-3";break}o.audio={id:"audio",container:f,codec:t.codec,initSegment:"mp3"===t.segmentCodec&&a.mpeg?new Uint8Array(0):da.initSegment([t]),metadata:{channelCount:t.channelCount}},u&&(d=t.inputTimeScale,l&&d===l.timescale?u=!1:h=c=r[0].pts-Math.round(d*i))}if(e.sps&&e.pps&&n.length){if(e.timescale=e.inputTimeScale,o.video={id:"main",container:"video/mp4",codec:e.codec,initSegment:da.initSegment([e]),metadata:{width:e.width,height:e.height}},u)if(d=e.inputTimeScale,l&&d===l.timescale)u=!1;else{const t=this.getVideoStartPts(n),e=Math.round(d*i);c=Math.min(c,Ra(n[0].dts,t)-e),h=Math.min(h,t-e)}this.videoTrackConfig={width:e.width,height:e.height,pixelRatio:e.pixelRatio}}if(Object.keys(o).length)return this.ISGenerated=!0,u?(this._initPTS={baseTime:h,timescale:d},this._initDTS={baseTime:c,timescale:d}):h=d=void 0,{tracks:o,initPTS:h,timescale:d}}remuxVideo(t,e,i,s){const r=t.inputTimeScale,n=t.samples,a=[],o=n.length,l=this._initPTS;let h,c,d=this.nextAvcDts,u=8,f=this.videoSampleDuration,g=Number.POSITIVE_INFINITY,m=Number.NEGATIVE_INFINITY,p=!1;if(!i||null===d){const t=e*r,s=n[0].pts-Ra(n[0].dts,n[0].pts);ba&&null!==d&&Math.abs(t-s-d)<15e3?i=!0:d=t-s}const v=l.baseTime*r/l.timescale;for(let M=0;M<o;M++){const t=n[M];t.pts=Ra(t.pts-v,d),t.dts=Ra(t.dts-v,d),t.dts<n[M>0?M-1:M].dts&&(p=!0)}p&&n.sort((function(t,e){const i=t.dts-e.dts,s=t.pts-e.pts;return i||s})),h=n[0].dts,c=n[n.length-1].dts;const y=c-h,E=y?Math.round(y/(o-1)):f||t.inputTimeScale/30;if(i){const t=h-d,i=t>E,s=t<-1;if((i||s)&&(i?Xt.warn(`AVC: ${ma(t,!0)} ms (${t}dts) hole between fragments detected at ${e.toFixed(3)}`):Xt.warn(`AVC: ${ma(-t,!0)} ms (${t}dts) overlapping between fragments detected at ${e.toFixed(3)}`),!s||d>=n[0].pts||ba)){h=d;const e=n[0].pts-t;if(i)n[0].dts=h,n[0].pts=e;else for(let i=0;i<n.length;i++){if(n[i].dts>e)break;n[i].dts-=t,n[i].pts-=t}Xt.log(`Video: Initial PTS/DTS adjusted: ${ma(e,!0)}/${ma(h,!0)}, delta: ${ma(t,!0)} ms`)}}h=Math.max(0,h);let T=0,S=0,b=h;for(let M=0;M<o;M++){const t=n[M],e=t.units,i=e.length;let s=0;for(let r=0;r<i;r++)s+=e[r].data.length;S+=s,T+=i,t.length=s,t.dts<b?(t.dts=b,b+=E/4|0||1):b=t.dts,g=Math.min(t.pts,g),m=Math.max(t.pts,m)}c=n[o-1].dts;const A=S+4*T+8;let L;try{L=new Uint8Array(A)}catch(P){return void this.observer.emit(Gt.ERROR,Gt.ERROR,{type:jt.MUX_ERROR,details:Kt.REMUX_ALLOC_ERROR,fatal:!1,error:P,bytes:A,reason:"fail allocating video mdat "+A})}const R=new DataView(L.buffer);R.setUint32(0,A),L.set(da.types.mdat,4);let D=!1,I=Number.POSITIVE_INFINITY,k=Number.POSITIVE_INFINITY,w=Number.NEGATIVE_INFINITY,C=Number.NEGATIVE_INFINITY;for(let M=0;M<o;M++){const t=n[M],e=t.units;let i,l=0;for(let s=0,r=e.length;s<r;s++){const t=e[s],i=t.data,r=t.data.byteLength;R.setUint32(u,r),u+=4,L.set(i,u),u+=r,l+=4+r}if(M<o-1)f=n[M+1].dts-t.dts,i=n[M+1].pts-t.pts;else{const e=this.config,a=M>0?t.dts-n[M-1].dts:E;if(i=M>0?t.pts-n[M-1].pts:E,e.stretchShortVideoTrack&&null!==this.nextAudioPts){const i=Math.floor(e.maxBufferHole*r),n=(s?g+s*r:this.nextAudioPts)-t.pts;n>i?(f=n-a,f<0?f=a:D=!0,Xt.log(`[mp4-remuxer]: It is approximately ${n/90} ms to the next segment; using duration ${f/90} ms for the last video frame.`)):f=a}else f=a}const h=Math.round(t.pts-t.dts);I=Math.min(I,f),w=Math.max(w,f),k=Math.min(k,i),C=Math.max(C,i),a.push(new wa(t.key,f,l,h))}if(a.length)if(ba){if(ba<70){const t=a[0].flags;t.dependsOn=2,t.isNonSync=0}}else if(Aa&&C-k<w-I&&E/w<.025&&0===a[0].cts){Xt.warn("Found irregular gaps in sample duration. Using PTS instead of DTS to determine MP4 sample duration.");let t=h;for(let e=0,i=a.length;e<i;e++){const s=t+a[e].duration,r=t+a[e].cts;if(e<i-1){const t=s+a[e+1].cts;a[e].duration=t-r}else a[e].duration=e?a[e-1].duration:E;a[e].cts=0,t=s}}f=D||!f?E:f,this.nextAvcDts=d=c+f,this.videoSampleDuration=f,this.isVideoContiguous=!0;const _=da.moof(t.sequenceNumber++,h,Nt({},t,{samples:a})),O="video",x={data1:_,data2:L,startPTS:g/r,endPTS:(m+f)/r,startDTS:h/r,endDTS:d/r,type:O,hasAudio:!1,hasVideo:!0,nb:a.length,dropped:t.dropped};return t.samples=[],t.dropped=0,x}getSamplesPerFrame(t){switch(t.segmentCodec){case"mp3":return Ea;case"ac3":return Ta;default:return ya}}remuxAudio(t,e,i,s,r){const n=t.inputTimeScale,a=t.samplerate?t.samplerate:n,o=n/a,l=this.getSamplesPerFrame(t),h=l*o,c=this._initPTS,d="mp3"===t.segmentCodec&&this.typeSupported.mpeg,u=[],f=void 0!==r;let g=t.samples,m=d?0:8,p=this.nextAudioPts||-1;const v=e*n,y=c.baseTime*n/c.timescale;if(this.isAudioContiguous=i=i||g.length&&p>0&&(s&&Math.abs(v-p)<9e3||Math.abs(Ra(g[0].pts-y,v)-p)<20*h),g.forEach((function(t){t.pts=Ra(t.pts-y,v)})),!i||p<0){if(g=g.filter(t=>t.pts>=0),!g.length)return;p=0===r?0:s&&!f?Math.max(0,v):g[0].pts}if("aac"===t.segmentCodec){const e=this.config.maxAudioFramesDrift;for(let i=0,s=p;i<g.length;i++){const r=g[i],a=r.pts,o=a-s,l=Math.abs(1e3*o/n);if(o<=-e*h&&f)0===i&&(Xt.warn(`Audio frame @ ${(a/n).toFixed(3)}s overlaps nextAudioPts by ${Math.round(1e3*o/n)} ms.`),this.nextAudioPts=p=s=a);else if(o>=e*h&&l<va&&f){let e=Math.round(o/h);s=a-e*h,s<0&&(e--,s+=h),0===i&&(this.nextAudioPts=p=s),Xt.warn(`[mp4-remuxer]: Injecting ${e} audio frame @ ${(s/n).toFixed(3)}s due to ${Math.round(1e3*o/n)} ms gap.`);for(let n=0;n<e;n++){const e=Math.max(s,0);let n=ha.getSilentFrame(t.manifestCodec||t.codec,t.channelCount);n||(Xt.log("[mp4-remuxer]: Unable to get silent frame for given audio codec; duplicating last frame instead."),n=r.unit.subarray()),g.splice(i,0,{unit:n,pts:e}),s+=h,i++}}r.pts=s,s+=h}}let E,T=null,S=null,b=0,A=g.length;while(A--)b+=g[A].unit.byteLength;for(let O=0,x=g.length;O<x;O++){const e=g[O],s=e.unit;let r=e.pts;if(null!==S){const t=u[O-1];t.duration=Math.round((r-S)/o)}else{if(i&&"aac"===t.segmentCodec&&(r=p),T=r,!(b>0))return;b+=m;try{E=new Uint8Array(b)}catch(_){return void this.observer.emit(Gt.ERROR,Gt.ERROR,{type:jt.MUX_ERROR,details:Kt.REMUX_ALLOC_ERROR,fatal:!1,error:_,bytes:b,reason:"fail allocating audio mdat "+b})}if(!d){const t=new DataView(E.buffer);t.setUint32(0,b),E.set(da.types.mdat,4)}}E.set(s,m);const n=s.byteLength;m+=n,u.push(new wa(!0,l,n,0)),S=r}const L=u.length;if(!L)return;const R=u[u.length-1];this.nextAudioPts=p=S+o*R.duration;const D=d?new Uint8Array(0):da.moof(t.sequenceNumber++,T/o,Nt({},t,{samples:u}));t.samples=[];const I=T/n,k=p/n,w="audio",C={data1:D,data2:E,startPTS:I,endPTS:k,startDTS:I,endDTS:k,type:w,hasAudio:!0,hasVideo:!1,nb:L};return this.isAudioContiguous=!0,C}remuxEmptyAudio(t,e,i,s){const r=t.inputTimeScale,n=t.samplerate?t.samplerate:r,a=r/n,o=this.nextAudioPts,l=this._initDTS,h=9e4*l.baseTime/l.timescale,c=(null!==o?o:s.startDTS*r)+h,d=s.endDTS*r+h,u=a*ya,f=Math.ceil((d-c)/u),g=ha.getSilentFrame(t.manifestCodec||t.codec,t.channelCount);if(Xt.warn("[mp4-remuxer]: remux empty Audio"),!g)return void Xt.trace("[mp4-remuxer]: Unable to remuxEmptyAudio since we were unable to get a silent frame for given audio codec");const m=[];for(let p=0;p<f;p++){const t=c+p*u;m.push({unit:g,pts:t,dts:t})}return t.samples=m,this.remuxAudio(t,e,i,!1)}}function Ra(t,e){let i;if(null===e)return t;i=e<t?-8589934592:8589934592;while(Math.abs(t-e)>4294967296)t+=i;return t}function Da(t){for(let e=0;e<t.length;e++)if(t[e].key)return e;return-1}function Ia(t,e,i,s){const r=t.samples.length;if(!r)return;const n=t.inputTimeScale;for(let o=0;o<r;o++){const r=t.samples[o];r.pts=Ra(r.pts-i.baseTime*n/i.timescale,e*n)/n,r.dts=Ra(r.dts-s.baseTime*n/s.timescale,e*n)/n}const a=t.samples;return t.samples=[],{samples:a}}function ka(t,e,i){const s=t.samples.length;if(!s)return;const r=t.inputTimeScale;for(let a=0;a<s;a++){const s=t.samples[a];s.pts=Ra(s.pts-i.baseTime*r/i.timescale,e*r)/r}t.samples.sort((t,e)=>t.pts-e.pts);const n=t.samples;return t.samples=[],{samples:n}}class wa{constructor(t,e,i,s){this.size=void 0,this.duration=void 0,this.cts=void 0,this.flags=void 0,this.duration=e,this.size=i,this.cts=s,this.flags={isLeading:0,isDependedOn:0,hasRedundancy:0,degradPrio:0,dependsOn:t?2:1,isNonSync:t?0:1}}}class Ca{constructor(){this.emitInitSegment=!1,this.audioCodec=void 0,this.videoCodec=void 0,this.initData=void 0,this.initPTS=null,this.initTracks=void 0,this.lastEndTime=null}destroy(){}resetTimeStamp(t){this.initPTS=t,this.lastEndTime=null}resetNextTimestamp(){this.lastEndTime=null}resetInitSegment(t,e,i,s){this.audioCodec=e,this.videoCodec=i,this.generateInitSegment(li(t,s)),this.emitInitSegment=!0}generateInitSegment(t){let{audioCodec:e,videoCodec:i}=this;if(null==t||!t.byteLength)return this.initTracks=void 0,void(this.initData=void 0);const s=this.initData=si(t);s.audio&&(e=Oa(s.audio,re.AUDIO)),s.video&&(i=Oa(s.video,re.VIDEO));const r={};s.audio&&s.video?r.audiovideo={container:"video/mp4",codec:e+","+i,initSegment:t,id:"main"}:s.audio?r.audio={container:"audio/mp4",codec:e,initSegment:t,id:"audio"}:s.video?r.video={container:"video/mp4",codec:i,initSegment:t,id:"main"}:Xt.warn("[passthrough-remuxer.ts]: initSegment does not contain moov or trak boxes."),this.initTracks=r}remux(t,e,i,s,r,n){var a,o;let{initPTS:l,lastEndTime:h}=this;const c={audio:void 0,video:void 0,text:s,id3:i,initSegment:void 0};Ut(h)||(h=this.lastEndTime=r||0);const d=e.samples;if(null==d||!d.length)return c;const u={initPTS:void 0,timescale:1};let f=this.initData;if(null!=(a=f)&&a.length||(this.generateInitSegment(d),f=this.initData),null==(o=f)||!o.length)return Xt.warn("[passthrough-remuxer.ts]: Failed to generate initSegment."),c;this.emitInitSegment&&(u.tracks=this.initTracks,this.emitInitSegment=!1);const g=di(d,f),m=ci(f,d),p=null===m?r:m;(_a(l,p,r,g)||u.timescale!==l.timescale&&n)&&(u.initPTS=p-r,l&&1===l.timescale&&Xt.warn("Adjusting initPTS by "+(u.initPTS-l.baseTime)),this.initPTS=l={baseTime:u.initPTS,timescale:1});const v=t?p-l.baseTime/l.timescale:h,y=v+g;fi(f,d,l.baseTime/l.timescale),g>0?this.lastEndTime=y:(Xt.warn("Duration parsed from mp4 should be greater than zero"),this.resetNextTimestamp());const E=!!f.audio,T=!!f.video;let S="";E&&(S+="audio"),T&&(S+="video");const b={data1:d,startPTS:v,startDTS:v,endPTS:y,endDTS:y,type:S,hasAudio:E,hasVideo:T,nb:1,dropped:0};return c.audio="audio"===b.type?b:void 0,c.video="audio"!==b.type?b:void 0,c.initSegment=u,c.id3=Ia(i,r,l,l),s.samples.length&&(c.text=ka(s,r,l)),c}}function _a(t,e,i,s){if(null===t)return!0;const r=Math.max(s,1),n=e-t.baseTime/t.timescale;return Math.abs(n-i)>r}function Oa(t,e){const i=null==t?void 0:t.codec;if(i&&i.length>4)return i;if(e===re.AUDIO){if("ec-3"===i||"ac-3"===i||"alac"===i)return i;if("fLaC"===i||"Opus"===i){const t=!1;return qi(i,t)}const t="mp4a.40.5";return Xt.info(`Parsed audio codec "${i}" or audio object type not handled. Using "${t}"`),t}return Xt.warn(`Unhandled video codec "${i}"`),"hvc1"===i||"hev1"===i?"hvc1.1.6.L120.90":"av01"===i?"av01.0.04M.08":"avc1.42e01e"}try{Sa=self.performance.now.bind(self.performance)}catch(nc){Xt.debug("Unable to use Performance API on this environment"),Sa=null==me?void 0:me.Date.now}const xa=[{demux:qn,remux:Ca},{demux:ea,remux:La},{demux:Vn,remux:La},{demux:la,remux:La}];xa.splice(2,0,{demux:Wn,remux:La});class Pa{constructor(t,e,i,s,r){this.async=!1,this.observer=void 0,this.typeSupported=void 0,this.config=void 0,this.vendor=void 0,this.id=void 0,this.demuxer=void 0,this.remuxer=void 0,this.decrypter=void 0,this.probe=void 0,this.decryptionPromise=null,this.transmuxConfig=void 0,this.currentTransmuxState=void 0,this.observer=t,this.typeSupported=e,this.config=i,this.vendor=s,this.id=r}configure(t){this.transmuxConfig=t,this.decrypter&&this.decrypter.reset()}push(t,e,i,s){const r=i.transmuxing;r.executeStart=Sa();let n=new Uint8Array(t);const{currentTransmuxState:a,transmuxConfig:o}=this;s&&(this.currentTransmuxState=s);const{contiguous:l,discontinuity:h,trackSwitch:c,accurateTimeOffset:d,timeOffset:u,initSegmentChange:f}=s||a,{audioCodec:g,videoCodec:m,defaultInitPts:p,duration:v,initSegmentData:y}=o,E=Ma(n,e);if(E&&"AES-128"===E.method){const t=this.getDecrypter();if(!t.isSync())return this.decryptionPromise=t.webCryptoDecrypt(n,E.key.buffer,E.iv.buffer).then(t=>{const e=this.push(t,null,i);return this.decryptionPromise=null,e}),this.decryptionPromise;{let e=t.softwareDecrypt(n,E.key.buffer,E.iv.buffer);const s=i.part>-1;if(s&&(e=t.flush()),!e)return r.executeEnd=Sa(),Fa(i);n=new Uint8Array(e)}}const T=this.needsProbing(h,c);if(T){const t=this.configureTransmuxer(n);if(t)return Xt.warn("[transmuxer] "+t.message),this.observer.emit(Gt.ERROR,Gt.ERROR,{type:jt.MEDIA_ERROR,details:Kt.FRAG_PARSING_ERROR,fatal:!1,error:t,reason:t.message}),r.executeEnd=Sa(),Fa(i)}(h||c||f||T)&&this.resetInitSegment(y,g,m,v,e),(h||f||T)&&this.resetInitialTimestamp(p),l||this.resetContiguity();const S=this.transmux(n,E,u,d,i),b=this.currentTransmuxState;return b.contiguous=!0,b.discontinuity=!1,b.trackSwitch=!1,r.executeEnd=Sa(),S}flush(t){const e=t.transmuxing;e.executeStart=Sa();const{decrypter:i,currentTransmuxState:s,decryptionPromise:r}=this;if(r)return r.then(()=>this.flush(t));const n=[],{timeOffset:a}=s;if(i){const e=i.flush();e&&n.push(this.push(e,null,t))}const{demuxer:o,remuxer:l}=this;if(!o||!l)return e.executeEnd=Sa(),[Fa(t)];const h=o.flush(a);return Na(h)?h.then(e=>(this.flushRemux(n,e,t),n)):(this.flushRemux(n,h,t),n)}flushRemux(t,e,i){const{audioTrack:s,videoTrack:r,id3Track:n,textTrack:a}=e,{accurateTimeOffset:o,timeOffset:l}=this.currentTransmuxState;Xt.log(`[transmuxer.ts]: Flushed fragment ${i.sn}${i.part>-1?" p: "+i.part:""} of level ${i.level}`);const h=this.remuxer.remux(s,r,n,a,l,o,!0,this.id);t.push({remuxResult:h,chunkMeta:i}),i.transmuxing.executeEnd=Sa()}resetInitialTimestamp(t){const{demuxer:e,remuxer:i}=this;e&&i&&(e.resetTimeStamp(t),i.resetTimeStamp(t))}resetContiguity(){const{demuxer:t,remuxer:e}=this;t&&e&&(t.resetContiguity(),e.resetNextTimestamp())}resetInitSegment(t,e,i,s,r){const{demuxer:n,remuxer:a}=this;n&&a&&(n.resetInitSegment(t,e,i,s),a.resetInitSegment(t,e,i,r))}destroy(){this.demuxer&&(this.demuxer.destroy(),this.demuxer=void 0),this.remuxer&&(this.remuxer.destroy(),this.remuxer=void 0)}transmux(t,e,i,s,r){let n;return n=e&&"SAMPLE-AES"===e.method?this.transmuxSampleAes(t,e,i,s,r):this.transmuxUnencrypted(t,i,s,r),n}transmuxUnencrypted(t,e,i,s){const{audioTrack:r,videoTrack:n,id3Track:a,textTrack:o}=this.demuxer.demux(t,e,!1,!this.config.progressive),l=this.remuxer.remux(r,n,a,o,e,i,!1,this.id);return{remuxResult:l,chunkMeta:s}}transmuxSampleAes(t,e,i,s,r){return this.demuxer.demuxSampleAes(t,e,i).then(t=>{const e=this.remuxer.remux(t.audioTrack,t.videoTrack,t.id3Track,t.textTrack,i,s,!1,this.id);return{remuxResult:e,chunkMeta:r}})}configureTransmuxer(t){const{config:e,observer:i,typeSupported:s,vendor:r}=this;let n;for(let d=0,u=xa.length;d<u;d++){var a;if(null!=(a=xa[d].demux)&&a.probe(t)){n=xa[d];break}}if(!n)return new Error("Failed to find demuxer by probing fragment data");const o=this.demuxer,l=this.remuxer,h=n.remux,c=n.demux;l&&l instanceof h||(this.remuxer=new h(i,e,s,r)),o&&o instanceof c||(this.demuxer=new c(i,e,s),this.probe=c.probe)}needsProbing(t,e){return!this.demuxer||!this.remuxer||t||e}getDecrypter(){let t=this.decrypter;return t||(t=this.decrypter=new hn(this.config)),t}}function Ma(t,e){let i=null;return t.byteLength>0&&null!=(null==e?void 0:e.key)&&null!==e.iv&&null!=e.method&&(i=e),i}const Fa=t=>({remuxResult:{},chunkMeta:t});function Na(t){return"then"in t&&t.then instanceof Function}class Ua{constructor(t,e,i,s,r){this.audioCodec=void 0,this.videoCodec=void 0,this.initSegmentData=void 0,this.duration=void 0,this.defaultInitPts=void 0,this.audioCodec=t,this.videoCodec=e,this.initSegmentData=i,this.duration=s,this.defaultInitPts=r||null}}class Ba{constructor(t,e,i,s,r,n){this.discontinuity=void 0,this.contiguous=void 0,this.accurateTimeOffset=void 0,this.trackSwitch=void 0,this.timeOffset=void 0,this.initSegmentChange=void 0,this.discontinuity=t,this.contiguous=e,this.accurateTimeOffset=i,this.trackSwitch=s,this.timeOffset=r,this.initSegmentChange=n}}var $a={exports:{}};(function(t){var e=Object.prototype.hasOwnProperty,i="~";function s(){}function r(t,e,i){this.fn=t,this.context=e,this.once=i||!1}function n(t,e,s,n,a){if("function"!==typeof s)throw new TypeError("The listener must be a function");var o=new r(s,n||t,a),l=i?i+e:e;return t._events[l]?t._events[l].fn?t._events[l]=[t._events[l],o]:t._events[l].push(o):(t._events[l]=o,t._eventsCount++),t}function a(t,e){0===--t._eventsCount?t._events=new s:delete t._events[e]}function o(){this._events=new s,this._eventsCount=0}Object.create&&(s.prototype=Object.create(null),(new s).__proto__||(i=!1)),o.prototype.eventNames=function(){var t,s,r=[];if(0===this._eventsCount)return r;for(s in t=this._events)e.call(t,s)&&r.push(i?s.slice(1):s);return Object.getOwnPropertySymbols?r.concat(Object.getOwnPropertySymbols(t)):r},o.prototype.listeners=function(t){var e=i?i+t:t,s=this._events[e];if(!s)return[];if(s.fn)return[s.fn];for(var r=0,n=s.length,a=new Array(n);r<n;r++)a[r]=s[r].fn;return a},o.prototype.listenerCount=function(t){var e=i?i+t:t,s=this._events[e];return s?s.fn?1:s.length:0},o.prototype.emit=function(t,e,s,r,n,a){var o=i?i+t:t;if(!this._events[o])return!1;var l,h,c=this._events[o],d=arguments.length;if(c.fn){switch(c.once&&this.removeListener(t,c.fn,void 0,!0),d){case 1:return c.fn.call(c.context),!0;case 2:return c.fn.call(c.context,e),!0;case 3:return c.fn.call(c.context,e,s),!0;case 4:return c.fn.call(c.context,e,s,r),!0;case 5:return c.fn.call(c.context,e,s,r,n),!0;case 6:return c.fn.call(c.context,e,s,r,n,a),!0}for(h=1,l=new Array(d-1);h<d;h++)l[h-1]=arguments[h];c.fn.apply(c.context,l)}else{var u,f=c.length;for(h=0;h<f;h++)switch(c[h].once&&this.removeListener(t,c[h].fn,void 0,!0),d){case 1:c[h].fn.call(c[h].context);break;case 2:c[h].fn.call(c[h].context,e);break;case 3:c[h].fn.call(c[h].context,e,s);break;case 4:c[h].fn.call(c[h].context,e,s,r);break;default:if(!l)for(u=1,l=new Array(d-1);u<d;u++)l[u-1]=arguments[u];c[h].fn.apply(c[h].context,l)}}return!0},o.prototype.on=function(t,e,i){return n(this,t,e,i,!1)},o.prototype.once=function(t,e,i){return n(this,t,e,i,!0)},o.prototype.removeListener=function(t,e,s,r){var n=i?i+t:t;if(!this._events[n])return this;if(!e)return a(this,n),this;var o=this._events[n];if(o.fn)o.fn!==e||r&&!o.once||s&&o.context!==s||a(this,n);else{for(var l=0,h=[],c=o.length;l<c;l++)(o[l].fn!==e||r&&!o[l].once||s&&o[l].context!==s)&&h.push(o[l]);h.length?this._events[n]=1===h.length?h[0]:h:a(this,n)}return this},o.prototype.removeAllListeners=function(t){var e;return t?(e=i?i+t:t,this._events[e]&&a(this,e)):(this._events=new s,this._eventsCount=0),this},o.prototype.off=o.prototype.removeListener,o.prototype.addListener=o.prototype.on,o.prefixed=i,o.EventEmitter=o,t.exports=o})($a);var Ga=$a.exports,ja=wt(Ga);class Ka{constructor(t,e,i,s){this.error=null,this.hls=void 0,this.id=void 0,this.observer=void 0,this.frag=null,this.part=null,this.useWorker=void 0,this.workerContext=null,this.onwmsg=void 0,this.transmuxer=null,this.onTransmuxComplete=void 0,this.onFlush=void 0;const r=t.config;this.hls=t,this.id=e,this.useWorker=!!r.enableWorker,this.onTransmuxComplete=i,this.onFlush=s;const n=(t,e)=>{e=e||{},e.frag=this.frag,e.id=this.id,t===Gt.ERROR&&(this.error=e.error),this.hls.trigger(t,e)};this.observer=new ja,this.observer.on(Gt.FRAG_DECRYPTED,n),this.observer.on(Gt.ERROR,n);const a=Pi(r.preferManagedMediaSource)||{isTypeSupported:()=>!1},o={mpeg:a.isTypeSupported("audio/mpeg"),mp3:a.isTypeSupported('audio/mp4; codecs="mp3"'),ac3:a.isTypeSupported('audio/mp4; codecs="ac-3"')};if(this.useWorker&&"undefined"!==typeof Worker){const t=r.workerPath||mn();if(t){try{r.workerPath?(Xt.log(`loading Web Worker ${r.workerPath} for "${e}"`),this.workerContext=vn(r.workerPath)):(Xt.log(`injecting Web Worker for "${e}"`),this.workerContext=pn()),this.onwmsg=t=>this.onWorkerMessage(t);const{worker:t}=this.workerContext;t.addEventListener("message",this.onwmsg),t.onerror=t=>{const i=new Error(`${t.message}  (${t.filename}:${t.lineno})`);r.enableWorker=!1,Xt.warn(`Error in "${e}" Web Worker, fallback to inline`),this.hls.trigger(Gt.ERROR,{type:jt.OTHER_ERROR,details:Kt.INTERNAL_EXCEPTION,fatal:!1,event:"demuxerWorker",error:i})},t.postMessage({cmd:"init",typeSupported:o,vendor:"",id:e,config:JSON.stringify(r)})}catch(nc){Xt.warn(`Error setting up "${e}" Web Worker, fallback to inline`,nc),this.resetWorker(),this.error=null,this.transmuxer=new Pa(this.observer,o,r,"",e)}return}}this.transmuxer=new Pa(this.observer,o,r,"",e)}resetWorker(){if(this.workerContext){const{worker:t,objectURL:e}=this.workerContext;e&&self.URL.revokeObjectURL(e),t.removeEventListener("message",this.onwmsg),t.onerror=null,t.terminate(),this.workerContext=null}}destroy(){if(this.workerContext)this.resetWorker(),this.onwmsg=void 0;else{const t=this.transmuxer;t&&(t.destroy(),this.transmuxer=null)}const t=this.observer;t&&t.removeAllListeners(),this.frag=null,this.observer=null,this.hls=null}push(t,e,i,s,r,n,a,o,l,h){var c,d;l.transmuxing.start=self.performance.now();const{transmuxer:u}=this,f=n?n.start:r.start,g=r.decryptdata,m=this.frag,p=!(m&&r.cc===m.cc),v=!(m&&l.level===m.level),y=m?l.sn-m.sn:-1,E=this.part?l.part-this.part.index:-1,T=0===y&&l.id>1&&l.id===(null==m?void 0:m.stats.chunkCount),S=!v&&(1===y||0===y&&(1===E||T&&E<=0)),b=self.performance.now();(v||y||0===r.stats.parsing.start)&&(r.stats.parsing.start=b),!n||!E&&S||(n.stats.parsing.start=b);const A=!(m&&(null==(c=r.initSegment)?void 0:c.url)===(null==(d=m.initSegment)?void 0:d.url)),L=new Ba(p,S,o,v,f,A);if(!S||p||A){Xt.log(`[transmuxer-interface, ${r.type}]: Starting new transmux session for sn: ${l.sn} p: ${l.part} level: ${l.level} id: ${l.id}\n        discontinuity: ${p}\n        trackSwitch: ${v}\n        contiguous: ${S}\n        accurateTimeOffset: ${o}\n        timeOffset: ${f}\n        initSegmentChange: ${A}`);const t=new Ua(i,s,e,a,h);this.configureTransmuxer(t)}if(this.frag=r,this.part=n,this.workerContext)this.workerContext.worker.postMessage({cmd:"demux",data:t,decryptdata:g,chunkMeta:l,state:L},t instanceof ArrayBuffer?[t]:[]);else if(u){const e=u.push(t,g,l,L);Na(e)?(u.async=!0,e.then(t=>{this.handleTransmuxComplete(t)}).catch(t=>{this.transmuxerError(t,l,"transmuxer-interface push error")})):(u.async=!1,this.handleTransmuxComplete(e))}}flush(t){t.transmuxing.start=self.performance.now();const{transmuxer:e}=this;if(this.workerContext)this.workerContext.worker.postMessage({cmd:"flush",chunkMeta:t});else if(e){let i=e.flush(t);const s=Na(i);s||e.async?(Na(i)||(i=Promise.resolve(i)),i.then(e=>{this.handleFlushResult(e,t)}).catch(e=>{this.transmuxerError(e,t,"transmuxer-interface flush error")})):this.handleFlushResult(i,t)}}transmuxerError(t,e,i){this.hls&&(this.error=t,this.hls.trigger(Gt.ERROR,{type:jt.MEDIA_ERROR,details:Kt.FRAG_PARSING_ERROR,chunkMeta:e,frag:this.frag||void 0,fatal:!1,error:t,err:t,reason:i}))}handleFlushResult(t,e){t.forEach(t=>{this.handleTransmuxComplete(t)}),this.onFlush(e)}onWorkerMessage(t){const e=t.data;if(null==e||!e.event)return void Xt.warn("worker message received with no "+(e?"event name":"data"));const i=this.hls;if(this.hls)switch(e.event){case"init":{var s;const t=null==(s=this.workerContext)?void 0:s.objectURL;t&&self.URL.revokeObjectURL(t);break}case"transmuxComplete":this.handleTransmuxComplete(e.data);break;case"flush":this.onFlush(e.data);break;case"workerLog":Xt[e.data.logType]&&Xt[e.data.logType](e.data.message);break;default:e.data=e.data||{},e.data.frag=this.frag,e.data.id=this.id,i.trigger(e.event,e.data);break}}configureTransmuxer(t){const{transmuxer:e}=this;this.workerContext?this.workerContext.worker.postMessage({cmd:"configure",config:t}):e&&e.configure(t)}handleTransmuxComplete(t){t.chunkMeta.transmuxing.end=self.performance.now(),this.onTransmuxComplete(t)}}function Va(t,e){if(t.length!==e.length)return!1;for(let i=0;i<t.length;i++)if(!Ha(t[i].attrs,e[i].attrs))return!1;return!0}function Ha(t,e,i){const s=t["STABLE-RENDITION-ID"];return s&&!i?s===e["STABLE-RENDITION-ID"]:!(i||["LANGUAGE","NAME","CHARACTERISTICS","AUTOSELECT","DEFAULT","FORCED","ASSOC-LANGUAGE"]).some(i=>t[i]!==e[i])}function qa(t,e){return e.label.toLowerCase()===t.name.toLowerCase()&&(!e.language||e.language.toLowerCase()===(t.lang||"").toLowerCase())}const Ya=100;class Wa extends un{constructor(t,e,i){super(t,e,i,"[audio-stream-controller]",cs.AUDIO),this.videoBuffer=null,this.videoTrackCC=-1,this.waitingVideoCC=-1,this.bufferedTrack=null,this.switchingTrack=null,this.trackId=-1,this.waitingData=null,this.mainDetails=null,this.flushing=!1,this.bufferFlushed=!1,this.cachedTrackLoadedData=null,this._registerListeners()}onHandlerDestroying(){this._unregisterListeners(),super.onHandlerDestroying(),this.mainDetails=null,this.bufferedTrack=null,this.switchingTrack=null}_registerListeners(){const{hls:t}=this;t.on(Gt.MEDIA_ATTACHED,this.onMediaAttached,this),t.on(Gt.MEDIA_DETACHING,this.onMediaDetaching,this),t.on(Gt.MANIFEST_LOADING,this.onManifestLoading,this),t.on(Gt.LEVEL_LOADED,this.onLevelLoaded,this),t.on(Gt.AUDIO_TRACKS_UPDATED,this.onAudioTracksUpdated,this),t.on(Gt.AUDIO_TRACK_SWITCHING,this.onAudioTrackSwitching,this),t.on(Gt.AUDIO_TRACK_LOADED,this.onAudioTrackLoaded,this),t.on(Gt.ERROR,this.onError,this),t.on(Gt.BUFFER_RESET,this.onBufferReset,this),t.on(Gt.BUFFER_CREATED,this.onBufferCreated,this),t.on(Gt.BUFFER_FLUSHING,this.onBufferFlushing,this),t.on(Gt.BUFFER_FLUSHED,this.onBufferFlushed,this),t.on(Gt.INIT_PTS_FOUND,this.onInitPtsFound,this),t.on(Gt.FRAG_BUFFERED,this.onFragBuffered,this)}_unregisterListeners(){const{hls:t}=this;t.off(Gt.MEDIA_ATTACHED,this.onMediaAttached,this),t.off(Gt.MEDIA_DETACHING,this.onMediaDetaching,this),t.off(Gt.MANIFEST_LOADING,this.onManifestLoading,this),t.off(Gt.LEVEL_LOADED,this.onLevelLoaded,this),t.off(Gt.AUDIO_TRACKS_UPDATED,this.onAudioTracksUpdated,this),t.off(Gt.AUDIO_TRACK_SWITCHING,this.onAudioTrackSwitching,this),t.off(Gt.AUDIO_TRACK_LOADED,this.onAudioTrackLoaded,this),t.off(Gt.ERROR,this.onError,this),t.off(Gt.BUFFER_RESET,this.onBufferReset,this),t.off(Gt.BUFFER_CREATED,this.onBufferCreated,this),t.off(Gt.BUFFER_FLUSHING,this.onBufferFlushing,this),t.off(Gt.BUFFER_FLUSHED,this.onBufferFlushed,this),t.off(Gt.INIT_PTS_FOUND,this.onInitPtsFound,this),t.off(Gt.FRAG_BUFFERED,this.onFragBuffered,this)}onInitPtsFound(t,{frag:e,id:i,initPTS:s,timescale:r}){if("main"===i){const t=e.cc;this.initPTS[e.cc]={baseTime:s,timescale:r},this.log(`InitPTS for cc: ${t} found from main: ${s}`),this.videoTrackCC=t,this.state===dn.WAITING_INIT_PTS&&this.tick()}}startLoad(t){if(!this.levels)return this.startPosition=t,void(this.state=dn.STOPPED);const e=this.lastCurrentTime;this.stopLoad(),this.setInterval(Ya),e>0&&-1===t?(this.log("Override startPosition with lastCurrentTime @"+e.toFixed(3)),t=e,this.state=dn.IDLE):(this.loadedmetadata=!1,this.state=dn.WAITING_TRACK),this.nextLoadPosition=this.startPosition=this.lastCurrentTime=t,this.tick()}doTick(){switch(this.state){case dn.IDLE:this.doTickIdle();break;case dn.WAITING_TRACK:{var t;const{levels:e,trackId:i}=this,s=null==e||null==(t=e[i])?void 0:t.details;if(s){if(this.waitForCdnTuneIn(s))break;this.state=dn.WAITING_INIT_PTS}break}case dn.FRAG_LOADING_WAITING_RETRY:{var e;const t=performance.now(),i=this.retryDate;if(!i||t>=i||null!=(e=this.media)&&e.seeking){const{levels:t,trackId:e}=this;this.log("RetryDate reached, switch back to IDLE state"),this.resetStartWhenNotLoaded((null==t?void 0:t[e])||null),this.state=dn.IDLE}break}case dn.WAITING_INIT_PTS:{const t=this.waitingData;if(t){const{frag:e,part:i,cache:s,complete:r}=t;if(void 0!==this.initPTS[e.cc]){this.waitingData=null,this.waitingVideoCC=-1,this.state=dn.FRAG_LOADING;const t=s.flush(),n={frag:e,part:i,payload:t,networkDetails:null};this._handleFragmentLoadProgress(n),r&&super._handleFragmentLoadComplete(n)}else if(this.videoTrackCC!==this.waitingVideoCC)this.log(`Waiting fragment cc (${e.cc}) cancelled because video is at cc ${this.videoTrackCC}`),this.clearWaitingFragment();else{const t=this.getLoadPosition(),i=Gr.bufferInfo(this.mediaBuffer,t,this.config.maxBufferHole),s=lr(i.end,this.config.maxFragLookUpTolerance,e);s<0&&(this.log(`Waiting fragment cc (${e.cc}) @ ${e.start} cancelled because another fragment at ${i.end} is needed`),this.clearWaitingFragment())}}else this.state=dn.IDLE}}this.onTickEnd()}clearWaitingFragment(){const t=this.waitingData;t&&(this.fragmentTracker.removeFragment(t.frag),this.waitingData=null,this.waitingVideoCC=-1,this.state=dn.IDLE)}resetLoadingState(){this.clearWaitingFragment(),super.resetLoadingState()}onTickEnd(){const{media:t}=this;null!=t&&t.readyState&&(this.lastCurrentTime=t.currentTime)}doTickIdle(){const{hls:t,levels:e,media:i,trackId:s}=this,r=t.config;if(!i&&(this.startFragRequested||!r.startFragPrefetch)||null==e||!e[s])return;const n=e[s],a=n.details;if(!a||a.live&&this.levelLastLoaded!==n||this.waitForCdnTuneIn(a))return void(this.state=dn.WAITING_TRACK);const o=this.mediaBuffer?this.mediaBuffer:this.media;this.bufferFlushed&&o&&(this.bufferFlushed=!1,this.afterBufferFlushed(o,re.AUDIO,cs.AUDIO));const l=this.getFwdBufferInfo(o,cs.AUDIO);if(null===l)return;const{bufferedTrack:h,switchingTrack:c}=this;if(!c&&this._streamEnded(l,a))return t.trigger(Gt.BUFFER_EOS,{type:"audio"}),void(this.state=dn.ENDED);const d=this.getFwdBufferInfo(this.videoBuffer?this.videoBuffer:this.media,cs.MAIN),u=l.len,f=this.getMaxBufferLength(null==d?void 0:d.len),g=a.fragments,m=g[0].start;let p=this.flushing?this.getLoadPosition():l.end;if(c&&i){const t=this.getLoadPosition();h&&!Ha(c.attrs,h.attrs)&&(p=t),a.PTSKnown&&t<m&&(l.end>m||l.nextStart)&&(this.log("Alt audio track ahead of main track, seek to start of alt audio track"),i.currentTime=m+.05)}if(u>=f&&!c&&p<g[g.length-1].start)return;let v=this.getNextFragment(p,a),y=!1;if(v&&this.isLoopLoading(v,p)&&(y=!!v.gap,v=this.getNextFragmentLoopLoading(v,a,l,cs.MAIN,f)),!v)return void(this.bufferFlushed=!0);const E=d&&v.start>d.end+a.targetduration;if(E||(null==d||!d.len)&&l.len){const t=this.getAppendedFrag(v.start,cs.MAIN);if(null===t)return;if(y||(y=!!t.gap||!!E&&0===d.len),E&&!y||y&&l.nextStart&&l.nextStart<t.end)return}this.loadFragment(v,n,p)}getMaxBufferLength(t){const e=super.getMaxBufferLength();return t?Math.min(Math.max(e,t),this.config.maxMaxBufferLength):e}onMediaDetaching(){this.videoBuffer=null,this.bufferFlushed=this.flushing=!1,super.onMediaDetaching()}onAudioTracksUpdated(t,{audioTracks:e}){this.resetTransmuxer(),this.levels=e.map(t=>new Ns(t))}onAudioTrackSwitching(t,e){const i=!!e.url;this.trackId=e.id;const{fragCurrent:s}=this;s&&(s.abortRequests(),this.removeUnbufferedFrags(s.start)),this.resetLoadingState(),i?this.setInterval(Ya):this.resetTransmuxer(),i?(this.switchingTrack=e,this.state=dn.IDLE,this.flushAudioIfNeeded(e)):(this.switchingTrack=null,this.bufferedTrack=e,this.state=dn.STOPPED),this.tick()}onManifestLoading(){this.fragmentTracker.removeAllFragments(),this.startPosition=this.lastCurrentTime=0,this.bufferFlushed=this.flushing=!1,this.levels=this.mainDetails=this.waitingData=this.bufferedTrack=this.cachedTrackLoadedData=this.switchingTrack=null,this.startFragRequested=!1,this.trackId=this.videoTrackCC=this.waitingVideoCC=-1}onLevelLoaded(t,e){this.mainDetails=e.details,null!==this.cachedTrackLoadedData&&(this.hls.trigger(Gt.AUDIO_TRACK_LOADED,this.cachedTrackLoadedData),this.cachedTrackLoadedData=null)}onAudioTrackLoaded(t,e){var i;if(null==this.mainDetails)return void(this.cachedTrackLoadedData=e);const{levels:s}=this,{details:r,id:n}=e;if(!s)return void this.warn("Audio tracks were reset while loading level "+n);this.log(`Audio track ${n} loaded [${r.startSN},${r.endSN}]${r.lastPartSn?`[part-${r.lastPartSn}-${r.lastPartIndex}]`:""},duration:${r.totalduration}`);const a=s[n];let o=0;if(r.live||null!=(i=a.details)&&i.live){this.checkLiveUpdate(r);const t=this.mainDetails;if(r.deltaUpdateFailed||!t)return;var l;if(!a.details&&r.hasProgramDateTime&&t.hasProgramDateTime)Qr(r,t),o=r.fragments[0].start;else o=this.alignPlaylists(r,a.details,null==(l=this.levelLastLoaded)?void 0:l.details)}a.details=r,this.levelLastLoaded=a,this.startFragRequested||!this.mainDetails&&r.live||this.setStartPosition(this.mainDetails||r,o),this.state!==dn.WAITING_TRACK||this.waitForCdnTuneIn(r)||(this.state=dn.IDLE),this.tick()}_handleFragmentLoadProgress(t){var e;const{frag:i,part:s,payload:r}=t,{config:n,trackId:a,levels:o}=this;if(!o)return void this.warn(`Audio tracks were reset while fragment load was in progress. Fragment ${i.sn} of level ${i.level} will not be buffered`);const l=o[a];if(!l)return void this.warn("Audio track is undefined on fragment load progress");const h=l.details;if(!h)return this.warn("Audio track details undefined on fragment load progress"),void this.removeUnbufferedFrags(i.start);const c=n.defaultAudioCodec||l.audioCodec||"mp4a.40.2";let d=this.transmuxer;d||(d=this.transmuxer=new Ka(this.hls,cs.AUDIO,this._handleTransmuxComplete.bind(this),this._handleTransmuxerFlush.bind(this)));const u=this.initPTS[i.cc],f=null==(e=i.initSegment)?void 0:e.data;if(void 0!==u){const t=!1,e=s?s.index:-1,n=-1!==e,a=new jr(i.level,i.sn,i.stats.chunkCount,r.byteLength,e,n);d.push(r,f,c,"",i,s,h.totalduration,t,a,u)}else{this.log(`Unknown video PTS for cc ${i.cc}, waiting for video PTS before demuxing audio frag ${i.sn} of [${h.startSN} ,${h.endSN}],track ${a}`);const{cache:t}=this.waitingData=this.waitingData||{frag:i,part:s,cache:new fn,complete:!1};t.push(new Uint8Array(r)),this.waitingVideoCC=this.videoTrackCC,this.state=dn.WAITING_INIT_PTS}}_handleFragmentLoadComplete(t){this.waitingData?this.waitingData.complete=!0:super._handleFragmentLoadComplete(t)}onBufferReset(){this.mediaBuffer=this.videoBuffer=null,this.loadedmetadata=!1}onBufferCreated(t,e){const i=e.tracks.audio;i&&(this.mediaBuffer=i.buffer||null),e.tracks.video&&(this.videoBuffer=e.tracks.video.buffer||null)}onFragBuffered(t,e){const{frag:i,part:s}=e;if(i.type===cs.AUDIO)if(this.fragContextChanged(i))this.warn(`Fragment ${i.sn}${s?" p: "+s.index:""} of level ${i.level} finished buffering, but was aborted. state: ${this.state}, audioSwitch: ${this.switchingTrack?this.switchingTrack.name:"false"}`);else{if("initSegment"!==i.sn){this.fragPrevious=i;const t=this.switchingTrack;t&&(this.bufferedTrack=t,this.switchingTrack=null,this.hls.trigger(Gt.AUDIO_TRACK_SWITCHED,xt({},t)))}this.fragBufferedComplete(i,s)}else if(!this.loadedmetadata&&i.type===cs.MAIN){const t=this.videoBuffer||this.media;if(t){const e=Gr.getBuffered(t);e.length&&(this.loadedmetadata=!0)}}}onError(t,e){var i;if(e.fatal)this.state=dn.ERROR;else switch(e.details){case Kt.FRAG_GAP:case Kt.FRAG_PARSING_ERROR:case Kt.FRAG_DECRYPT_ERROR:case Kt.FRAG_LOAD_ERROR:case Kt.FRAG_LOAD_TIMEOUT:case Kt.KEY_LOAD_ERROR:case Kt.KEY_LOAD_TIMEOUT:this.onFragmentOrKeyLoadError(cs.AUDIO,e);break;case Kt.AUDIO_TRACK_LOAD_ERROR:case Kt.AUDIO_TRACK_LOAD_TIMEOUT:case Kt.LEVEL_PARSING_ERROR:e.levelRetry||this.state!==dn.WAITING_TRACK||(null==(i=e.context)?void 0:i.type)!==hs.AUDIO_TRACK||(this.state=dn.IDLE);break;case Kt.BUFFER_APPEND_ERROR:case Kt.BUFFER_FULL_ERROR:if(!e.parent||"audio"!==e.parent)return;if(e.details===Kt.BUFFER_APPEND_ERROR)return void this.resetLoadingState();this.reduceLengthAndFlushBuffer(e)&&(this.bufferedTrack=null,super.flushMainBuffer(0,Number.POSITIVE_INFINITY,"audio"));break;case Kt.INTERNAL_EXCEPTION:this.recoverWorkerError(e);break}}onBufferFlushing(t,{type:e}){e!==re.VIDEO&&(this.flushing=!0)}onBufferFlushed(t,{type:e}){if(e!==re.VIDEO){this.flushing=!1,this.bufferFlushed=!0,this.state===dn.ENDED&&(this.state=dn.IDLE);const t=this.mediaBuffer||this.media;t&&(this.afterBufferFlushed(t,e,cs.AUDIO),this.tick())}}_handleTransmuxComplete(t){var e;const i="audio",{hls:s}=this,{remuxResult:r,chunkMeta:n}=t,a=this.getCurrentContext(n);if(!a)return void this.resetWhenMissingContext(n);const{frag:o,part:l,level:h}=a,{details:c}=h,{audio:d,text:u,id3:f,initSegment:g}=r;if(!this.fragContextChanged(o)&&c){if(this.state=dn.PARSING,this.switchingTrack&&d&&this.completeAudioSwitch(this.switchingTrack),null!=g&&g.tracks){const t=o.initSegment||o;this._bufferInitSegment(h,g.tracks,t,n),s.trigger(Gt.FRAG_PARSING_INIT_SEGMENT,{frag:t,id:i,tracks:g.tracks})}if(d){const{startPTS:t,endPTS:e,startDTS:i,endDTS:s}=d;l&&(l.elementaryStreams[re.AUDIO]={startPTS:t,endPTS:e,startDTS:i,endDTS:s}),o.setElementaryStreamInfo(re.AUDIO,t,e,i,s),this.bufferFragmentData(d,o,l,n)}if(null!=f&&null!=(e=f.samples)&&e.length){const t=Nt({id:i,frag:o,details:c},f);s.trigger(Gt.FRAG_PARSING_METADATA,t)}if(u){const t=Nt({id:i,frag:o,details:c},u);s.trigger(Gt.FRAG_PARSING_USERDATA,t)}}else this.fragmentTracker.removeFragment(o)}_bufferInitSegment(t,e,i,s){if(this.state!==dn.PARSING)return;e.video&&delete e.video;const r=e.audio;if(!r)return;r.id="audio";const n=t.audioCodec;this.log(`Init audio buffer, container:${r.container}, codecs[level/parsed]=[${n}/${r.codec}]`),n&&1===n.split(",").length&&(r.levelCodec=n),this.hls.trigger(Gt.BUFFER_CODECS,e);const a=r.initSegment;if(null!=a&&a.byteLength){const t={type:"audio",frag:i,part:null,chunkMeta:s,parent:i.type,data:a};this.hls.trigger(Gt.BUFFER_APPENDING,t)}this.tickImmediate()}loadFragment(t,e,i){const s=this.fragmentTracker.getState(t);var r;if(this.fragCurrent=t,this.switchingTrack||s===Fr.NOT_LOADED||s===Fr.PARTIAL)if("initSegment"===t.sn)this._loadInitSegment(t,e);else if(null!=(r=e.details)&&r.live&&!this.initPTS[t.cc]){this.log(`Waiting for video PTS in continuity counter ${t.cc} of live stream before loading audio fragment ${t.sn} of level ${this.trackId}`),this.state=dn.WAITING_INIT_PTS;const i=this.mainDetails;i&&i.fragments[0].start!==e.details.fragments[0].start&&Qr(e.details,i)}else this.startFragRequested=!0,super.loadFragment(t,e,i);else this.clearTrackerIfNeeded(t)}flushAudioIfNeeded(t){const{media:e,bufferedTrack:i}=this,s=null==i?void 0:i.attrs,r=t.attrs;e&&s&&(s.CHANNELS!==r.CHANNELS||i.name!==t.name||i.lang!==t.lang)&&(this.log("Switching audio track : flushing all audio"),super.flushMainBuffer(0,Number.POSITIVE_INFINITY,"audio"),this.bufferedTrack=null)}completeAudioSwitch(t){const{hls:e}=this;this.flushAudioIfNeeded(t),this.bufferedTrack=t,this.switchingTrack=null,e.trigger(Gt.AUDIO_TRACK_SWITCHED,xt({},t))}}class za extends gr{constructor(t){super(t,"[audio-track-controller]"),this.tracks=[],this.groupIds=null,this.tracksInGroup=[],this.trackId=-1,this.currentTrack=null,this.selectDefaultTrack=!0,this.registerListeners()}registerListeners(){const{hls:t}=this;t.on(Gt.MANIFEST_LOADING,this.onManifestLoading,this),t.on(Gt.MANIFEST_PARSED,this.onManifestParsed,this),t.on(Gt.LEVEL_LOADING,this.onLevelLoading,this),t.on(Gt.LEVEL_SWITCHING,this.onLevelSwitching,this),t.on(Gt.AUDIO_TRACK_LOADED,this.onAudioTrackLoaded,this),t.on(Gt.ERROR,this.onError,this)}unregisterListeners(){const{hls:t}=this;t.off(Gt.MANIFEST_LOADING,this.onManifestLoading,this),t.off(Gt.MANIFEST_PARSED,this.onManifestParsed,this),t.off(Gt.LEVEL_LOADING,this.onLevelLoading,this),t.off(Gt.LEVEL_SWITCHING,this.onLevelSwitching,this),t.off(Gt.AUDIO_TRACK_LOADED,this.onAudioTrackLoaded,this),t.off(Gt.ERROR,this.onError,this)}destroy(){this.unregisterListeners(),this.tracks.length=0,this.tracksInGroup.length=0,this.currentTrack=null,super.destroy()}onManifestLoading(){this.tracks=[],this.tracksInGroup=[],this.groupIds=null,this.currentTrack=null,this.trackId=-1,this.selectDefaultTrack=!0}onManifestParsed(t,e){this.tracks=e.audioTracks||[]}onAudioTrackLoaded(t,e){const{id:i,groupId:s,details:r}=e,n=this.tracksInGroup[i];if(!n||n.groupId!==s)return void this.warn(`Audio track with id:${i} and group:${s} not found in active group ${null==n?void 0:n.groupId}`);const a=n.details;n.details=e.details,this.log(`Audio track ${i} "${n.name}" lang:${n.lang} group:${s} loaded [${r.startSN}-${r.endSN}]`),i===this.trackId&&this.playlistLoaded(i,e,a)}onLevelLoading(t,e){this.switchLevel(e.level)}onLevelSwitching(t,e){this.switchLevel(e.level)}switchLevel(t){const e=this.hls.levels[t];if(!e)return;const i=e.audioGroups||null,s=this.groupIds;let r=this.currentTrack;if(!i||(null==s?void 0:s.length)!==(null==i?void 0:i.length)||null!=i&&i.some(t=>-1===(null==s?void 0:s.indexOf(t)))){this.groupIds=i,this.trackId=-1,this.currentTrack=null;const t=this.tracks.filter(t=>!i||-1!==i.indexOf(t.groupId));if(t.length)this.selectDefaultTrack&&!t.some(t=>t.default)&&(this.selectDefaultTrack=!1),t.forEach((t,e)=>{t.id=e});else if(!r&&!this.tracksInGroup.length)return;this.tracksInGroup=t;const e=this.hls.config.audioPreference;if(!r&&e){const i=kr(e,t,_r);if(i>-1)r=t[i];else{const t=kr(e,this.tracks);r=this.tracks[t]}}let s=this.findTrackId(r);-1===s&&r&&(s=this.findTrackId(null));const a={audioTracks:t};this.log(`Updating audio tracks, ${t.length} track(s) found in group(s): ${null==i?void 0:i.join(",")}`),this.hls.trigger(Gt.AUDIO_TRACKS_UPDATED,a);const o=this.trackId;if(-1!==s&&-1===o)this.setAudioTrack(s);else if(t.length&&-1===o){var n;const e=new Error(`No audio track selected for current audio group-ID(s): ${null==(n=this.groupIds)?void 0:n.join(",")} track count: ${t.length}`);this.warn(e.message),this.hls.trigger(Gt.ERROR,{type:jt.MEDIA_ERROR,details:Kt.AUDIO_TRACK_LOAD_ERROR,fatal:!0,error:e})}}else this.shouldReloadPlaylist(r)&&this.setAudioTrack(this.trackId)}onError(t,e){!e.fatal&&e.context&&(e.context.type!==hs.AUDIO_TRACK||e.context.id!==this.trackId||this.groupIds&&-1===this.groupIds.indexOf(e.context.groupId)||(this.requestScheduled=-1,this.checkRetry(e)))}get allAudioTracks(){return this.tracks}get audioTracks(){return this.tracksInGroup}get audioTrack(){return this.trackId}set audioTrack(t){this.selectDefaultTrack=!1,this.setAudioTrack(t)}setAudioOption(t){const e=this.hls;if(e.config.audioPreference=t,t){const i=this.allAudioTracks;if(this.selectDefaultTrack=!1,i.length){const s=this.currentTrack;if(s&&wr(t,s,_r))return s;const r=kr(t,this.tracksInGroup,_r);if(r>-1){const t=this.tracksInGroup[r];return this.setAudioTrack(r),t}if(s){let s=e.loadLevel;-1===s&&(s=e.firstAutoLevel);const r=Or(t,e.levels,i,s,_r);if(-1===r)return null;e.nextLoadLevel=r}if(t.channels||t.audioCodec){const e=kr(t,i);if(e>-1)return i[e]}}}return null}setAudioTrack(t){const e=this.tracksInGroup;if(t<0||t>=e.length)return void this.warn("Invalid audio track id: "+t);this.clearTimer(),this.selectDefaultTrack=!1;const i=this.currentTrack,s=e[t],r=s.details&&!s.details.live;if(t===this.trackId&&s===i&&r)return;if(this.log(`Switching to audio-track ${t} "${s.name}" lang:${s.lang} group:${s.groupId} channels:${s.channels}`),this.trackId=t,this.currentTrack=s,this.hls.trigger(Gt.AUDIO_TRACK_SWITCHING,xt({},s)),r)return;const n=this.switchParams(s.url,null==i?void 0:i.details,s.details);this.loadPlaylist(n)}findTrackId(t){const e=this.tracksInGroup;for(let i=0;i<e.length;i++){const s=e[i];if((!this.selectDefaultTrack||s.default)&&(!t||wr(t,s,_r)))return i}if(t){const{name:i,lang:s,assocLang:r,characteristics:n,audioCodec:a,channels:o}=t;for(let t=0;t<e.length;t++){const l=e[t];if(wr({name:i,lang:s,assocLang:r,characteristics:n,audioCodec:a,channels:o},l,_r))return t}for(let l=0;l<e.length;l++){const i=e[l];if(Ha(t.attrs,i.attrs,["LANGUAGE","ASSOC-LANGUAGE","CHARACTERISTICS"]))return l}for(let l=0;l<e.length;l++){const i=e[l];if(Ha(t.attrs,i.attrs,["LANGUAGE"]))return l}}return-1}loadPlaylist(t){const e=this.currentTrack;if(this.shouldLoadPlaylist(e)&&e){super.loadPlaylist();const s=e.id,r=e.groupId;let n=e.url;if(t)try{n=t.addDirectives(n)}catch(i){this.warn("Could not construct new URL with HLS Delivery Directives: "+i)}this.log(`loading audio-track playlist ${s} "${e.name}" lang:${e.lang} group:${r}`),this.clearTimer(),this.hls.trigger(Gt.AUDIO_TRACK_LOADING,{url:n,id:s,groupId:r,deliveryDirectives:t||null})}}}const Xa=500;class Qa extends un{constructor(t,e,i){super(t,e,i,"[subtitle-stream-controller]",cs.SUBTITLE),this.currentTrackId=-1,this.tracksBuffered=[],this.mainDetails=null,this._registerListeners()}onHandlerDestroying(){this._unregisterListeners(),super.onHandlerDestroying(),this.mainDetails=null}_registerListeners(){const{hls:t}=this;t.on(Gt.MEDIA_ATTACHED,this.onMediaAttached,this),t.on(Gt.MEDIA_DETACHING,this.onMediaDetaching,this),t.on(Gt.MANIFEST_LOADING,this.onManifestLoading,this),t.on(Gt.LEVEL_LOADED,this.onLevelLoaded,this),t.on(Gt.ERROR,this.onError,this),t.on(Gt.SUBTITLE_TRACKS_UPDATED,this.onSubtitleTracksUpdated,this),t.on(Gt.SUBTITLE_TRACK_SWITCH,this.onSubtitleTrackSwitch,this),t.on(Gt.SUBTITLE_TRACK_LOADED,this.onSubtitleTrackLoaded,this),t.on(Gt.SUBTITLE_FRAG_PROCESSED,this.onSubtitleFragProcessed,this),t.on(Gt.BUFFER_FLUSHING,this.onBufferFlushing,this),t.on(Gt.FRAG_BUFFERED,this.onFragBuffered,this)}_unregisterListeners(){const{hls:t}=this;t.off(Gt.MEDIA_ATTACHED,this.onMediaAttached,this),t.off(Gt.MEDIA_DETACHING,this.onMediaDetaching,this),t.off(Gt.MANIFEST_LOADING,this.onManifestLoading,this),t.off(Gt.LEVEL_LOADED,this.onLevelLoaded,this),t.off(Gt.ERROR,this.onError,this),t.off(Gt.SUBTITLE_TRACKS_UPDATED,this.onSubtitleTracksUpdated,this),t.off(Gt.SUBTITLE_TRACK_SWITCH,this.onSubtitleTrackSwitch,this),t.off(Gt.SUBTITLE_TRACK_LOADED,this.onSubtitleTrackLoaded,this),t.off(Gt.SUBTITLE_FRAG_PROCESSED,this.onSubtitleFragProcessed,this),t.off(Gt.BUFFER_FLUSHING,this.onBufferFlushing,this),t.off(Gt.FRAG_BUFFERED,this.onFragBuffered,this)}startLoad(t){this.stopLoad(),this.state=dn.IDLE,this.setInterval(Xa),this.nextLoadPosition=this.startPosition=this.lastCurrentTime=t,this.tick()}onManifestLoading(){this.mainDetails=null,this.fragmentTracker.removeAllFragments()}onMediaDetaching(){this.tracksBuffered=[],super.onMediaDetaching()}onLevelLoaded(t,e){this.mainDetails=e.details}onSubtitleFragProcessed(t,e){const{frag:i,success:s}=e;if(this.fragPrevious=i,this.state=dn.IDLE,!s)return;const r=this.tracksBuffered[this.currentTrackId];if(!r)return;let n;const a=i.start;for(let l=0;l<r.length;l++)if(a>=r[l].start&&a<=r[l].end){n=r[l];break}const o=i.start+i.duration;n?n.end=o:(n={start:a,end:o},r.push(n)),this.fragmentTracker.fragBuffered(i),this.fragBufferedComplete(i,null)}onBufferFlushing(t,e){const{startOffset:i,endOffset:s}=e;if(0===i&&s!==Number.POSITIVE_INFINITY){const t=s-1;if(t<=0)return;e.endOffsetSubtitles=Math.max(0,t),this.tracksBuffered.forEach(e=>{for(let i=0;i<e.length;)if(e[i].end<=t)e.shift();else{if(!(e[i].start<t))break;e[i].start=t,i++}}),this.fragmentTracker.removeFragmentsInRange(i,t,cs.SUBTITLE)}}onFragBuffered(t,e){var i;this.loadedmetadata||e.frag.type!==cs.MAIN||null!=(i=this.media)&&i.buffered.length&&(this.loadedmetadata=!0)}onError(t,e){const i=e.frag;(null==i?void 0:i.type)===cs.SUBTITLE&&(e.details===Kt.FRAG_GAP&&this.fragmentTracker.fragBuffered(i,!0),this.fragCurrent&&this.fragCurrent.abortRequests(),this.state!==dn.STOPPED&&(this.state=dn.IDLE))}onSubtitleTracksUpdated(t,{subtitleTracks:e}){this.levels&&Va(this.levels,e)?this.levels=e.map(t=>new Ns(t)):(this.tracksBuffered=[],this.levels=e.map(t=>{const e=new Ns(t);return this.tracksBuffered[e.id]=[],e}),this.fragmentTracker.removeFragmentsInRange(0,Number.POSITIVE_INFINITY,cs.SUBTITLE),this.fragPrevious=null,this.mediaBuffer=null)}onSubtitleTrackSwitch(t,e){var i;if(this.currentTrackId=e.id,null==(i=this.levels)||!i.length||-1===this.currentTrackId)return void this.clearInterval();const s=this.levels[this.currentTrackId];null!=s&&s.details?this.mediaBuffer=this.mediaBufferTimeRanges:this.mediaBuffer=null,s&&this.setInterval(Xa)}onSubtitleTrackLoaded(t,e){var i;const{currentTrackId:s,levels:r}=this,{details:n,id:a}=e;if(!r)return void this.warn("Subtitle tracks were reset while loading level "+a);const o=r[a];if(a>=r.length||!o)return;this.log(`Subtitle track ${a} loaded [${n.startSN},${n.endSN}]${n.lastPartSn?`[part-${n.lastPartSn}-${n.lastPartIndex}]`:""},duration:${n.totalduration}`),this.mediaBuffer=this.mediaBufferTimeRanges;let l=0;if(n.live||null!=(i=o.details)&&i.live){const t=this.mainDetails;if(n.deltaUpdateFailed||!t)return;const e=t.fragments[0];var h;if(o.details)l=this.alignPlaylists(n,o.details,null==(h=this.levelLastLoaded)?void 0:h.details),0===l&&e&&(l=e.start,qs(n,l));else n.hasProgramDateTime&&t.hasProgramDateTime?(Qr(n,t),l=n.fragments[0].start):e&&(l=e.start,qs(n,l))}if(o.details=n,this.levelLastLoaded=o,a===s&&(this.startFragRequested||!this.mainDetails&&n.live||this.setStartPosition(this.mainDetails||n,l),this.tick(),n.live&&!this.fragCurrent&&this.media&&this.state===dn.IDLE)){const t=ar(null,n.fragments,this.media.currentTime,0);t||(this.warn("Subtitle playlist not aligned with playback"),o.details=void 0)}}_handleFragmentLoadComplete(t){const{frag:e,payload:i}=t,s=e.decryptdata,r=this.hls;if(!this.fragContextChanged(e)&&i&&i.byteLength>0&&null!=s&&s.key&&s.iv&&"AES-128"===s.method){const t=performance.now();this.decrypter.decrypt(new Uint8Array(i),s.key.buffer,s.iv.buffer).catch(t=>{throw r.trigger(Gt.ERROR,{type:jt.MEDIA_ERROR,details:Kt.FRAG_DECRYPT_ERROR,fatal:!1,error:t,reason:t.message,frag:e}),t}).then(i=>{const s=performance.now();r.trigger(Gt.FRAG_DECRYPTED,{frag:e,payload:i,stats:{tstart:t,tdecrypt:s}})}).catch(t=>{this.warn(`${t.name}: ${t.message}`),this.state=dn.IDLE})}}doTick(){if(this.media){if(this.state===dn.IDLE){const{currentTrackId:t,levels:e}=this,i=null==e?void 0:e[t];if(!i||!e.length||!i.details)return;const{config:s}=this,r=this.getLoadPosition(),n=Gr.bufferedInfo(this.tracksBuffered[this.currentTrackId]||[],r,s.maxBufferHole),{end:a,len:o}=n,l=this.getFwdBufferInfo(this.media,cs.MAIN),h=i.details,c=this.getMaxBufferLength(null==l?void 0:l.len)+h.levelTargetDuration;if(o>c)return;const d=h.fragments,u=d.length,f=h.edge;let g=null;const m=this.fragPrevious;if(a<f){const t=s.maxFragLookUpTolerance,e=a>f-t?0:t;g=ar(m,d,Math.max(d[0].start,a),e),!g&&m&&m.start<d[0].start&&(g=d[0])}else g=d[u-1];if(!g)return;if(g=this.mapToInitFragWhenRequired(g),"initSegment"!==g.sn){const t=g.sn-h.startSN,e=d[t-1];e&&e.cc===g.cc&&this.fragmentTracker.getState(e)===Fr.NOT_LOADED&&(g=e)}this.fragmentTracker.getState(g)===Fr.NOT_LOADED&&this.loadFragment(g,i,a)}}else this.state=dn.IDLE}getMaxBufferLength(t){const e=super.getMaxBufferLength();return t?Math.max(e,t):e}loadFragment(t,e,i){this.fragCurrent=t,"initSegment"===t.sn?this._loadInitSegment(t,e):(this.startFragRequested=!0,super.loadFragment(t,e,i))}get mediaBufferTimeRanges(){return new Ja(this.tracksBuffered[this.currentTrackId]||[])}}class Ja{constructor(t){this.buffered=void 0;const e=(e,i,s)=>{if(i>>>=0,i>s-1)throw new DOMException(`Failed to execute '${e}' on 'TimeRanges': The index provided (${i}) is greater than the maximum bound (${s})`);return t[i][e]};this.buffered={get length(){return t.length},end(i){return e("end",i,t.length)},start(i){return e("start",i,t.length)}}}}class Za extends gr{constructor(t){super(t,"[subtitle-track-controller]"),this.media=null,this.tracks=[],this.groupIds=null,this.tracksInGroup=[],this.trackId=-1,this.currentTrack=null,this.selectDefaultTrack=!0,this.queuedDefaultTrack=-1,this.asyncPollTrackChange=()=>this.pollTrackChange(0),this.useTextTrackPolling=!1,this.subtitlePollingInterval=-1,this._subtitleDisplay=!0,this.onTextTracksChanged=()=>{if(this.useTextTrackPolling||self.clearInterval(this.subtitlePollingInterval),!this.media||!this.hls.config.renderTextTracksNatively)return;let t=null;const e=Ts(this.media.textTracks);for(let s=0;s<e.length;s++)if("hidden"===e[s].mode)t=e[s];else if("showing"===e[s].mode){t=e[s];break}const i=this.findTrackForTextTrack(t);this.subtitleTrack!==i&&this.setSubtitleTrack(i)},this.registerListeners()}destroy(){this.unregisterListeners(),this.tracks.length=0,this.tracksInGroup.length=0,this.currentTrack=null,this.onTextTracksChanged=this.asyncPollTrackChange=null,super.destroy()}get subtitleDisplay(){return this._subtitleDisplay}set subtitleDisplay(t){this._subtitleDisplay=t,this.trackId>-1&&this.toggleTrackModes()}registerListeners(){const{hls:t}=this;t.on(Gt.MEDIA_ATTACHED,this.onMediaAttached,this),t.on(Gt.MEDIA_DETACHING,this.onMediaDetaching,this),t.on(Gt.MANIFEST_LOADING,this.onManifestLoading,this),t.on(Gt.MANIFEST_PARSED,this.onManifestParsed,this),t.on(Gt.LEVEL_LOADING,this.onLevelLoading,this),t.on(Gt.LEVEL_SWITCHING,this.onLevelSwitching,this),t.on(Gt.SUBTITLE_TRACK_LOADED,this.onSubtitleTrackLoaded,this),t.on(Gt.ERROR,this.onError,this)}unregisterListeners(){const{hls:t}=this;t.off(Gt.MEDIA_ATTACHED,this.onMediaAttached,this),t.off(Gt.MEDIA_DETACHING,this.onMediaDetaching,this),t.off(Gt.MANIFEST_LOADING,this.onManifestLoading,this),t.off(Gt.MANIFEST_PARSED,this.onManifestParsed,this),t.off(Gt.LEVEL_LOADING,this.onLevelLoading,this),t.off(Gt.LEVEL_SWITCHING,this.onLevelSwitching,this),t.off(Gt.SUBTITLE_TRACK_LOADED,this.onSubtitleTrackLoaded,this),t.off(Gt.ERROR,this.onError,this)}onMediaAttached(t,e){this.media=e.media,this.media&&(this.queuedDefaultTrack>-1&&(this.subtitleTrack=this.queuedDefaultTrack,this.queuedDefaultTrack=-1),this.useTextTrackPolling=!(this.media.textTracks&&"onchange"in this.media.textTracks),this.useTextTrackPolling?this.pollTrackChange(500):this.media.textTracks.addEventListener("change",this.asyncPollTrackChange))}pollTrackChange(t){self.clearInterval(this.subtitlePollingInterval),this.subtitlePollingInterval=self.setInterval(this.onTextTracksChanged,t)}onMediaDetaching(){if(!this.media)return;self.clearInterval(this.subtitlePollingInterval),this.useTextTrackPolling||this.media.textTracks.removeEventListener("change",this.asyncPollTrackChange),this.trackId>-1&&(this.queuedDefaultTrack=this.trackId);const t=Ts(this.media.textTracks);t.forEach(t=>{ps(t)}),this.subtitleTrack=-1,this.media=null}onManifestLoading(){this.tracks=[],this.groupIds=null,this.tracksInGroup=[],this.trackId=-1,this.currentTrack=null,this.selectDefaultTrack=!0}onManifestParsed(t,e){this.tracks=e.subtitleTracks}onSubtitleTrackLoaded(t,e){const{id:i,groupId:s,details:r}=e,n=this.tracksInGroup[i];if(!n||n.groupId!==s)return void this.warn(`Subtitle track with id:${i} and group:${s} not found in active group ${null==n?void 0:n.groupId}`);const a=n.details;n.details=e.details,this.log(`Subtitle track ${i} "${n.name}" lang:${n.lang} group:${s} loaded [${r.startSN}-${r.endSN}]`),i===this.trackId&&this.playlistLoaded(i,e,a)}onLevelLoading(t,e){this.switchLevel(e.level)}onLevelSwitching(t,e){this.switchLevel(e.level)}switchLevel(t){const e=this.hls.levels[t];if(!e)return;const i=e.subtitleGroups||null,s=this.groupIds;let r=this.currentTrack;if(!i||(null==s?void 0:s.length)!==(null==i?void 0:i.length)||null!=i&&i.some(t=>-1===(null==s?void 0:s.indexOf(t)))){this.groupIds=i,this.trackId=-1,this.currentTrack=null;const t=this.tracks.filter(t=>!i||-1!==i.indexOf(t.groupId));if(t.length)this.selectDefaultTrack&&!t.some(t=>t.default)&&(this.selectDefaultTrack=!1),t.forEach((t,e)=>{t.id=e});else if(!r&&!this.tracksInGroup.length)return;this.tracksInGroup=t;const e=this.hls.config.subtitlePreference;if(!r&&e){this.selectDefaultTrack=!1;const i=kr(e,t);if(i>-1)r=t[i];else{const t=kr(e,this.tracks);r=this.tracks[t]}}let s=this.findTrackId(r);-1===s&&r&&(s=this.findTrackId(null));const n={subtitleTracks:t};this.log(`Updating subtitle tracks, ${t.length} track(s) found in "${null==i?void 0:i.join(",")}" group-id`),this.hls.trigger(Gt.SUBTITLE_TRACKS_UPDATED,n),-1!==s&&-1===this.trackId&&this.setSubtitleTrack(s)}else this.shouldReloadPlaylist(r)&&this.setSubtitleTrack(this.trackId)}findTrackId(t){const e=this.tracksInGroup,i=this.selectDefaultTrack;for(let s=0;s<e.length;s++){const r=e[s];if((!i||r.default)&&(i||t)&&(!t||wr(r,t)))return s}if(t){for(let i=0;i<e.length;i++){const s=e[i];if(Ha(t.attrs,s.attrs,["LANGUAGE","ASSOC-LANGUAGE","CHARACTERISTICS"]))return i}for(let i=0;i<e.length;i++){const s=e[i];if(Ha(t.attrs,s.attrs,["LANGUAGE"]))return i}}return-1}findTrackForTextTrack(t){if(t){const e=this.tracksInGroup;for(let i=0;i<e.length;i++){const s=e[i];if(qa(s,t))return i}}return-1}onError(t,e){!e.fatal&&e.context&&(e.context.type!==hs.SUBTITLE_TRACK||e.context.id!==this.trackId||this.groupIds&&-1===this.groupIds.indexOf(e.context.groupId)||this.checkRetry(e))}get allSubtitleTracks(){return this.tracks}get subtitleTracks(){return this.tracksInGroup}get subtitleTrack(){return this.trackId}set subtitleTrack(t){this.selectDefaultTrack=!1,this.setSubtitleTrack(t)}setSubtitleOption(t){if(this.hls.config.subtitlePreference=t,t){const e=this.allSubtitleTracks;if(this.selectDefaultTrack=!1,e.length){const i=this.currentTrack;if(i&&wr(t,i))return i;const s=kr(t,this.tracksInGroup);if(s>-1){const t=this.tracksInGroup[s];return this.setSubtitleTrack(s),t}if(i)return null;{const i=kr(t,e);if(i>-1)return e[i]}}}return null}loadPlaylist(t){super.loadPlaylist();const e=this.currentTrack;if(this.shouldLoadPlaylist(e)&&e){const s=e.id,r=e.groupId;let n=e.url;if(t)try{n=t.addDirectives(n)}catch(i){this.warn("Could not construct new URL with HLS Delivery Directives: "+i)}this.log("Loading subtitle playlist for id "+s),this.hls.trigger(Gt.SUBTITLE_TRACK_LOADING,{url:n,id:s,groupId:r,deliveryDirectives:t||null})}}toggleTrackModes(){const{media:t}=this;if(!t)return;const e=Ts(t.textTracks),i=this.currentTrack;let s;if(i&&(s=e.filter(t=>qa(i,t))[0],s||this.warn(`Unable to find subtitle TextTrack with name "${i.name}" and language "${i.lang}"`)),[].slice.call(e).forEach(t=>{"disabled"!==t.mode&&t!==s&&(t.mode="disabled")}),s){const t=this.subtitleDisplay?"showing":"hidden";s.mode!==t&&(s.mode=t)}}setSubtitleTrack(t){const e=this.tracksInGroup;if(!this.media)return void(this.queuedDefaultTrack=t);if(t<-1||t>=e.length||!Ut(t))return void this.warn("Invalid subtitle track id: "+t);this.clearTimer(),this.selectDefaultTrack=!1;const i=this.currentTrack,s=e[t]||null;if(this.trackId=t,this.currentTrack=s,this.toggleTrackModes(),!s)return void this.hls.trigger(Gt.SUBTITLE_TRACK_SWITCH,{id:t});const r=!!s.details&&!s.details.live;if(t===this.trackId&&s===i&&r)return;this.log("Switching to subtitle-track "+t+(s?` "${s.name}" lang:${s.lang} group:${s.groupId}`:""));const{id:n,groupId:a="",name:o,type:l,url:h}=s;this.hls.trigger(Gt.SUBTITLE_TRACK_SWITCH,{id:n,groupId:a,name:o,type:l,url:h});const c=this.switchParams(s.url,null==i?void 0:i.details,s.details);this.loadPlaylist(c)}}class to{constructor(t){this.buffers=void 0,this.queues={video:[],audio:[],audiovideo:[]},this.buffers=t}append(t,e,i){const s=this.queues[e];s.push(t),1!==s.length||i||this.executeNext(e)}insertAbort(t,e){const i=this.queues[e];i.unshift(t),this.executeNext(e)}appendBlocker(t){let e;const i=new Promise(t=>{e=t}),s={execute:e,onStart:()=>{},onComplete:()=>{},onError:()=>{}};return this.append(s,t),i}executeNext(t){const e=this.queues[t];if(e.length){const s=e[0];try{s.execute()}catch(i){Xt.warn(`[buffer-operation-queue]: Exception executing "${t}" SourceBuffer operation: ${i}`),s.onError(i);const e=this.buffers[t];null!=e&&e.updating||this.shiftAndExecuteNext(t)}}}shiftAndExecuteNext(t){this.queues[t].shift(),this.executeNext(t)}current(t){return this.queues[t][0]}}const eo=/(avc[1234]|hvc1|hev1|dvh[1e]|vp09|av01)(?:\.[^.,]+)+/;class io{constructor(t){this.details=null,this._objectUrl=null,this.operationQueue=void 0,this.listeners=void 0,this.hls=void 0,this.bufferCodecEventsExpected=0,this._bufferCodecEventsTotal=0,this.media=null,this.mediaSource=null,this.lastMpegAudioChunk=null,this.appendSource=void 0,this.appendErrors={audio:0,video:0,audiovideo:0},this.tracks={},this.pendingTracks={},this.sourceBuffer=void 0,this.log=void 0,this.warn=void 0,this.error=void 0,this._onEndStreaming=t=>{this.hls&&this.hls.pauseBuffering()},this._onStartStreaming=t=>{this.hls&&this.hls.resumeBuffering()},this._onMediaSourceOpen=()=>{const{media:t,mediaSource:e}=this;this.log("Media source opened"),t&&(t.removeEventListener("emptied",this._onMediaEmptied),this.updateMediaElementDuration(),this.hls.trigger(Gt.MEDIA_ATTACHED,{media:t,mediaSource:e})),e&&e.removeEventListener("sourceopen",this._onMediaSourceOpen),this.checkPendingTracks()},this._onMediaSourceClose=()=>{this.log("Media source closed")},this._onMediaSourceEnded=()=>{this.log("Media source ended")},this._onMediaEmptied=()=>{const{mediaSrc:t,_objectUrl:e}=this;t!==e&&Xt.error(`Media element src was set while attaching MediaSource (${e} > ${t})`)},this.hls=t;const e="[buffer-controller]";this.appendSource=Mi(Pi(t.config.preferManagedMediaSource)),this.log=Xt.log.bind(Xt,e),this.warn=Xt.warn.bind(Xt,e),this.error=Xt.error.bind(Xt,e),this._initSourceBuffer(),this.registerListeners()}hasSourceTypes(){return this.getSourceBufferTypes().length>0||Object.keys(this.pendingTracks).length>0}destroy(){this.unregisterListeners(),this.details=null,this.lastMpegAudioChunk=null,this.hls=null}registerListeners(){const{hls:t}=this;t.on(Gt.MEDIA_ATTACHING,this.onMediaAttaching,this),t.on(Gt.MEDIA_DETACHING,this.onMediaDetaching,this),t.on(Gt.MANIFEST_LOADING,this.onManifestLoading,this),t.on(Gt.MANIFEST_PARSED,this.onManifestParsed,this),t.on(Gt.BUFFER_RESET,this.onBufferReset,this),t.on(Gt.BUFFER_APPENDING,this.onBufferAppending,this),t.on(Gt.BUFFER_CODECS,this.onBufferCodecs,this),t.on(Gt.BUFFER_EOS,this.onBufferEos,this),t.on(Gt.BUFFER_FLUSHING,this.onBufferFlushing,this),t.on(Gt.LEVEL_UPDATED,this.onLevelUpdated,this),t.on(Gt.FRAG_PARSED,this.onFragParsed,this),t.on(Gt.FRAG_CHANGED,this.onFragChanged,this)}unregisterListeners(){const{hls:t}=this;t.off(Gt.MEDIA_ATTACHING,this.onMediaAttaching,this),t.off(Gt.MEDIA_DETACHING,this.onMediaDetaching,this),t.off(Gt.MANIFEST_LOADING,this.onManifestLoading,this),t.off(Gt.MANIFEST_PARSED,this.onManifestParsed,this),t.off(Gt.BUFFER_RESET,this.onBufferReset,this),t.off(Gt.BUFFER_APPENDING,this.onBufferAppending,this),t.off(Gt.BUFFER_CODECS,this.onBufferCodecs,this),t.off(Gt.BUFFER_EOS,this.onBufferEos,this),t.off(Gt.BUFFER_FLUSHING,this.onBufferFlushing,this),t.off(Gt.LEVEL_UPDATED,this.onLevelUpdated,this),t.off(Gt.FRAG_PARSED,this.onFragParsed,this),t.off(Gt.FRAG_CHANGED,this.onFragChanged,this)}_initSourceBuffer(){this.sourceBuffer={},this.operationQueue=new to(this.sourceBuffer),this.listeners={audio:[],video:[],audiovideo:[]},this.appendErrors={audio:0,video:0,audiovideo:0},this.lastMpegAudioChunk=null}onManifestLoading(){this.bufferCodecEventsExpected=this._bufferCodecEventsTotal=0,this.details=null}onManifestParsed(t,e){let i=2;(e.audio&&!e.video||!e.altAudio)&&(i=1),this.bufferCodecEventsExpected=this._bufferCodecEventsTotal=i,this.log(this.bufferCodecEventsExpected+" bufferCodec event(s) expected")}onMediaAttaching(t,e){const i=this.media=e.media,s=Pi(this.appendSource);if(i&&s){var r;const t=this.mediaSource=new s;this.log("created media source: "+(null==(r=t.constructor)?void 0:r.name)),t.addEventListener("sourceopen",this._onMediaSourceOpen),t.addEventListener("sourceended",this._onMediaSourceEnded),t.addEventListener("sourceclose",this._onMediaSourceClose),this.appendSource&&(t.addEventListener("startstreaming",this._onStartStreaming),t.addEventListener("endstreaming",this._onEndStreaming));const e=this._objectUrl=self.URL.createObjectURL(t);if(this.appendSource)try{i.removeAttribute("src");const s=self.ManagedMediaSource;i.disableRemotePlayback=i.disableRemotePlayback||s&&t instanceof s,so(i),ro(i,e),i.load()}catch(n){i.src=e}else i.src=e;i.addEventListener("emptied",this._onMediaEmptied)}}onMediaDetaching(){const{media:t,mediaSource:e,_objectUrl:i}=this;if(e){if(this.log("media source detaching"),"open"===e.readyState)try{e.endOfStream()}catch(nc){this.warn(`onMediaDetaching: ${nc.message} while calling endOfStream`)}this.onBufferReset(),e.removeEventListener("sourceopen",this._onMediaSourceOpen),e.removeEventListener("sourceended",this._onMediaSourceEnded),e.removeEventListener("sourceclose",this._onMediaSourceClose),this.appendSource&&(e.removeEventListener("startstreaming",this._onStartStreaming),e.removeEventListener("endstreaming",this._onEndStreaming)),t&&(t.removeEventListener("emptied",this._onMediaEmptied),i&&self.URL.revokeObjectURL(i),this.mediaSrc===i?(t.removeAttribute("src"),this.appendSource&&so(t),t.load()):this.warn("media|source.src was changed by a third party - skip cleanup")),this.mediaSource=null,this.media=null,this._objectUrl=null,this.bufferCodecEventsExpected=this._bufferCodecEventsTotal,this.pendingTracks={},this.tracks={}}this.hls.trigger(Gt.MEDIA_DETACHED,void 0)}onBufferReset(){this.getSourceBufferTypes().forEach(t=>{this.resetBuffer(t)}),this._initSourceBuffer()}resetBuffer(t){const e=this.sourceBuffer[t];try{var i;if(e)this.removeBufferListeners(t),this.sourceBuffer[t]=void 0,null!=(i=this.mediaSource)&&i.sourceBuffers.length&&this.mediaSource.removeSourceBuffer(e)}catch(nc){this.warn("onBufferReset "+t,nc)}}onBufferCodecs(t,e){const i=this.getSourceBufferTypes().length,s=Object.keys(e);if(s.forEach(t=>{if(i){const i=this.tracks[t];if(i&&"function"===typeof i.buffer.changeType){var s;const{id:r,codec:n,levelCodec:a,container:o,metadata:l}=e[t],h=Yi(i.codec,i.levelCodec),c=null==h?void 0:h.replace(eo,"$1");let d=Yi(n,a);const u=null==(s=d)?void 0:s.replace(eo,"$1");if(d&&c!==u){"audio"===t.slice(0,5)&&(d=qi(d,this.appendSource));const e=`${o};codecs=${d}`;this.appendChangeType(t,e),this.log(`switching codec ${h} to ${d}`),this.tracks[t]={buffer:i.buffer,codec:n,container:o,levelCodec:a,metadata:l,id:r}}}}else this.pendingTracks[t]=e[t]}),i)return;const r=Math.max(this.bufferCodecEventsExpected-1,0);this.bufferCodecEventsExpected!==r&&(this.log(`${r} bufferCodec event(s) expected ${s.join(",")}`),this.bufferCodecEventsExpected=r),this.mediaSource&&"open"===this.mediaSource.readyState&&this.checkPendingTracks()}appendChangeType(t,e){const{operationQueue:i}=this,s={execute:()=>{const s=this.sourceBuffer[t];s&&(this.log(`changing ${t} sourceBuffer type to ${e}`),s.changeType(e)),i.shiftAndExecuteNext(t)},onStart:()=>{},onComplete:()=>{},onError:e=>{this.warn(`Failed to change ${t} SourceBuffer type`,e)}};i.append(s,t,!!this.pendingTracks[t])}onBufferAppending(t,e){const{hls:i,operationQueue:s,tracks:r}=this,{data:n,type:a,frag:o,part:l,chunkMeta:h}=e,c=h.buffering[a],d=self.performance.now();c.start=d;const u=o.stats.buffering,f=l?l.stats.buffering:null;0===u.start&&(u.start=d),f&&0===f.start&&(f.start=d);const g=r.audio;let m=!1;"audio"===a&&"audio/mpeg"===(null==g?void 0:g.container)&&(m=!this.lastMpegAudioChunk||1===h.id||this.lastMpegAudioChunk.sn!==h.sn,this.lastMpegAudioChunk=h);const p=o.start,v={execute:()=>{if(c.executeStart=self.performance.now(),m){const t=this.sourceBuffer[a];if(t){const e=p-t.timestampOffset;Math.abs(e)>=.1&&(this.log(`Updating audio SourceBuffer timestampOffset to ${p} (delta: ${e}) sn: ${o.sn})`),t.timestampOffset=p)}}this.appendExecutor(n,a)},onStart:()=>{},onComplete:()=>{const t=self.performance.now();c.executeEnd=c.end=t,0===u.first&&(u.first=t),f&&0===f.first&&(f.first=t);const{sourceBuffer:e}=this,i={};for(const s in e)i[s]=Gr.getBuffered(e[s]);this.appendErrors[a]=0,"audio"===a||"video"===a?this.appendErrors.audiovideo=0:(this.appendErrors.audio=0,this.appendErrors.video=0),this.hls.trigger(Gt.BUFFER_APPENDED,{type:a,frag:o,part:l,chunkMeta:h,parent:o.type,timeRanges:i})},onError:t=>{const e={type:jt.MEDIA_ERROR,parent:o.type,details:Kt.BUFFER_APPEND_ERROR,sourceBufferName:a,frag:o,part:l,chunkMeta:h,error:t,err:t,fatal:!1};if(t.code===DOMException.QUOTA_EXCEEDED_ERR)e.details=Kt.BUFFER_FULL_ERROR;else{const t=++this.appendErrors[a];e.details=Kt.BUFFER_APPEND_ERROR,this.warn(`Failed ${t}/${i.config.appendErrorMaxRetry} times to append segment in "${a}" sourceBuffer`),t>=i.config.appendErrorMaxRetry&&(e.fatal=!0)}i.trigger(Gt.ERROR,e)}};s.append(v,a,!!this.pendingTracks[a])}onBufferFlushing(t,e){const{operationQueue:i}=this,s=t=>({execute:this.removeExecutor.bind(this,t,e.startOffset,e.endOffset),onStart:()=>{},onComplete:()=>{this.hls.trigger(Gt.BUFFER_FLUSHED,{type:t})},onError:e=>{this.warn(`Failed to remove from ${t} SourceBuffer`,e)}});e.type?i.append(s(e.type),e.type):this.getSourceBufferTypes().forEach(t=>{i.append(s(t),t)})}onFragParsed(t,e){const{frag:i,part:s}=e,r=[],n=s?s.elementaryStreams:i.elementaryStreams;n[re.AUDIOVIDEO]?r.push("audiovideo"):(n[re.AUDIO]&&r.push("audio"),n[re.VIDEO]&&r.push("video"));const a=()=>{const t=self.performance.now();i.stats.buffering.end=t,s&&(s.stats.buffering.end=t);const e=s?s.stats:i.stats;this.hls.trigger(Gt.FRAG_BUFFERED,{frag:i,part:s,stats:e,id:i.type})};0===r.length&&this.warn(`Fragments must have at least one ElementaryStreamType set. type: ${i.type} level: ${i.level} sn: ${i.sn}`),this.blockBuffers(a,r)}onFragChanged(t,e){this.trimBuffers()}onBufferEos(t,e){const i=this.getSourceBufferTypes().reduce((t,i)=>{const s=this.sourceBuffer[i];return!s||e.type&&e.type!==i||(s.ending=!0,s.ended||(s.ended=!0,this.log(i+" sourceBuffer now EOS"))),t&&!(s&&!s.ended)},!0);i&&(this.log("Queueing mediaSource.endOfStream()"),this.blockBuffers(()=>{this.getSourceBufferTypes().forEach(t=>{const e=this.sourceBuffer[t];e&&(e.ending=!1)});const{mediaSource:t}=this;t&&"open"===t.readyState?(this.log("Calling mediaSource.endOfStream()"),t.endOfStream()):t&&this.log("Could not call mediaSource.endOfStream(). mediaSource.readyState: "+t.readyState)}))}onLevelUpdated(t,{details:e}){e.fragments.length&&(this.details=e,this.getSourceBufferTypes().length?this.blockBuffers(this.updateMediaElementDuration.bind(this)):this.updateMediaElementDuration())}trimBuffers(){const{hls:t,details:e,media:i}=this;if(!i||null===e)return;const s=this.getSourceBufferTypes();if(!s.length)return;const r=t.config,n=i.currentTime,a=e.levelTargetDuration,o=e.live&&null!==r.liveBackBufferLength?r.liveBackBufferLength:r.backBufferLength;if(Ut(o)&&o>0){const t=Math.max(o,a),e=Math.floor(n/a)*a-t;this.flushBackBuffer(n,a,e)}if(Ut(r.frontBufferFlushThreshold)&&r.frontBufferFlushThreshold>0){const t=Math.max(r.maxBufferLength,r.frontBufferFlushThreshold),e=Math.max(t,a),i=Math.floor(n/a)*a+e;this.flushFrontBuffer(n,a,i)}}flushBackBuffer(t,e,i){const{details:s,sourceBuffer:r}=this,n=this.getSourceBufferTypes();n.forEach(n=>{const a=r[n];if(a){const r=Gr.getBuffered(a);if(r.length>0&&i>r.start(0)){if(this.hls.trigger(Gt.BACK_BUFFER_REACHED,{bufferEnd:i}),null!=s&&s.live)this.hls.trigger(Gt.LIVE_BACK_BUFFER_REACHED,{bufferEnd:i});else if(a.ended&&r.end(r.length-1)-t<2*e)return void this.log(`Cannot flush ${n} back buffer while SourceBuffer is in ended state`);this.hls.trigger(Gt.BUFFER_FLUSHING,{startOffset:0,endOffset:i,type:n})}}})}flushFrontBuffer(t,e,i){const{sourceBuffer:s}=this,r=this.getSourceBufferTypes();r.forEach(r=>{const n=s[r];if(n){const s=Gr.getBuffered(n),a=s.length;if(a<2)return;const o=s.start(a-1),l=s.end(a-1);if(i>o||t>=o&&t<=l)return;if(n.ended&&t-l<2*e)return void this.log(`Cannot flush ${r} front buffer while SourceBuffer is in ended state`);this.hls.trigger(Gt.BUFFER_FLUSHING,{startOffset:o,endOffset:1/0,type:r})}})}updateMediaElementDuration(){if(!this.details||!this.media||!this.mediaSource||"open"!==this.mediaSource.readyState)return;const{details:t,hls:e,media:i,mediaSource:s}=this,r=t.fragments[0].start+t.totalduration,n=i.duration,a=Ut(s.duration)?s.duration:0;t.live&&e.config.liveDurationInfinity?(s.duration=1/0,this.updateSeekableRange(t)):(r>a&&r>n||!Ut(n))&&(this.log("Updating Media Source duration to "+r.toFixed(3)),s.duration=r)}updateSeekableRange(t){const e=this.mediaSource,i=t.fragments,s=i.length;if(s&&t.live&&null!=e&&e.setLiveSeekableRange){const s=Math.max(0,i[0].start),r=Math.max(s,s+t.totalduration);this.log(`Media Source duration is set to ${e.duration}. Setting seekable range to ${s}-${r}.`),e.setLiveSeekableRange(s,r)}}checkPendingTracks(){const{bufferCodecEventsExpected:t,operationQueue:e,pendingTracks:i}=this,s=Object.keys(i).length;if(s&&(!t||2===s||"audiovideo"in i)){this.createSourceBuffers(i),this.pendingTracks={};const t=this.getSourceBufferTypes();if(t.length)this.hls.trigger(Gt.BUFFER_CREATED,{tracks:this.tracks}),t.forEach(t=>{e.executeNext(t)});else{const t=new Error("could not create source buffer for media codec(s)");this.hls.trigger(Gt.ERROR,{type:jt.MEDIA_ERROR,details:Kt.BUFFER_INCOMPATIBLE_CODECS_ERROR,fatal:!0,error:t,reason:t.message})}}}createSourceBuffers(t){const{sourceBuffer:e,mediaSource:i}=this;if(!i)throw Error("createSourceBuffers called when mediaSource was null");for(const r in t)if(!e[r]){var s;const n=t[r];if(!n)throw Error(`source buffer exists for track ${r}, however track does not`);let a=-1===(null==(s=n.levelCodec)?void 0:s.indexOf(","))?n.levelCodec:n.codec;a&&"audio"===r.slice(0,5)&&(a=qi(a,this.appendSource));const o=`${n.container};codecs=${a}`;this.log(`creating sourceBuffer(${o})`);try{const t=e[r]=i.addSourceBuffer(o),s=r;this.addBufferListener(s,"updatestart",this._onSBUpdateStart),this.addBufferListener(s,"updateend",this._onSBUpdateEnd),this.addBufferListener(s,"error",this._onSBUpdateError),this.appendSource&&this.addBufferListener(s,"bufferedchange",(t,e)=>{const i=e.removedRanges;null!=i&&i.length&&this.hls.trigger(Gt.BUFFER_FLUSHED,{type:r})}),this.tracks[r]={buffer:t,codec:a,container:n.container,levelCodec:n.levelCodec,metadata:n.metadata,id:n.id}}catch(nc){this.error("error while trying to add sourceBuffer: "+nc.message),this.hls.trigger(Gt.ERROR,{type:jt.MEDIA_ERROR,details:Kt.BUFFER_ADD_CODEC_ERROR,fatal:!1,error:nc,sourceBufferName:r,mimeType:o})}}}get mediaSrc(){var t;const e=(null==(t=this.media)?void 0:t.firstChild)||this.media;return null==e?void 0:e.src}_onSBUpdateStart(t){const{operationQueue:e}=this,i=e.current(t);i.onStart()}_onSBUpdateEnd(t){var e;if("closed"===(null==(e=this.mediaSource)?void 0:e.readyState))return void this.resetBuffer(t);const{operationQueue:i}=this,s=i.current(t);s.onComplete(),i.shiftAndExecuteNext(t)}_onSBUpdateError(t,e){var i;const s=new Error(`${t} SourceBuffer error. MediaSource readyState: ${null==(i=this.mediaSource)?void 0:i.readyState}`);this.error(""+s,e),this.hls.trigger(Gt.ERROR,{type:jt.MEDIA_ERROR,details:Kt.BUFFER_APPENDING_ERROR,sourceBufferName:t,error:s,fatal:!1});const r=this.operationQueue.current(t);r&&r.onError(s)}removeExecutor(t,e,i){const{media:s,mediaSource:r,operationQueue:n,sourceBuffer:a}=this,o=a[t];if(!s||!r||!o)return this.warn(`Attempting to remove from the ${t} SourceBuffer, but it does not exist`),void n.shiftAndExecuteNext(t);const l=Ut(s.duration)?s.duration:1/0,h=Ut(r.duration)?r.duration:1/0,c=Math.max(0,e),d=Math.min(i,l,h);d>c&&(!o.ending||o.ended)?(o.ended=!1,this.log(`Removing [${c},${d}] from the ${t} SourceBuffer`),o.remove(c,d)):n.shiftAndExecuteNext(t)}appendExecutor(t,e){const i=this.sourceBuffer[e];if(i)i.ended=!1,i.appendBuffer(t);else if(!this.pendingTracks[e])throw new Error(`Attempting to append to the ${e} SourceBuffer, but it does not exist`)}blockBuffers(t,e=this.getSourceBufferTypes()){if(!e.length)return this.log("Blocking operation requested, but no SourceBuffers exist"),void Promise.resolve().then(t);const{operationQueue:i}=this,s=e.map(t=>i.appendBlocker(t));Promise.all(s).then(()=>{t(),e.forEach(t=>{const e=this.sourceBuffer[t];null!=e&&e.updating||i.shiftAndExecuteNext(t)})})}getSourceBufferTypes(){return Object.keys(this.sourceBuffer)}addBufferListener(t,e,i){const s=this.sourceBuffer[t];if(!s)return;const r=i.bind(this,t);this.listeners[t].push({event:e,listener:r}),s.addEventListener(e,r)}removeBufferListeners(t){const e=this.sourceBuffer[t];e&&this.listeners[t].forEach(t=>{e.removeEventListener(t.event,t.listener)})}}function so(t){const e=t.querySelectorAll("source");[].slice.call(e).forEach(e=>{t.removeChild(e)})}function ro(t,e){const i=self.document.createElement("source");i.type="video/mp4",i.src=e,t.appendChild(i)}const no={42:225,92:233,94:237,95:243,96:250,123:231,124:247,125:209,126:241,127:9608,128:174,129:176,130:189,131:191,132:8482,133:162,134:163,135:9834,136:224,137:32,138:232,139:226,140:234,141:238,142:244,143:251,144:193,145:201,146:211,147:218,148:220,149:252,150:8216,151:161,152:42,153:8217,154:9473,155:169,156:8480,157:8226,158:8220,159:8221,160:192,161:194,162:199,163:200,164:202,165:203,166:235,167:206,168:207,169:239,170:212,171:217,172:249,173:219,174:171,175:187,176:195,177:227,178:205,179:204,180:236,181:210,182:242,183:213,184:245,185:123,186:125,187:92,188:94,189:95,190:124,191:8764,192:196,193:228,194:214,195:246,196:223,197:165,198:164,199:9475,200:197,201:229,202:216,203:248,204:9487,205:9491,206:9495,207:9499},ao=t=>String.fromCharCode(no[t]||t),oo=15,lo=100,ho={17:1,18:3,21:5,22:7,23:9,16:11,19:12,20:14},co={17:2,18:4,21:6,22:8,23:10,19:13,20:15},uo={25:1,26:3,29:5,30:7,31:9,24:11,27:12,28:14},fo={25:2,26:4,29:6,30:8,31:10,27:13,28:15},go=["white","green","blue","cyan","red","yellow","magenta","black","transparent"];class mo{constructor(){this.time=null,this.verboseLevel=0}log(t,e){if(this.verboseLevel>=t){const i="function"===typeof e?e():e;Xt.log(`${this.time} [${t}] ${i}`)}}}const po=function(t){const e=[];for(let i=0;i<t.length;i++)e.push(t[i].toString(16));return e};class vo{constructor(){this.foreground="white",this.underline=!1,this.italics=!1,this.background="black",this.flash=!1}reset(){this.foreground="white",this.underline=!1,this.italics=!1,this.background="black",this.flash=!1}setStyles(t){const e=["foreground","underline","italics","background","flash"];for(let i=0;i<e.length;i++){const s=e[i];t.hasOwnProperty(s)&&(this[s]=t[s])}}isDefault(){return"white"===this.foreground&&!this.underline&&!this.italics&&"black"===this.background&&!this.flash}equals(t){return this.foreground===t.foreground&&this.underline===t.underline&&this.italics===t.italics&&this.background===t.background&&this.flash===t.flash}copy(t){this.foreground=t.foreground,this.underline=t.underline,this.italics=t.italics,this.background=t.background,this.flash=t.flash}toString(){return"color="+this.foreground+", underline="+this.underline+", italics="+this.italics+", background="+this.background+", flash="+this.flash}}class yo{constructor(){this.uchar=" ",this.penState=new vo}reset(){this.uchar=" ",this.penState.reset()}setChar(t,e){this.uchar=t,this.penState.copy(e)}setPenState(t){this.penState.copy(t)}equals(t){return this.uchar===t.uchar&&this.penState.equals(t.penState)}copy(t){this.uchar=t.uchar,this.penState.copy(t.penState)}isEmpty(){return" "===this.uchar&&this.penState.isDefault()}}class Eo{constructor(t){this.chars=[],this.pos=0,this.currPenState=new vo,this.cueStartTime=null,this.logger=void 0;for(let e=0;e<lo;e++)this.chars.push(new yo);this.logger=t}equals(t){for(let e=0;e<lo;e++)if(!this.chars[e].equals(t.chars[e]))return!1;return!0}copy(t){for(let e=0;e<lo;e++)this.chars[e].copy(t.chars[e])}isEmpty(){let t=!0;for(let e=0;e<lo;e++)if(!this.chars[e].isEmpty()){t=!1;break}return t}setCursor(t){this.pos!==t&&(this.pos=t),this.pos<0?(this.logger.log(3,"Negative cursor position "+this.pos),this.pos=0):this.pos>lo&&(this.logger.log(3,"Too large cursor position "+this.pos),this.pos=lo)}moveCursor(t){const e=this.pos+t;if(t>1)for(let i=this.pos+1;i<e+1;i++)this.chars[i].setPenState(this.currPenState);this.setCursor(e)}backSpace(){this.moveCursor(-1),this.chars[this.pos].setChar(" ",this.currPenState)}insertChar(t){t>=144&&this.backSpace();const e=ao(t);this.pos>=lo?this.logger.log(0,()=>"Cannot insert "+t.toString(16)+" ("+e+") at position "+this.pos+". Skipping it!"):(this.chars[this.pos].setChar(e,this.currPenState),this.moveCursor(1))}clearFromPos(t){let e;for(e=t;e<lo;e++)this.chars[e].reset()}clear(){this.clearFromPos(0),this.pos=0,this.currPenState.reset()}clearToEndOfRow(){this.clearFromPos(this.pos)}getTextString(){const t=[];let e=!0;for(let i=0;i<lo;i++){const s=this.chars[i].uchar;" "!==s&&(e=!1),t.push(s)}return e?"":t.join("")}setPenStyles(t){this.currPenState.setStyles(t);const e=this.chars[this.pos];e.setPenState(this.currPenState)}}class To{constructor(t){this.rows=[],this.currRow=oo-1,this.nrRollUpRows=null,this.lastOutputScreen=null,this.logger=void 0;for(let e=0;e<oo;e++)this.rows.push(new Eo(t));this.logger=t}reset(){for(let t=0;t<oo;t++)this.rows[t].clear();this.currRow=oo-1}equals(t){let e=!0;for(let i=0;i<oo;i++)if(!this.rows[i].equals(t.rows[i])){e=!1;break}return e}copy(t){for(let e=0;e<oo;e++)this.rows[e].copy(t.rows[e])}isEmpty(){let t=!0;for(let e=0;e<oo;e++)if(!this.rows[e].isEmpty()){t=!1;break}return t}backSpace(){const t=this.rows[this.currRow];t.backSpace()}clearToEndOfRow(){const t=this.rows[this.currRow];t.clearToEndOfRow()}insertChar(t){const e=this.rows[this.currRow];e.insertChar(t)}setPen(t){const e=this.rows[this.currRow];e.setPenStyles(t)}moveCursor(t){const e=this.rows[this.currRow];e.moveCursor(t)}setCursor(t){this.logger.log(2,"setCursor: "+t);const e=this.rows[this.currRow];e.setCursor(t)}setPAC(t){this.logger.log(2,()=>"pacData = "+JSON.stringify(t));let e=t.row-1;if(this.nrRollUpRows&&e<this.nrRollUpRows-1&&(e=this.nrRollUpRows-1),this.nrRollUpRows&&this.currRow!==e){for(let e=0;e<oo;e++)this.rows[e].clear();const t=this.currRow+1-this.nrRollUpRows,i=this.lastOutputScreen;if(i){const s=i.rows[t].cueStartTime,r=this.logger.time;if(null!==s&&null!==r&&s<r)for(let n=0;n<this.nrRollUpRows;n++)this.rows[e-this.nrRollUpRows+n+1].copy(i.rows[t+n])}}this.currRow=e;const i=this.rows[this.currRow];if(null!==t.indent){const e=t.indent,s=Math.max(e-1,0);i.setCursor(t.indent),t.color=i.chars[s].penState.foreground}const s={foreground:t.color,underline:t.underline,italics:t.italics,background:"black",flash:!1};this.setPen(s)}setBkgData(t){this.logger.log(2,()=>"bkgData = "+JSON.stringify(t)),this.backSpace(),this.setPen(t),this.insertChar(32)}setRollUpRows(t){this.nrRollUpRows=t}rollUp(){if(null===this.nrRollUpRows)return void this.logger.log(3,"roll_up but nrRollUpRows not set yet");this.logger.log(1,()=>this.getDisplayText());const t=this.currRow+1-this.nrRollUpRows,e=this.rows.splice(t,1)[0];e.clear(),this.rows.splice(this.currRow,0,e),this.logger.log(2,"Rolling up")}getDisplayText(t){t=t||!1;const e=[];let i="",s=-1;for(let r=0;r<oo;r++){const i=this.rows[r].getTextString();i&&(s=r+1,t?e.push("Row "+s+": '"+i+"'"):e.push(i.trim()))}return e.length>0&&(i=t?"["+e.join(" | ")+"]":e.join("\n")),i}getTextAndFormat(){return this.rows}}class So{constructor(t,e,i){this.chNr=void 0,this.outputFilter=void 0,this.mode=void 0,this.verbose=void 0,this.displayedMemory=void 0,this.nonDisplayedMemory=void 0,this.lastOutputScreen=void 0,this.currRollUpRow=void 0,this.writeScreen=void 0,this.cueStartTime=void 0,this.logger=void 0,this.chNr=t,this.outputFilter=e,this.mode=null,this.verbose=0,this.displayedMemory=new To(i),this.nonDisplayedMemory=new To(i),this.lastOutputScreen=new To(i),this.currRollUpRow=this.displayedMemory.rows[oo-1],this.writeScreen=this.displayedMemory,this.mode=null,this.cueStartTime=null,this.logger=i}reset(){this.mode=null,this.displayedMemory.reset(),this.nonDisplayedMemory.reset(),this.lastOutputScreen.reset(),this.outputFilter.reset(),this.currRollUpRow=this.displayedMemory.rows[oo-1],this.writeScreen=this.displayedMemory,this.mode=null,this.cueStartTime=null}getHandler(){return this.outputFilter}setHandler(t){this.outputFilter=t}setPAC(t){this.writeScreen.setPAC(t)}setBkgData(t){this.writeScreen.setBkgData(t)}setMode(t){t!==this.mode&&(this.mode=t,this.logger.log(2,()=>"MODE="+t),"MODE_POP-ON"===this.mode?this.writeScreen=this.nonDisplayedMemory:(this.writeScreen=this.displayedMemory,this.writeScreen.reset()),"MODE_ROLL-UP"!==this.mode&&(this.displayedMemory.nrRollUpRows=null,this.nonDisplayedMemory.nrRollUpRows=null),this.mode=t)}insertChars(t){for(let i=0;i<t.length;i++)this.writeScreen.insertChar(t[i]);const e=this.writeScreen===this.displayedMemory?"DISP":"NON_DISP";this.logger.log(2,()=>e+": "+this.writeScreen.getDisplayText(!0)),"MODE_PAINT-ON"!==this.mode&&"MODE_ROLL-UP"!==this.mode||(this.logger.log(1,()=>"DISPLAYED: "+this.displayedMemory.getDisplayText(!0)),this.outputDataUpdate())}ccRCL(){this.logger.log(2,"RCL - Resume Caption Loading"),this.setMode("MODE_POP-ON")}ccBS(){this.logger.log(2,"BS - BackSpace"),"MODE_TEXT"!==this.mode&&(this.writeScreen.backSpace(),this.writeScreen===this.displayedMemory&&this.outputDataUpdate())}ccAOF(){}ccAON(){}ccDER(){this.logger.log(2,"DER- Delete to End of Row"),this.writeScreen.clearToEndOfRow(),this.outputDataUpdate()}ccRU(t){this.logger.log(2,"RU("+t+") - Roll Up"),this.writeScreen=this.displayedMemory,this.setMode("MODE_ROLL-UP"),this.writeScreen.setRollUpRows(t)}ccFON(){this.logger.log(2,"FON - Flash On"),this.writeScreen.setPen({flash:!0})}ccRDC(){this.logger.log(2,"RDC - Resume Direct Captioning"),this.setMode("MODE_PAINT-ON")}ccTR(){this.logger.log(2,"TR"),this.setMode("MODE_TEXT")}ccRTD(){this.logger.log(2,"RTD"),this.setMode("MODE_TEXT")}ccEDM(){this.logger.log(2,"EDM - Erase Displayed Memory"),this.displayedMemory.reset(),this.outputDataUpdate(!0)}ccCR(){this.logger.log(2,"CR - Carriage Return"),this.writeScreen.rollUp(),this.outputDataUpdate(!0)}ccENM(){this.logger.log(2,"ENM - Erase Non-displayed Memory"),this.nonDisplayedMemory.reset()}ccEOC(){if(this.logger.log(2,"EOC - End Of Caption"),"MODE_POP-ON"===this.mode){const t=this.displayedMemory;this.displayedMemory=this.nonDisplayedMemory,this.nonDisplayedMemory=t,this.writeScreen=this.nonDisplayedMemory,this.logger.log(1,()=>"DISP: "+this.displayedMemory.getDisplayText())}this.outputDataUpdate(!0)}ccTO(t){this.logger.log(2,"TO("+t+") - Tab Offset"),this.writeScreen.moveCursor(t)}ccMIDROW(t){const e={flash:!1};if(e.underline=t%2===1,e.italics=t>=46,e.italics)e.foreground="white";else{const i=Math.floor(t/2)-16,s=["white","green","blue","cyan","red","yellow","magenta"];e.foreground=s[i]}this.logger.log(2,"MIDROW: "+JSON.stringify(e)),this.writeScreen.setPen(e)}outputDataUpdate(t=!1){const e=this.logger.time;null!==e&&this.outputFilter&&(null!==this.cueStartTime||this.displayedMemory.isEmpty()?this.displayedMemory.equals(this.lastOutputScreen)||(this.outputFilter.newCue(this.cueStartTime,e,this.lastOutputScreen),t&&this.outputFilter.dispatchCue&&this.outputFilter.dispatchCue(),this.cueStartTime=this.displayedMemory.isEmpty()?null:e):this.cueStartTime=e,this.lastOutputScreen.copy(this.displayedMemory))}cueSplitAtTime(t){this.outputFilter&&(this.displayedMemory.isEmpty()||(this.outputFilter.newCue&&this.outputFilter.newCue(this.cueStartTime,t,this.displayedMemory),this.cueStartTime=t))}}class bo{constructor(t,e,i){this.channels=void 0,this.currentChannel=0,this.cmdHistory=Ro(),this.logger=void 0;const s=this.logger=new mo;this.channels=[null,new So(t,e,s),new So(t+1,i,s)]}getHandler(t){return this.channels[t].getHandler()}setHandler(t,e){this.channels[t].setHandler(e)}addData(t,e){this.logger.time=t;for(let i=0;i<e.length;i+=2){const t=127&e[i],s=127&e[i+1];let r=!1,n=null;if(0===t&&0===s)continue;this.logger.log(3,()=>"["+po([e[i],e[i+1]])+"] -> ("+po([t,s])+")");const a=this.cmdHistory,o=t>=16&&t<=31;if(o){if(Lo(t,s,a)){Ao(null,null,a),this.logger.log(3,()=>"Repeated command ("+po([t,s])+") is dropped");continue}Ao(t,s,this.cmdHistory),r=this.parseCmd(t,s),r||(r=this.parseMidrow(t,s)),r||(r=this.parsePAC(t,s)),r||(r=this.parseBackgroundAttributes(t,s))}else Ao(null,null,a);if(!r&&(n=this.parseChars(t,s),n)){const t=this.currentChannel;if(t&&t>0){const e=this.channels[t];e.insertChars(n)}else this.logger.log(2,"No channel found yet. TEXT-MODE?")}r||n||this.logger.log(2,()=>"Couldn't parse cleaned data "+po([t,s])+" orig: "+po([e[i],e[i+1]]))}}parseCmd(t,e){const i=(20===t||28===t||21===t||29===t)&&e>=32&&e<=47,s=(23===t||31===t)&&e>=33&&e<=35;if(!i&&!s)return!1;const r=20===t||21===t||23===t?1:2,n=this.channels[r];return 20===t||21===t||28===t||29===t?32===e?n.ccRCL():33===e?n.ccBS():34===e?n.ccAOF():35===e?n.ccAON():36===e?n.ccDER():37===e?n.ccRU(2):38===e?n.ccRU(3):39===e?n.ccRU(4):40===e?n.ccFON():41===e?n.ccRDC():42===e?n.ccTR():43===e?n.ccRTD():44===e?n.ccEDM():45===e?n.ccCR():46===e?n.ccENM():47===e&&n.ccEOC():n.ccTO(e-32),this.currentChannel=r,!0}parseMidrow(t,e){let i=0;if((17===t||25===t)&&e>=32&&e<=47){if(i=17===t?1:2,i!==this.currentChannel)return this.logger.log(0,"Mismatch channel in midrow parsing"),!1;const s=this.channels[i];return!!s&&(s.ccMIDROW(e),this.logger.log(3,()=>"MIDROW ("+po([t,e])+")"),!0)}return!1}parsePAC(t,e){let i;const s=(t>=17&&t<=23||t>=25&&t<=31)&&e>=64&&e<=127,r=(16===t||24===t)&&e>=64&&e<=95;if(!s&&!r)return!1;const n=t<=23?1:2;i=e>=64&&e<=95?1===n?ho[t]:uo[t]:1===n?co[t]:fo[t];const a=this.channels[n];return!!a&&(a.setPAC(this.interpretPAC(i,e)),this.currentChannel=n,!0)}interpretPAC(t,e){let i;const s={color:null,italics:!1,indent:null,underline:!1,row:t};return i=e>95?e-96:e-64,s.underline=1===(1&i),i<=13?s.color=["white","green","blue","cyan","red","yellow","magenta","white"][Math.floor(i/2)]:i<=15?(s.italics=!0,s.color="white"):s.indent=4*Math.floor((i-16)/2),s}parseChars(t,e){let i,s=null,r=null;if(t>=25?(i=2,r=t-8):(i=1,r=t),r>=17&&r<=19){let t;t=17===r?e+80:18===r?e+112:e+144,this.logger.log(2,()=>"Special char '"+ao(t)+"' in channel "+i),s=[t]}else t>=32&&t<=127&&(s=0===e?[t]:[t,e]);return s&&this.logger.log(3,()=>"Char codes =  "+po(s).join(",")),s}parseBackgroundAttributes(t,e){const i=(16===t||24===t)&&e>=32&&e<=47,s=(23===t||31===t)&&e>=45&&e<=47;if(!i&&!s)return!1;let r;const n={};16===t||24===t?(r=Math.floor((e-32)/2),n.background=go[r],e%2===1&&(n.background=n.background+"_semi")):45===e?n.background="transparent":(n.foreground="black",47===e&&(n.underline=!0));const a=t<=23?1:2,o=this.channels[a];return o.setBkgData(n),!0}reset(){for(let t=0;t<Object.keys(this.channels).length;t++){const e=this.channels[t];e&&e.reset()}Ao(null,null,this.cmdHistory)}cueSplitAtTime(t){for(let e=0;e<this.channels.length;e++){const i=this.channels[e];i&&i.cueSplitAtTime(t)}}}function Ao(t,e,i){i.a=t,i.b=e}function Lo(t,e,i){return i.a===t&&i.b===e}function Ro(){return{a:null,b:null}}class Do{constructor(t,e){this.timelineController=void 0,this.cueRanges=[],this.trackName=void 0,this.startTime=null,this.endTime=null,this.screen=null,this.timelineController=t,this.trackName=e}dispatchCue(){null!==this.startTime&&(this.timelineController.addCues(this.trackName,this.startTime,this.endTime,this.screen,this.cueRanges),this.startTime=null)}newCue(t,e,i){(null===this.startTime||this.startTime>t)&&(this.startTime=t),this.endTime=e,this.screen=i,this.timelineController.createCaptionsTrack(this.trackName)}reset(){this.cueRanges=[],this.startTime=null}}var Io=function(){if(null!=me&&me.VTTCue)return self.VTTCue;const t=["","lr","rl"],e=["start","middle","end","left","right"];function i(t,e){if("string"!==typeof e)return!1;if(!Array.isArray(t))return!1;const i=e.toLowerCase();return!!~t.indexOf(i)&&i}function s(e){return i(t,e)}function r(t){return i(e,t)}function n(t,...e){let i=1;for(;i<arguments.length;i++){const e=arguments[i];for(const i in e)t[i]=e[i]}return t}function a(t,e,i){const a=this,o={enumerable:!0};a.hasBeenReset=!1;let l="",h=!1,c=t,d=e,u=i,f=null,g="",m=!0,p="auto",v="start",y=50,E="middle",T=50,S="middle";Object.defineProperty(a,"id",n({},o,{get:function(){return l},set:function(t){l=""+t}})),Object.defineProperty(a,"pauseOnExit",n({},o,{get:function(){return h},set:function(t){h=!!t}})),Object.defineProperty(a,"startTime",n({},o,{get:function(){return c},set:function(t){if("number"!==typeof t)throw new TypeError("Start time must be set to a number.");c=t,this.hasBeenReset=!0}})),Object.defineProperty(a,"endTime",n({},o,{get:function(){return d},set:function(t){if("number"!==typeof t)throw new TypeError("End time must be set to a number.");d=t,this.hasBeenReset=!0}})),Object.defineProperty(a,"text",n({},o,{get:function(){return u},set:function(t){u=""+t,this.hasBeenReset=!0}})),Object.defineProperty(a,"region",n({},o,{get:function(){return f},set:function(t){f=t,this.hasBeenReset=!0}})),Object.defineProperty(a,"vertical",n({},o,{get:function(){return g},set:function(t){const e=s(t);if(!1===e)throw new SyntaxError("An invalid or illegal string was specified.");g=e,this.hasBeenReset=!0}})),Object.defineProperty(a,"snapToLines",n({},o,{get:function(){return m},set:function(t){m=!!t,this.hasBeenReset=!0}})),Object.defineProperty(a,"line",n({},o,{get:function(){return p},set:function(t){if("number"!==typeof t&&"auto"!==t)throw new SyntaxError("An invalid number or illegal string was specified.");p=t,this.hasBeenReset=!0}})),Object.defineProperty(a,"lineAlign",n({},o,{get:function(){return v},set:function(t){const e=r(t);if(!e)throw new SyntaxError("An invalid or illegal string was specified.");v=e,this.hasBeenReset=!0}})),Object.defineProperty(a,"position",n({},o,{get:function(){return y},set:function(t){if(t<0||t>100)throw new Error("Position must be between 0 and 100.");y=t,this.hasBeenReset=!0}})),Object.defineProperty(a,"positionAlign",n({},o,{get:function(){return E},set:function(t){const e=r(t);if(!e)throw new SyntaxError("An invalid or illegal string was specified.");E=e,this.hasBeenReset=!0}})),Object.defineProperty(a,"size",n({},o,{get:function(){return T},set:function(t){if(t<0||t>100)throw new Error("Size must be between 0 and 100.");T=t,this.hasBeenReset=!0}})),Object.defineProperty(a,"align",n({},o,{get:function(){return S},set:function(t){const e=r(t);if(!e)throw new SyntaxError("An invalid or illegal string was specified.");S=e,this.hasBeenReset=!0}})),a.displayState=void 0}return a.prototype.getCueAsHTML=function(){const t=self.WebVTT;return t.convertCueToDOMTree(self,this.text)},a}();class ko{decode(t,e){if(!t)return"";if("string"!==typeof t)throw new Error("Error - expected string data.");return decodeURIComponent(encodeURIComponent(t))}}function wo(t){function e(t,e,i,s){return 3600*(0|t)+60*(0|e)+(0|i)+parseFloat(s||0)}const i=t.match(/^(?:(\d+):)?(\d{2}):(\d{2})(\.\d+)?/);return i?parseFloat(i[2])>59?e(i[2],i[3],0,i[4]):e(i[1],i[2],i[3],i[4]):null}class Co{constructor(){this.values=Object.create(null)}set(t,e){this.get(t)||""===e||(this.values[t]=e)}get(t,e,i){return i?this.has(t)?this.values[t]:e[i]:this.has(t)?this.values[t]:e}has(t){return t in this.values}alt(t,e,i){for(let s=0;s<i.length;++s)if(e===i[s]){this.set(t,e);break}}integer(t,e){/^-?\d+$/.test(e)&&this.set(t,parseInt(e,10))}percent(t,e){if(/^([\d]{1,3})(\.[\d]*)?%$/.test(e)){const i=parseFloat(e);if(i>=0&&i<=100)return this.set(t,i),!0}return!1}}function _o(t,e,i,s){const r=s?t.split(s):[t];for(const n in r){if("string"!==typeof r[n])continue;const t=r[n].split(i);if(2!==t.length)continue;const s=t[0],a=t[1];e(s,a)}}const Oo=new Io(0,0,""),xo="middle"===Oo.align?"middle":"center";function Po(t,e,i){const s=t;function r(){const e=wo(t);if(null===e)throw new Error("Malformed timestamp: "+s);return t=t.replace(/^[^\sa-zA-Z-]+/,""),e}function n(t,e){const s=new Co;_o(t,(function(t,e){let r;switch(t){case"region":for(let r=i.length-1;r>=0;r--)if(i[r].id===e){s.set(t,i[r].region);break}break;case"vertical":s.alt(t,e,["rl","lr"]);break;case"line":r=e.split(","),s.integer(t,r[0]),s.percent(t,r[0])&&s.set("snapToLines",!1),s.alt(t,r[0],["auto"]),2===r.length&&s.alt("lineAlign",r[1],["start",xo,"end"]);break;case"position":r=e.split(","),s.percent(t,r[0]),2===r.length&&s.alt("positionAlign",r[1],["start",xo,"end","line-left","line-right","auto"]);break;case"size":s.percent(t,e);break;case"align":s.alt(t,e,["start",xo,"end","left","right"]);break}}),/:/,/\s/),e.region=s.get("region",null),e.vertical=s.get("vertical","");let r=s.get("line","auto");"auto"===r&&-1===Oo.line&&(r=-1),e.line=r,e.lineAlign=s.get("lineAlign","start"),e.snapToLines=s.get("snapToLines",!0),e.size=s.get("size",100),e.align=s.get("align",xo);let n=s.get("position","auto");"auto"===n&&50===Oo.position&&(n="start"===e.align||"left"===e.align?0:"end"===e.align||"right"===e.align?100:50),e.position=n}function a(){t=t.replace(/^\s+/,"")}if(a(),e.startTime=r(),a(),"--\x3e"!==t.slice(0,3))throw new Error("Malformed time stamp (time stamps must be separated by '--\x3e'): "+s);t=t.slice(3),a(),e.endTime=r(),a(),n(t,e)}function Mo(t){return t.replace(/<br(?: \/)?>/gi,"\n")}class Fo{constructor(){this.state="INITIAL",this.buffer="",this.decoder=new ko,this.regionList=[],this.cue=null,this.oncue=void 0,this.onparsingerror=void 0,this.onflush=void 0}parse(t){const e=this;function i(){let t=e.buffer,i=0;t=Mo(t);while(i<t.length&&"\r"!==t[i]&&"\n"!==t[i])++i;const s=t.slice(0,i);return"\r"===t[i]&&++i,"\n"===t[i]&&++i,e.buffer=t.slice(i),s}function s(t){_o(t,(function(t,e){}),/:/)}t&&(e.buffer+=e.decoder.decode(t,{stream:!0}));try{let t="";if("INITIAL"===e.state){if(!/\r\n|\n/.test(e.buffer))return this;t=i();const s=t.match(/^(ï»¿)?WEBVTT([ \t].*)?$/);if(null==s||!s[0])throw new Error("Malformed WebVTT signature.");e.state="HEADER"}let n=!1;while(e.buffer){if(!/\r\n|\n/.test(e.buffer))return this;switch(n?n=!1:t=i(),e.state){case"HEADER":/:/.test(t)?s(t):t||(e.state="ID");continue;case"NOTE":t||(e.state="ID");continue;case"ID":if(/^NOTE($|[ \t])/.test(t)){e.state="NOTE";break}if(!t)continue;if(e.cue=new Io(0,0,""),e.state="CUE",-1===t.indexOf("--\x3e")){e.cue.id=t;continue}case"CUE":if(!e.cue){e.state="BADCUE";continue}try{Po(t,e.cue,e.regionList)}catch(r){e.cue=null,e.state="BADCUE";continue}e.state="CUETEXT";continue;case"CUETEXT":{const i=-1!==t.indexOf("--\x3e");if(!t||i&&(n=!0)){e.oncue&&e.cue&&e.oncue(e.cue),e.cue=null,e.state="ID";continue}if(null===e.cue)continue;e.cue.text&&(e.cue.text+="\n"),e.cue.text+=t}continue;case"BADCUE":t||(e.state="ID")}}}catch(r){"CUETEXT"===e.state&&e.cue&&e.oncue&&e.oncue(e.cue),e.cue=null,e.state="INITIAL"===e.state?"BADWEBVTT":"BADCUE"}return this}flush(){const t=this;try{if((t.cue||"HEADER"===t.state)&&(t.buffer+="\n\n",t.parse()),"INITIAL"===t.state||"BADWEBVTT"===t.state)throw new Error("Malformed WebVTT signature.")}catch(e){t.onparsingerror&&t.onparsingerror(e)}return t.onflush&&t.onflush(),this}}const No=/\r\n|\n\r|\n|\r/g,Uo=function(t,e,i=0){return t.slice(i,i+e.length)===e},Bo=function(t){let e=parseInt(t.slice(-3));const i=parseInt(t.slice(-6,-4)),s=parseInt(t.slice(-9,-7)),r=t.length>9?parseInt(t.substring(0,t.indexOf(":"))):0;if(!Ut(e)||!Ut(i)||!Ut(s)||!Ut(r))throw Error("Malformed X-TIMESTAMP-MAP: Local:"+t);return e+=1e3*i,e+=6e4*s,e+=36e5*r,e},$o=function(t){let e=5381,i=t.length;while(i)e=33*e^t.charCodeAt(--i);return(e>>>0).toString()};function Go(t,e,i){return $o(t.toString())+$o(e.toString())+$o(i)}const jo=function(t,e,i){let s=t[e],r=t[s.prevCC];if(!r||!r.new&&s.new)return t.ccOffset=t.presentationOffset=s.start,void(s.new=!1);while(null!=(n=r)&&n.new){var n;t.ccOffset+=s.start-r.start,s.new=!1,s=r,r=t[s.prevCC]}t.presentationOffset=i};function Ko(t,e,i,s,r,n,a){const o=new Fo,l=Ge(new Uint8Array(t)).trim().replace(No,"\n").split("\n"),h=[],c=e?pa(e.baseTime,e.timescale):0;let d,u="00:00.000",f=0,g=0,m=!0;o.oncue=function(t){const n=i[s];let a=i.ccOffset;const o=(f-c)/9e4;if(null!=n&&n.new&&(void 0!==g?a=i.ccOffset=n.start:jo(i,s,o)),o){if(!e)return void(d=new Error("Missing initPTS for VTT MPEGTS"));a=o-i.presentationOffset}const l=t.endTime-t.startTime,u=Ra(9e4*(t.startTime+a-g),9e4*r)/9e4;t.startTime=Math.max(u,0),t.endTime=Math.max(u+l,0);const m=t.text.trim();t.text=decodeURIComponent(encodeURIComponent(m)),t.id||(t.id=Go(t.startTime,t.endTime,m)),t.endTime>0&&h.push(t)},o.onparsingerror=function(t){d=t},o.onflush=function(){d?a(d):n(h)},l.forEach(t=>{if(m){if(Uo(t,"X-TIMESTAMP-MAP=")){m=!1,t.slice(16).split(",").forEach(t=>{Uo(t,"LOCAL:")?u=t.slice(6):Uo(t,"MPEGTS:")&&(f=parseInt(t.slice(7)))});try{g=Bo(u)/1e3}catch(e){d=e}return}""===t&&(m=!1)}o.parse(t+"\n")}),o.flush()}const Vo="stpp.ttml.im1t",Ho=/^(\d{2,}):(\d{2}):(\d{2}):(\d{2})\.?(\d+)?$/,qo=/^(\d*(?:\.\d*)?)(h|m|s|ms|f|t)$/,Yo={left:"start",center:"center",right:"end",start:"start",end:"end"};function Wo(t,e,i,s){const r=ei(new Uint8Array(t),["mdat"]);if(0===r.length)return void s(new Error("Could not parse IMSC1 mdat"));const n=r.map(t=>Ge(t)),a=ga(e.baseTime,1,e.timescale);try{n.forEach(t=>i(zo(t,a)))}catch(o){s(o)}}function zo(t,e){const i=new DOMParser,s=i.parseFromString(t,"text/xml"),r=s.getElementsByTagName("tt")[0];if(!r)throw new Error("Invalid ttml");const n={frameRate:30,subFrameRate:1,frameRateMultiplier:0,tickRate:0},a=Object.keys(n).reduce((t,e)=>(t[e]=r.getAttribute("ttp:"+e)||n[e],t),{}),o="preserve"!==r.getAttribute("xml:space"),l=Qo(Xo(r,"styling","style")),h=Qo(Xo(r,"layout","region")),c=Xo(r,"body","[begin]");return[].map.call(c,t=>{const i=Jo(t,o);if(!i||!t.hasAttribute("begin"))return null;const s=il(t.getAttribute("begin"),a),r=il(t.getAttribute("dur"),a);let n=il(t.getAttribute("end"),a);if(null===s)throw el(t);if(null===n){if(null===r)throw el(t);n=s+r}const c=new Io(s-e,n-e,i);c.id=Go(c.startTime,c.endTime,c.text);const d=h[t.getAttribute("region")],u=l[t.getAttribute("style")],f=Zo(d,u,l),{textAlign:g}=f;if(g){const t=Yo[g];t&&(c.lineAlign=t),c.align=g}return Nt(c,f),c}).filter(t=>null!==t)}function Xo(t,e,i){const s=t.getElementsByTagName(e)[0];return s?[].slice.call(s.querySelectorAll(i)):[]}function Qo(t){return t.reduce((t,e)=>{const i=e.getAttribute("xml:id");return i&&(t[i]=e),t},{})}function Jo(t,e){return[].slice.call(t.childNodes).reduce((t,i,s)=>{var r;return"br"===i.nodeName&&s?t+"\n":null!=(r=i.childNodes)&&r.length?Jo(i,e):e?t+i.textContent.trim().replace(/\s+/g," "):t+i.textContent},"")}function Zo(t,e,i){const s="http://www.w3.org/ns/ttml#styling";let r=null;const n=["displayAlign","textAlign","color","backgroundColor","fontSize","fontFamily"],a=null!=t&&t.hasAttribute("style")?t.getAttribute("style"):null;return a&&i.hasOwnProperty(a)&&(r=i[a]),n.reduce((i,n)=>{const a=tl(e,s,n)||tl(t,s,n)||tl(r,s,n);return a&&(i[n]=a),i},{})}function tl(t,e,i){return t&&t.hasAttributeNS(e,i)?t.getAttributeNS(e,i):null}function el(t){return new Error("Could not parse ttml timestamp "+t)}function il(t,e){if(!t)return null;let i=wo(t);return null===i&&(Ho.test(t)?i=sl(t,e):qo.test(t)&&(i=rl(t,e))),i}function sl(t,e){const i=Ho.exec(t),s=(0|i[4])+(0|i[5])/e.subFrameRate;return 3600*(0|i[1])+60*(0|i[2])+(0|i[3])+s/e.frameRate}function rl(t,e){const i=qo.exec(t),s=Number(i[1]),r=i[2];switch(r){case"h":return 3600*s;case"m":return 60*s;case"ms":return 1e3*s;case"f":return s/e.frameRate;case"t":return s/e.tickRate}return s}class nl{constructor(t){this.hls=void 0,this.media=null,this.config=void 0,this.enabled=!0,this.Cues=void 0,this.textTracks=[],this.tracks=[],this.initPTS=[],this.unparsedVttFrags=[],this.captionsTracks={},this.nonNativeCaptionsTracks={},this.cea608Parser1=void 0,this.cea608Parser2=void 0,this.lastCc=-1,this.lastSn=-1,this.lastPartIndex=-1,this.prevCC=-1,this.vttCCs=hl(),this.captionsProperties=void 0,this.hls=t,this.config=t.config,this.Cues=t.config.cueHandler,this.captionsProperties={textTrack1:{label:this.config.captionsTextTrack1Label,languageCode:this.config.captionsTextTrack1LanguageCode},textTrack2:{label:this.config.captionsTextTrack2Label,languageCode:this.config.captionsTextTrack2LanguageCode},textTrack3:{label:this.config.captionsTextTrack3Label,languageCode:this.config.captionsTextTrack3LanguageCode},textTrack4:{label:this.config.captionsTextTrack4Label,languageCode:this.config.captionsTextTrack4LanguageCode}},t.on(Gt.MEDIA_ATTACHING,this.onMediaAttaching,this),t.on(Gt.MEDIA_DETACHING,this.onMediaDetaching,this),t.on(Gt.MANIFEST_LOADING,this.onManifestLoading,this),t.on(Gt.MANIFEST_LOADED,this.onManifestLoaded,this),t.on(Gt.SUBTITLE_TRACKS_UPDATED,this.onSubtitleTracksUpdated,this),t.on(Gt.FRAG_LOADING,this.onFragLoading,this),t.on(Gt.FRAG_LOADED,this.onFragLoaded,this),t.on(Gt.FRAG_PARSING_USERDATA,this.onFragParsingUserdata,this),t.on(Gt.FRAG_DECRYPTED,this.onFragDecrypted,this),t.on(Gt.INIT_PTS_FOUND,this.onInitPtsFound,this),t.on(Gt.SUBTITLE_TRACKS_CLEARED,this.onSubtitleTracksCleared,this),t.on(Gt.BUFFER_FLUSHING,this.onBufferFlushing,this)}destroy(){const{hls:t}=this;t.off(Gt.MEDIA_ATTACHING,this.onMediaAttaching,this),t.off(Gt.MEDIA_DETACHING,this.onMediaDetaching,this),t.off(Gt.MANIFEST_LOADING,this.onManifestLoading,this),t.off(Gt.MANIFEST_LOADED,this.onManifestLoaded,this),t.off(Gt.SUBTITLE_TRACKS_UPDATED,this.onSubtitleTracksUpdated,this),t.off(Gt.FRAG_LOADING,this.onFragLoading,this),t.off(Gt.FRAG_LOADED,this.onFragLoaded,this),t.off(Gt.FRAG_PARSING_USERDATA,this.onFragParsingUserdata,this),t.off(Gt.FRAG_DECRYPTED,this.onFragDecrypted,this),t.off(Gt.INIT_PTS_FOUND,this.onInitPtsFound,this),t.off(Gt.SUBTITLE_TRACKS_CLEARED,this.onSubtitleTracksCleared,this),t.off(Gt.BUFFER_FLUSHING,this.onBufferFlushing,this),this.hls=this.config=null,this.cea608Parser1=this.cea608Parser2=void 0}initCea608Parsers(){if(this.config.enableCEA708Captions&&(!this.cea608Parser1||!this.cea608Parser2)){const t=new Do(this,"textTrack1"),e=new Do(this,"textTrack2"),i=new Do(this,"textTrack3"),s=new Do(this,"textTrack4");this.cea608Parser1=new bo(1,t,e),this.cea608Parser2=new bo(3,i,s)}}addCues(t,e,i,s,r){let n=!1;for(let a=r.length;a--;){const t=r[a],s=ll(t[0],t[1],e,i);if(s>=0&&(t[0]=Math.min(t[0],e),t[1]=Math.max(t[1],i),n=!0,s/(i-e)>.5))return}if(n||r.push([e,i]),this.config.renderTextTracksNatively){const r=this.captionsTracks[t];this.Cues.newCue(r,e,i,s)}else{const r=this.Cues.newCue(null,e,i,s);this.hls.trigger(Gt.CUES_PARSED,{type:"captions",cues:r,track:t})}}onInitPtsFound(t,{frag:e,id:i,initPTS:s,timescale:r}){const{unparsedVttFrags:n}=this;"main"===i&&(this.initPTS[e.cc]={baseTime:s,timescale:r}),n.length&&(this.unparsedVttFrags=[],n.forEach(t=>{this.onFragLoaded(Gt.FRAG_LOADED,t)}))}getExistingTrack(t,e){const{media:i}=this;if(i)for(let s=0;s<i.textTracks.length;s++){const r=i.textTracks[s];if(ol(r,{name:t,lang:e,attrs:{}}))return r}return null}createCaptionsTrack(t){this.config.renderTextTracksNatively?this.createNativeTrack(t):this.createNonNativeTrack(t)}createNativeTrack(t){if(this.captionsTracks[t])return;const{captionsProperties:e,captionsTracks:i,media:s}=this,{label:r,languageCode:n}=e[t],a=this.getExistingTrack(r,n);if(a)i[t]=a,ps(i[t]),gs(i[t],s);else{const e=this.createTextTrack("captions",r,n);e&&(e[t]=!0,i[t]=e)}}createNonNativeTrack(t){if(this.nonNativeCaptionsTracks[t])return;const e=this.captionsProperties[t];if(!e)return;const i=e.label,s={_id:t,label:i,kind:"captions",default:!!e.media&&!!e.media.default,closedCaptions:e.media};this.nonNativeCaptionsTracks[t]=s,this.hls.trigger(Gt.NON_NATIVE_TEXT_TRACKS_FOUND,{tracks:[s]})}createTextTrack(t,e,i){const s=this.media;if(s)return s.addTextTrack(t,e,i)}onMediaAttaching(t,e){this.media=e.media,this._cleanTracks()}onMediaDetaching(){const{captionsTracks:t}=this;Object.keys(t).forEach(e=>{ps(t[e]),delete t[e]}),this.nonNativeCaptionsTracks={}}onManifestLoading(){this.lastCc=-1,this.lastSn=-1,this.lastPartIndex=-1,this.prevCC=-1,this.vttCCs=hl(),this._cleanTracks(),this.tracks=[],this.captionsTracks={},this.nonNativeCaptionsTracks={},this.textTracks=[],this.unparsedVttFrags=[],this.initPTS=[],this.cea608Parser1&&this.cea608Parser2&&(this.cea608Parser1.reset(),this.cea608Parser2.reset())}_cleanTracks(){const{media:t}=this;if(!t)return;const e=t.textTracks;if(e)for(let i=0;i<e.length;i++)ps(e[i])}onSubtitleTracksUpdated(t,e){const i=e.subtitleTracks||[],s=i.some(t=>t.textCodec===Vo);if(this.config.enableWebVTT||s&&this.config.enableIMSC1){const t=Va(this.tracks,i);if(t)return void(this.tracks=i);if(this.textTracks=[],this.tracks=i,this.config.renderTextTracksNatively){const t=this.media,e=t?Ts(t.textTracks):null;if(this.tracks.forEach((t,i)=>{let s;if(e){let i=null;for(let s=0;s<e.length;s++)if(e[s]&&ol(e[s],t)){i=e[s],e[s]=null;break}i&&(s=i)}if(s)ps(s);else{const e=al(t);s=this.createTextTrack(e,t.name,t.lang),s&&(s.mode="disabled")}s&&this.textTracks.push(s)}),null!=e&&e.length){const t=e.filter(t=>null!==t).map(t=>t.label);t.length&&Xt.warn(`Media element contains unused subtitle tracks: ${t.join(", ")}. Replace media element for each source to clear TextTracks and captions menu.`)}}else if(this.tracks.length){const t=this.tracks.map(t=>({label:t.name,kind:t.type.toLowerCase(),default:t.default,subtitleTrack:t}));this.hls.trigger(Gt.NON_NATIVE_TEXT_TRACKS_FOUND,{tracks:t})}}}onManifestLoaded(t,e){this.config.enableCEA708Captions&&e.captions&&e.captions.forEach(t=>{const e=/(?:CC|SERVICE)([1-4])/.exec(t.instreamId);if(!e)return;const i="textTrack"+e[1],s=this.captionsProperties[i];s&&(s.label=t.name,t.lang&&(s.languageCode=t.lang),s.media=t)})}closedCaptionsForLevel(t){const e=this.hls.levels[t.level];return null==e?void 0:e.attrs["CLOSED-CAPTIONS"]}onFragLoading(t,e){if(this.enabled&&e.frag.type===cs.MAIN){var i,s;const{cea608Parser1:t,cea608Parser2:r,lastSn:n}=this,{cc:a,sn:o}=e.frag,l=null!=(i=null==(s=e.part)?void 0:s.index)?i:-1;t&&r&&(o!==n+1||o===n&&l!==this.lastPartIndex+1||a!==this.lastCc)&&(t.reset(),r.reset()),this.lastCc=a,this.lastSn=o,this.lastPartIndex=l}}onFragLoaded(t,e){const{frag:i,payload:s}=e;if(i.type===cs.SUBTITLE)if(s.byteLength){const t=i.decryptdata,r="stats"in e;if(null==t||!t.encrypted||r){const t=this.tracks[i.level],r=this.vttCCs;r[i.cc]||(r[i.cc]={start:i.start,prevCC:this.prevCC,new:!0},this.prevCC=i.cc),t&&t.textCodec===Vo?this._parseIMSC1(i,s):this._parseVTTs(e)}}else this.hls.trigger(Gt.SUBTITLE_FRAG_PROCESSED,{success:!1,frag:i,error:new Error("Empty subtitle payload")})}_parseIMSC1(t,e){const i=this.hls;Wo(e,this.initPTS[t.cc],e=>{this._appendCues(e,t.level),i.trigger(Gt.SUBTITLE_FRAG_PROCESSED,{success:!0,frag:t})},e=>{Xt.log("Failed to parse IMSC1: "+e),i.trigger(Gt.SUBTITLE_FRAG_PROCESSED,{success:!1,frag:t,error:e})})}_parseVTTs(t){var e;const{frag:i,payload:s}=t,{initPTS:r,unparsedVttFrags:n}=this,a=r.length-1;if(!r[i.cc]&&-1===a)return void n.push(t);const o=this.hls,l=null!=(e=i.initSegment)&&e.data?mi(i.initSegment.data,new Uint8Array(s)):s;Ko(l,this.initPTS[i.cc],this.vttCCs,i.cc,i.start,t=>{this._appendCues(t,i.level),o.trigger(Gt.SUBTITLE_FRAG_PROCESSED,{success:!0,frag:i})},e=>{const r="Missing initPTS for VTT MPEGTS"===e.message;r?n.push(t):this._fallbackToIMSC1(i,s),Xt.log("Failed to parse VTT cue: "+e),r&&a>i.cc||o.trigger(Gt.SUBTITLE_FRAG_PROCESSED,{success:!1,frag:i,error:e})})}_fallbackToIMSC1(t,e){const i=this.tracks[t.level];i.textCodec||Wo(e,this.initPTS[t.cc],()=>{i.textCodec=Vo,this._parseIMSC1(t,e)},()=>{i.textCodec="wvtt"})}_appendCues(t,e){const i=this.hls;if(this.config.renderTextTracksNatively){const i=this.textTracks[e];if(!i||"disabled"===i.mode)return;t.forEach(t=>ms(i,t))}else{const s=this.tracks[e];if(!s)return;const r=s.default?"default":"subtitles"+e;i.trigger(Gt.CUES_PARSED,{type:"subtitles",cues:t,track:r})}}onFragDecrypted(t,e){const{frag:i}=e;i.type===cs.SUBTITLE&&this.onFragLoaded(Gt.FRAG_LOADED,e)}onSubtitleTracksCleared(){this.tracks=[],this.captionsTracks={}}onFragParsingUserdata(t,e){this.initCea608Parsers();const{cea608Parser1:i,cea608Parser2:s}=this;if(!this.enabled||!i||!s)return;const{frag:r,samples:n}=e;if(r.type!==cs.MAIN||"NONE"!==this.closedCaptionsForLevel(r))for(let a=0;a<n.length;a++){const t=n[a].bytes;if(t){const e=this.extractCea608Data(t);i.addData(n[a].pts,e[0]),s.addData(n[a].pts,e[1])}}}onBufferFlushing(t,{startOffset:e,endOffset:i,endOffsetSubtitles:s,type:r}){const{media:n}=this;if(n&&!(n.currentTime<i)){if(!r||"video"===r){const{captionsTracks:t}=this;Object.keys(t).forEach(s=>vs(t[s],e,i))}if(this.config.renderTextTracksNatively&&0===e&&void 0!==s){const{textTracks:t}=this;Object.keys(t).forEach(i=>vs(t[i],e,s))}}}extractCea608Data(t){const e=[[],[]],i=31&t[0];let s=2;for(let r=0;r<i;r++){const i=t[s++],r=127&t[s++],n=127&t[s++];if(0===r&&0===n)continue;const a=0!==(4&i);if(a){const t=3&i;0!==t&&1!==t||(e[t].push(r),e[t].push(n))}}return e}}function al(t){return t.characteristics&&/transcribes-spoken-dialog/gi.test(t.characteristics)&&/describes-music-and-sound/gi.test(t.characteristics)?"captions":"subtitles"}function ol(t,e){return!!t&&t.kind===al(e)&&qa(e,t)}function ll(t,e,i,s){return Math.min(e,s)-Math.max(t,i)}function hl(){return{ccOffset:0,presentationOffset:0,0:{start:0,prevCC:-1,new:!0}}}class cl{constructor(t){this.hls=void 0,this.autoLevelCapping=void 0,this.firstLevel=void 0,this.media=void 0,this.restrictedLevels=void 0,this.timer=void 0,this.clientRect=void 0,this.streamController=void 0,this.hls=t,this.autoLevelCapping=Number.POSITIVE_INFINITY,this.firstLevel=-1,this.media=null,this.restrictedLevels=[],this.timer=void 0,this.clientRect=null,this.registerListeners()}setStreamController(t){this.streamController=t}destroy(){this.hls&&this.unregisterListener(),this.timer&&this.stopCapping(),this.media=null,this.clientRect=null,this.hls=this.streamController=null}registerListeners(){const{hls:t}=this;t.on(Gt.FPS_DROP_LEVEL_CAPPING,this.onFpsDropLevelCapping,this),t.on(Gt.MEDIA_ATTACHING,this.onMediaAttaching,this),t.on(Gt.MANIFEST_PARSED,this.onManifestParsed,this),t.on(Gt.LEVELS_UPDATED,this.onLevelsUpdated,this),t.on(Gt.BUFFER_CODECS,this.onBufferCodecs,this),t.on(Gt.MEDIA_DETACHING,this.onMediaDetaching,this)}unregisterListener(){const{hls:t}=this;t.off(Gt.FPS_DROP_LEVEL_CAPPING,this.onFpsDropLevelCapping,this),t.off(Gt.MEDIA_ATTACHING,this.onMediaAttaching,this),t.off(Gt.MANIFEST_PARSED,this.onManifestParsed,this),t.off(Gt.LEVELS_UPDATED,this.onLevelsUpdated,this),t.off(Gt.BUFFER_CODECS,this.onBufferCodecs,this),t.off(Gt.MEDIA_DETACHING,this.onMediaDetaching,this)}onFpsDropLevelCapping(t,e){const i=this.hls.levels[e.droppedLevel];this.isLevelAllowed(i)&&this.restrictedLevels.push({bitrate:i.bitrate,height:i.height,width:i.width})}onMediaAttaching(t,e){this.media=e.media instanceof HTMLVideoElement?e.media:null,this.clientRect=null,this.timer&&this.hls.levels.length&&this.detectPlayerSize()}onManifestParsed(t,e){const i=this.hls;this.restrictedLevels=[],this.firstLevel=e.firstLevel,i.config.capLevelToPlayerSize&&e.video&&this.startCapping()}onLevelsUpdated(t,e){this.timer&&Ut(this.autoLevelCapping)&&this.detectPlayerSize()}onBufferCodecs(t,e){const i=this.hls;i.config.capLevelToPlayerSize&&e.video&&this.startCapping()}onMediaDetaching(){this.stopCapping()}detectPlayerSize(){if(this.media){if(this.mediaHeight<=0||this.mediaWidth<=0)return void(this.clientRect=null);const t=this.hls.levels;if(t.length){const e=this.hls,i=this.getMaxLevel(t.length-1);i!==this.autoLevelCapping&&Xt.log(`Setting autoLevelCapping to ${i}: ${t[i].height}p@${t[i].bitrate} for media ${this.mediaWidth}x${this.mediaHeight}`),e.autoLevelCapping=i,e.autoLevelCapping>this.autoLevelCapping&&this.streamController&&this.streamController.nextLevelSwitch(),this.autoLevelCapping=e.autoLevelCapping}}}getMaxLevel(t){const e=this.hls.levels;if(!e.length)return-1;const i=e.filter((e,i)=>this.isLevelAllowed(e)&&i<=t);return this.clientRect=null,cl.getMaxLevelByMediaSize(i,this.mediaWidth,this.mediaHeight)}startCapping(){this.timer||(this.autoLevelCapping=Number.POSITIVE_INFINITY,self.clearInterval(this.timer),this.timer=self.setInterval(this.detectPlayerSize.bind(this),1e3),this.detectPlayerSize())}stopCapping(){this.restrictedLevels=[],this.firstLevel=-1,this.autoLevelCapping=Number.POSITIVE_INFINITY,this.timer&&(self.clearInterval(this.timer),this.timer=void 0)}getDimensions(){if(this.clientRect)return this.clientRect;const t=this.media,e={width:0,height:0};if(t){const i=t.getBoundingClientRect();e.width=i.width,e.height=i.height,e.width||e.height||(e.width=i.right-i.left||t.width||0,e.height=i.bottom-i.top||t.height||0)}return this.clientRect=e,e}get mediaWidth(){return this.getDimensions().width*this.contentScaleFactor}get mediaHeight(){return this.getDimensions().height*this.contentScaleFactor}get contentScaleFactor(){let t=1;if(!this.hls.config.ignoreDevicePixelRatio)try{t=self.devicePixelRatio}catch(e){}return t}isLevelAllowed(t){const e=this.restrictedLevels;return!e.some(e=>t.bitrate===e.bitrate&&t.width===e.width&&t.height===e.height)}static getMaxLevelByMediaSize(t,e,i){if(null==t||!t.length)return-1;const s=(t,e)=>!e||(t.width!==e.width||t.height!==e.height);let r=t.length-1;const n=Math.max(e,i);for(let a=0;a<t.length;a+=1){const e=t[a];if((e.width>=n||e.height>=n)&&s(e,t[a+1])){r=a;break}}return r}}class dl{constructor(t){this.hls=void 0,this.isVideoPlaybackQualityAvailable=!1,this.timer=void 0,this.media=null,this.lastTime=void 0,this.lastDroppedFrames=0,this.lastDecodedFrames=0,this.streamController=void 0,this.hls=t,this.registerListeners()}setStreamController(t){this.streamController=t}registerListeners(){this.hls.on(Gt.MEDIA_ATTACHING,this.onMediaAttaching,this)}unregisterListeners(){this.hls.off(Gt.MEDIA_ATTACHING,this.onMediaAttaching,this)}destroy(){this.timer&&clearInterval(this.timer),this.unregisterListeners(),this.isVideoPlaybackQualityAvailable=!1,this.media=null}onMediaAttaching(t,e){const i=this.hls.config;if(i.capLevelOnFPSDrop){const t=e.media instanceof self.HTMLVideoElement?e.media:null;this.media=t,t&&"function"===typeof t.getVideoPlaybackQuality&&(this.isVideoPlaybackQualityAvailable=!0),self.clearInterval(this.timer),this.timer=self.setInterval(this.checkFPSInterval.bind(this),i.fpsDroppedMonitoringPeriod)}}checkFPS(t,e,i){const s=performance.now();if(e){if(this.lastTime){const t=s-this.lastTime,r=i-this.lastDroppedFrames,n=e-this.lastDecodedFrames,a=1e3*r/t,o=this.hls;if(o.trigger(Gt.FPS_DROP,{currentDropped:r,currentDecoded:n,totalDroppedFrames:i}),a>0&&r>o.config.fpsDroppedMonitoringThreshold*n){let t=o.currentLevel;Xt.warn("drop FPS ratio greater than max allowed value for currentLevel: "+t),t>0&&(-1===o.autoLevelCapping||o.autoLevelCapping>=t)&&(t-=1,o.trigger(Gt.FPS_DROP_LEVEL_CAPPING,{level:t,droppedLevel:o.currentLevel}),o.autoLevelCapping=t,this.streamController.nextLevelSwitch())}}this.lastTime=s,this.lastDroppedFrames=i,this.lastDecodedFrames=e}}checkFPSInterval(){const t=this.media;if(t)if(this.isVideoPlaybackQualityAvailable){const e=t.getVideoPlaybackQuality();this.checkFPS(t,e.totalVideoFrames,e.droppedVideoFrames)}else this.checkFPS(t,t.webkitDecodedFrameCount,t.webkitDroppedFrameCount)}}const ul="[eme]";class fl{constructor(t){this.hls=void 0,this.config=void 0,this.media=null,this.keyFormatPromise=null,this.keySystemAccessPromises={},this._requestLicenseFailureCount=0,this.mediaKeySessions=[],this.keyIdToKeySessionPromise={},this.setMediaKeysQueue=fl.CDMCleanupPromise?[fl.CDMCleanupPromise]:[],this.onMediaEncrypted=this._onMediaEncrypted.bind(this),this.onWaitingForKey=this._onWaitingForKey.bind(this),this.debug=Xt.debug.bind(Xt,ul),this.log=Xt.log.bind(Xt,ul),this.warn=Xt.warn.bind(Xt,ul),this.error=Xt.error.bind(Xt,ul),this.hls=t,this.config=t.config,this.registerListeners()}destroy(){this.unregisterListeners(),this.onMediaDetached();const t=this.config;t.requestMediaKeySystemAccessFunc=null,t.licenseXhrSetup=t.licenseResponseCallback=void 0,t.drmSystems=t.drmSystemOptions={},this.hls=this.onMediaEncrypted=this.onWaitingForKey=this.keyIdToKeySessionPromise=null,this.config=null}registerListeners(){this.hls.on(Gt.MEDIA_ATTACHED,this.onMediaAttached,this),this.hls.on(Gt.MEDIA_DETACHED,this.onMediaDetached,this),this.hls.on(Gt.MANIFEST_LOADING,this.onManifestLoading,this),this.hls.on(Gt.MANIFEST_LOADED,this.onManifestLoaded,this)}unregisterListeners(){this.hls.off(Gt.MEDIA_ATTACHED,this.onMediaAttached,this),this.hls.off(Gt.MEDIA_DETACHED,this.onMediaDetached,this),this.hls.off(Gt.MANIFEST_LOADING,this.onManifestLoading,this),this.hls.off(Gt.MANIFEST_LOADED,this.onManifestLoaded,this)}getLicenseServerUrl(t){const{drmSystems:e,widevineLicenseUrl:i}=this.config,s=e[t];if(s)return s.licenseUrl;if(t===pe.WIDEVINE&&i)return i;throw new Error(`no license server URL configured for key-system "${t}"`)}getServerCertificateUrl(t){const{drmSystems:e}=this.config,i=e[t];if(i)return i.serverCertificateUrl;this.log(`No Server Certificate in config.drmSystems["${t}"]`)}attemptKeySystemAccess(t){const e=this.hls.levels,i=(t,e,i)=>!!t&&i.indexOf(t)===e,s=e.map(t=>t.audioCodec).filter(i),r=e.map(t=>t.videoCodec).filter(i);return s.length+r.length===0&&r.push("avc1.42e01e"),new Promise((e,i)=>{const n=t=>{const a=t.shift();this.getMediaKeysPromise(a,s,r).then(t=>e({keySystem:a,mediaKeys:t})).catch(e=>{t.length?n(t):i(e instanceof gl?e:new gl({type:jt.KEY_SYSTEM_ERROR,details:Kt.KEY_SYSTEM_NO_ACCESS,error:e,fatal:!0},e.message))})};n(t)})}requestMediaKeySystemAccess(t,e){const{requestMediaKeySystemAccessFunc:i}=this.config;if("function"!==typeof i){let t="Configured requestMediaKeySystemAccess is not a function "+i;return null===Ae&&"http:"===self.location.protocol&&(t="navigator.requestMediaKeySystemAccess is not available over insecure protocol "+location.protocol),Promise.reject(new Error(t))}return i(t,e)}getMediaKeysPromise(t,e,i){const s=Le(t,e,i,this.config.drmSystemOptions),r=this.keySystemAccessPromises[t];let n=null==r?void 0:r.keySystemAccess;if(!n){this.log(`Requesting encrypted media "${t}" key-system access with config: ${JSON.stringify(s)}`),n=this.requestMediaKeySystemAccess(t,s);const e=this.keySystemAccessPromises[t]={keySystemAccess:n};return n.catch(e=>{this.log(`Failed to obtain access to key-system "${t}": ${e}`)}),n.then(i=>{this.log(`Access for key-system "${i.keySystem}" obtained`);const s=this.fetchServerCertificate(t);return this.log(`Create media-keys for "${t}"`),e.mediaKeys=i.createMediaKeys().then(e=>(this.log(`Media-keys created for "${t}"`),s.then(i=>i?this.setMediaKeysServerCertificate(e,t,i):e))),e.mediaKeys.catch(e=>{this.error(`Failed to create media-keys for "${t}"}: ${e}`)}),e.mediaKeys})}return n.then(()=>r.mediaKeys)}createMediaKeySessionContext({decryptdata:t,keySystem:e,mediaKeys:i}){this.log(`Creating key-system session "${e}" keyId: ${Ve.hexDump(t.keyId||[])}`);const s=i.createSession(),r={decryptdata:t,keySystem:e,mediaKeys:i,mediaKeysSession:s,keyStatus:"status-pending"};return this.mediaKeySessions.push(r),r}renewKeySession(t){const e=t.decryptdata;if(e.pssh){const i=this.createMediaKeySessionContext(t),s=this.getKeyIdString(e),r="cenc";this.keyIdToKeySessionPromise[s]=this.generateRequestWithPreferredKeySession(i,r,e.pssh,"expired")}else this.warn("Could not renew expired session. Missing pssh initData.");this.removeSession(t)}getKeyIdString(t){if(!t)throw new Error("Could not read keyId of undefined decryptdata");if(null===t.keyId)throw new Error("keyId is null");return Ve.hexDump(t.keyId)}updateKeySession(t,e){var i;const s=t.mediaKeysSession;return this.log(`Updating key-session "${s.sessionId}" for keyID ${Ve.hexDump((null==(i=t.decryptdata)?void 0:i.keyId)||[])}\n      } (data length: ${e?e.byteLength:e})`),s.update(e)}selectKeySystemFormat(t){const e=Object.keys(t.levelkeys||{});return this.keyFormatPromise||(this.log(`Selecting key-system from fragment (sn: ${t.sn} ${t.type}: ${t.level}) key formats ${e.join(", ")}`),this.keyFormatPromise=this.getKeyFormatPromise(e)),this.keyFormatPromise}getKeyFormatPromise(t){return new Promise((e,i)=>{const s=be(this.config),r=t.map(ye).filter(t=>!!t&&-1!==s.indexOf(t));return this.getKeySystemSelectionPromise(r).then(({keySystem:t})=>{const s=Se(t);s?e(s):i(new Error(`Unable to find format for key-system "${t}"`))}).catch(i)})}loadKey(t){const e=t.keyInfo.decryptdata,i=this.getKeyIdString(e),s=`(keyId: ${i} format: "${e.keyFormat}" method: ${e.method} uri: ${e.uri})`;this.log("Starting session for key "+s);let r=this.keyIdToKeySessionPromise[i];return r||(r=this.keyIdToKeySessionPromise[i]=this.getKeySystemForKeyPromise(e).then(({keySystem:i,mediaKeys:r})=>(this.throwIfDestroyed(),this.log(`Handle encrypted media sn: ${t.frag.sn} ${t.frag.type}: ${t.frag.level} using key ${s}`),this.attemptSetMediaKeys(i,r).then(()=>{this.throwIfDestroyed();const t=this.createMediaKeySessionContext({keySystem:i,mediaKeys:r,decryptdata:e}),s="cenc";return this.generateRequestWithPreferredKeySession(t,s,e.pssh,"playlist-key")}))),r.catch(t=>this.handleError(t))),r}throwIfDestroyed(t="Invalid state"){if(!this.hls)throw new Error("invalid state")}handleError(t){this.hls&&(this.error(t.message),t instanceof gl?this.hls.trigger(Gt.ERROR,t.data):this.hls.trigger(Gt.ERROR,{type:jt.KEY_SYSTEM_ERROR,details:Kt.KEY_SYSTEM_NO_KEYS,error:t,fatal:!0}))}getKeySystemForKeyPromise(t){const e=this.getKeyIdString(t),i=this.keyIdToKeySessionPromise[e];if(!i){const e=ye(t.keyFormat),i=e?[e]:be(this.config);return this.attemptKeySystemAccess(i)}return i}getKeySystemSelectionPromise(t){if(t.length||(t=be(this.config)),0===t.length)throw new gl({type:jt.KEY_SYSTEM_ERROR,details:Kt.KEY_SYSTEM_NO_CONFIGURED_LICENSE,fatal:!0},"Missing key-system license configuration options "+JSON.stringify({drmSystems:this.config.drmSystems}));return this.attemptKeySystemAccess(t)}_onMediaEncrypted(t){const{initDataType:e,initData:i}=t;if(this.debug(`"${t.type}" event: init data type: "${e}"`),null===i)return;let s,r;if("sinf"===e&&this.config.drmSystems[pe.FAIRPLAY]){const t=We(new Uint8Array(i));try{const e=ce(JSON.parse(t).sinf),i=hi(new Uint8Array(e));if(!i)return;s=i.subarray(8,24),r=pe.FAIRPLAY}catch(h){return void this.warn('Failed to parse sinf "encrypted" event message initData')}}else{const t=Li(i);if(null===t)return;0===t.version&&t.systemId===Ee.WIDEVINE&&t.data&&(s=t.data.subarray(8,24)),r=Te(t.systemId)}if(!r||!s)return;const n=Ve.hexDump(s),{keyIdToKeySessionPromise:a,mediaKeySessions:o}=this;let l=a[n];for(let c=0;c<o.length;c++){const t=o[c],r=t.decryptdata;if(r.pssh||!r.keyId)continue;const h=Ve.hexDump(r.keyId);if(n===h||-1!==r.uri.replace(/-/g,"").indexOf(n)){l=a[h],delete a[h],r.pssh=new Uint8Array(i),r.keyId=s,l=a[n]=l.then(()=>this.generateRequestWithPreferredKeySession(t,e,i,"encrypted-event-key-match"));break}}l||(l=a[n]=this.getKeySystemSelectionPromise([r]).then(({keySystem:t,mediaKeys:r})=>{var a;this.throwIfDestroyed();const o=new Di("ISO-23001-7",n,null!=(a=Se(t))?a:"");return o.pssh=new Uint8Array(i),o.keyId=s,this.attemptSetMediaKeys(t,r).then(()=>{this.throwIfDestroyed();const s=this.createMediaKeySessionContext({decryptdata:o,keySystem:t,mediaKeys:r});return this.generateRequestWithPreferredKeySession(s,e,i,"encrypted-event-no-match")})})),l.catch(t=>this.handleError(t))}_onWaitingForKey(t){this.log(`"${t.type}" event`)}attemptSetMediaKeys(t,e){const i=this.setMediaKeysQueue.slice();this.log(`Setting media-keys for "${t}"`);const s=Promise.all(i).then(()=>{if(!this.media)throw new Error("Attempted to set mediaKeys without media element attached");return this.media.setMediaKeys(e)});return this.setMediaKeysQueue.push(s),s.then(()=>{this.log(`Media-keys set for "${t}"`),i.push(s),this.setMediaKeysQueue=this.setMediaKeysQueue.filter(t=>-1===i.indexOf(t))})}generateRequestWithPreferredKeySession(t,e,i,s){var r,n;const a=null==(r=this.config.drmSystems)||null==(n=r[t.keySystem])?void 0:n.generateRequest;if(a)try{const s=a.call(this.hls,e,i,t);if(!s)throw new Error("Invalid response from configured generateRequest filter");e=s.initDataType,i=t.decryptdata.pssh=s.initData?new Uint8Array(s.initData):null}catch(f){var o;if(this.warn(f.message),null!=(o=this.hls)&&o.config.debug)throw f}if(null===i)return this.log(`Skipping key-session request for "${s}" (no initData)`),Promise.resolve(t);const l=this.getKeyIdString(t.decryptdata);this.log(`Generating key-session request for "${s}": ${l} (init data type: ${e} length: ${i?i.byteLength:null})`);const h=new ja,c=t._onmessage=e=>{const i=t.mediaKeysSession;if(!i)return void h.emit("error",new Error("invalid state"));const{messageType:s,message:r}=e;this.log(`"${s}" message event for session "${i.sessionId}" message size: ${r.byteLength}`),"license-request"===s||"license-renewal"===s?this.renewLicense(t,r).catch(t=>{this.handleError(t),h.emit("error",t)}):"license-release"===s?t.keySystem===pe.FAIRPLAY&&(this.updateKeySession(t,ge("acknowledged")),this.removeSession(t)):this.warn(`unhandled media key message type "${s}"`)},d=t._onkeystatuseschange=e=>{const i=t.mediaKeysSession;if(!i)return void h.emit("error",new Error("invalid state"));this.onKeyStatusChange(t);const s=t.keyStatus;h.emit("keyStatus",s),"expired"===s&&(this.warn(`${t.keySystem} expired for key ${l}`),this.renewKeySession(t))};t.mediaKeysSession.addEventListener("message",c),t.mediaKeysSession.addEventListener("keystatuseschange",d);const u=new Promise((t,e)=>{h.on("error",e),h.on("keyStatus",i=>{i.startsWith("usable")?t():"output-restricted"===i?e(new gl({type:jt.KEY_SYSTEM_ERROR,details:Kt.KEY_SYSTEM_STATUS_OUTPUT_RESTRICTED,fatal:!1},"HDCP level output restricted")):"internal-error"===i?e(new gl({type:jt.KEY_SYSTEM_ERROR,details:Kt.KEY_SYSTEM_STATUS_INTERNAL_ERROR,fatal:!0},`key status changed to "${i}"`)):"expired"===i?e(new Error("key expired while generating request")):this.warn(`unhandled key status change "${i}"`)})});return t.mediaKeysSession.generateRequest(e,i).then(()=>{var e;this.log(`Request generated for key-session "${null==(e=t.mediaKeysSession)?void 0:e.sessionId}" keyId: ${l}`)}).catch(t=>{throw new gl({type:jt.KEY_SYSTEM_ERROR,details:Kt.KEY_SYSTEM_NO_SESSION,error:t,fatal:!1},"Error generating key-session request: "+t)}).then(()=>u).catch(e=>{throw h.removeAllListeners(),this.removeSession(t),e}).then(()=>(h.removeAllListeners(),t))}onKeyStatusChange(t){t.mediaKeysSession.keyStatuses.forEach((e,i)=>{this.log(`key status change "${e}" for keyStatuses keyId: ${Ve.hexDump("buffer"in i?new Uint8Array(i.buffer,i.byteOffset,i.byteLength):new Uint8Array(i))} session keyId: ${Ve.hexDump(new Uint8Array(t.decryptdata.keyId||[]))} uri: ${t.decryptdata.uri}`),t.keyStatus=e})}fetchServerCertificate(t){const e=this.config,i=e.loader,s=new i(e),r=this.getServerCertificateUrl(t);return r?(this.log(`Fetching server certificate for "${t}"`),new Promise((i,n)=>{const a={responseType:"arraybuffer",url:r},o=e.certLoadPolicy.default,l={loadPolicy:o,timeout:o.maxLoadTimeMs,maxRetry:0,retryDelay:0,maxRetryDelay:0},h={onSuccess:(t,e,s,r)=>{i(t.data)},onError:(e,i,s,o)=>{n(new gl({type:jt.KEY_SYSTEM_ERROR,details:Kt.KEY_SYSTEM_SERVER_CERTIFICATE_REQUEST_FAILED,fatal:!0,networkDetails:s,response:xt({url:a.url,data:void 0},e)},`"${t}" certificate request failed (${r}). Status: ${e.code} (${e.text})`))},onTimeout:(e,i,s)=>{n(new gl({type:jt.KEY_SYSTEM_ERROR,details:Kt.KEY_SYSTEM_SERVER_CERTIFICATE_REQUEST_FAILED,fatal:!0,networkDetails:s,response:{url:a.url,data:void 0}},`"${t}" certificate request timed out (${r})`))},onAbort:(t,e,i)=>{n(new Error("aborted"))}};s.load(a,l,h)})):Promise.resolve()}setMediaKeysServerCertificate(t,e,i){return new Promise((s,r)=>{t.setServerCertificate(i).then(r=>{this.log(`setServerCertificate ${r?"success":"not supported by CDM"} (${null==i?void 0:i.byteLength}) on "${e}"`),s(t)}).catch(t=>{r(new gl({type:jt.KEY_SYSTEM_ERROR,details:Kt.KEY_SYSTEM_SERVER_CERTIFICATE_UPDATE_FAILED,error:t,fatal:!0},t.message))})})}renewLicense(t,e){return this.requestLicense(t,new Uint8Array(e)).then(e=>this.updateKeySession(t,new Uint8Array(e)).catch(t=>{throw new gl({type:jt.KEY_SYSTEM_ERROR,details:Kt.KEY_SYSTEM_SESSION_UPDATE_FAILED,error:t,fatal:!0},t.message)}))}unpackPlayReadyKeyMessage(t,e){const i=String.fromCharCode.apply(null,new Uint16Array(e.buffer));if(!i.includes("PlayReadyKeyMessage"))return t.setRequestHeader("Content-Type","text/xml; charset=utf-8"),e;const s=(new DOMParser).parseFromString(i,"application/xml"),r=s.querySelectorAll("HttpHeader");if(r.length>0){let e;for(let i=0,s=r.length;i<s;i++){var n,a;e=r[i];const s=null==(n=e.querySelector("name"))?void 0:n.textContent,o=null==(a=e.querySelector("value"))?void 0:a.textContent;s&&o&&t.setRequestHeader(s,o)}}const o=s.querySelector("Challenge"),l=null==o?void 0:o.textContent;if(!l)throw new Error("Cannot find <Challenge> in key message");return ge(atob(l))}setupLicenseXHR(t,e,i,s){const r=this.config.licenseXhrSetup;return r?Promise.resolve().then(()=>{if(!i.decryptdata)throw new Error("Key removed");return r.call(this.hls,t,e,i,s)}).catch(n=>{if(!i.decryptdata)throw n;return t.open("POST",e,!0),r.call(this.hls,t,e,i,s)}).then(i=>{t.readyState||t.open("POST",e,!0);const r=i||s;return{xhr:t,licenseChallenge:r}}):(t.open("POST",e,!0),Promise.resolve({xhr:t,licenseChallenge:s}))}requestLicense(t,e){const i=this.config.keyLoadPolicy.default;return new Promise((s,r)=>{const n=this.getLicenseServerUrl(t.keySystem);this.log("Sending license request to URL: "+n);const a=new XMLHttpRequest;a.responseType="arraybuffer",a.onreadystatechange=()=>{if(!this.hls||!t.mediaKeysSession)return r(new Error("invalid state"));if(4===a.readyState)if(200===a.status){this._requestLicenseFailureCount=0;let e=a.response;this.log("License received "+(e instanceof ArrayBuffer?e.byteLength:e));const i=this.config.licenseResponseCallback;if(i)try{e=i.call(this.hls,a,n,t)}catch(o){this.error(o)}s(e)}else{const o=i.errorRetry,l=o?o.maxNumRetry:0;if(this._requestLicenseFailureCount++,this._requestLicenseFailureCount>l||a.status>=400&&a.status<500)r(new gl({type:jt.KEY_SYSTEM_ERROR,details:Kt.KEY_SYSTEM_LICENSE_REQUEST_FAILED,fatal:!0,networkDetails:a,response:{url:n,data:void 0,code:a.status,text:a.statusText}},`License Request XHR failed (${n}). Status: ${a.status} (${a.statusText})`));else{const i=l-this._requestLicenseFailureCount+1;this.warn(`Retrying license request, ${i} attempts left`),this.requestLicense(t,e).then(s,r)}}},t.licenseXhr&&t.licenseXhr.readyState!==XMLHttpRequest.DONE&&t.licenseXhr.abort(),t.licenseXhr=a,this.setupLicenseXHR(a,n,t,e).then(({xhr:e,licenseChallenge:i})=>{t.keySystem==pe.PLAYREADY&&(i=this.unpackPlayReadyKeyMessage(e,i)),e.send(i)})})}onMediaAttached(t,e){if(!this.config.emeEnabled)return;const i=e.media;this.media=i,i.addEventListener("encrypted",this.onMediaEncrypted),i.addEventListener("waitingforkey",this.onWaitingForKey)}onMediaDetached(){const t=this.media,e=this.mediaKeySessions;t&&(t.removeEventListener("encrypted",this.onMediaEncrypted),t.removeEventListener("waitingforkey",this.onWaitingForKey),this.media=null),this._requestLicenseFailureCount=0,this.setMediaKeysQueue=[],this.mediaKeySessions=[],this.keyIdToKeySessionPromise={},Di.clearKeyUriToKeyIdMap();const i=e.length;fl.CDMCleanupPromise=Promise.all(e.map(t=>this.removeSession(t)).concat(null==t?void 0:t.setMediaKeys(null).catch(t=>{this.log("Could not clear media keys: "+t)}))).then(()=>{i&&(this.log("finished closing key sessions and clearing media keys"),e.length=0)}).catch(t=>{this.log("Could not close sessions and clear media keys: "+t)})}onManifestLoading(){this.keyFormatPromise=null}onManifestLoaded(t,{sessionKeys:e}){if(e&&this.config.emeEnabled&&!this.keyFormatPromise){const t=e.reduce((t,e)=>(-1===t.indexOf(e.keyFormat)&&t.push(e.keyFormat),t),[]);this.log("Selecting key-system from session-keys "+t.join(", ")),this.keyFormatPromise=this.getKeyFormatPromise(t)}}removeSession(t){const{mediaKeysSession:e,licenseXhr:i}=t;if(e){this.log("Remove licenses and keys and close session "+e.sessionId),t._onmessage&&(e.removeEventListener("message",t._onmessage),t._onmessage=void 0),t._onkeystatuseschange&&(e.removeEventListener("keystatuseschange",t._onkeystatuseschange),t._onkeystatuseschange=void 0),i&&i.readyState!==XMLHttpRequest.DONE&&i.abort(),t.mediaKeysSession=t.decryptdata=t.licenseXhr=void 0;const s=this.mediaKeySessions.indexOf(t);return s>-1&&this.mediaKeySessions.splice(s,1),e.remove().catch(t=>{this.log("Could not remove session: "+t)}).then(()=>e.close()).catch(t=>{this.log("Could not close session: "+t)})}}}fl.CDMCleanupPromise=void 0;class gl extends Error{constructor(t,e){super(e),this.data=void 0,t.error||(t.error=new Error(e)),this.data=t,t.err=t.error}}var ml,pl,vl;(function(t){t["MANIFEST"]="m",t["AUDIO"]="a",t["VIDEO"]="v",t["MUXED"]="av",t["INIT"]="i",t["CAPTION"]="c",t["TIMED_TEXT"]="tt",t["KEY"]="k",t["OTHER"]="o"})(ml||(ml={})),function(t){t["DASH"]="d",t["HLS"]="h",t["SMOOTH"]="s",t["OTHER"]="o"}(pl||(pl={})),function(t){t["OBJECT"]="CMCD-Object",t["REQUEST"]="CMCD-Request",t["SESSION"]="CMCD-Session",t["STATUS"]="CMCD-Status"}(vl||(vl={}));const yl={[vl.OBJECT]:["br","d","ot","tb"],[vl.REQUEST]:["bl","dl","mtp","nor","nrr","su"],[vl.SESSION]:["cid","pr","sf","sid","st","v"],[vl.STATUS]:["bs","rtp"]};class El{constructor(t,e){this.value=void 0,this.params=void 0,Array.isArray(t)&&(t=t.map(t=>t instanceof El?t:new El(t))),this.value=t,this.params=e}}class Tl{constructor(t){this.description=void 0,this.description=t}}const Sl="Dict";function bl(t){return Array.isArray(t)?JSON.stringify(t):t instanceof Map?"Map{}":t instanceof Set?"Set{}":"object"===typeof t?JSON.stringify(t):String(t)}function Al(t,e,i,s){return new Error(`failed to ${t} "${bl(e)}" as ${i}`,{cause:s})}const Ll="Bare Item",Rl="Boolean",Dl="Byte Sequence",Il="Decimal",kl="Integer";function wl(t){return t<-999999999999999||999999999999999<t}const Cl=/[\x00-\x1f\x7f]+/,_l="Token",Ol="Key";function xl(t,e,i){return Al("serialize",t,e,i)}function Pl(t){if("boolean"!==typeof t)throw xl(t,Rl);return t?"?1":"?0"}function Ml(t){return btoa(String.fromCharCode(...t))}function Fl(t){if(!1===ArrayBuffer.isView(t))throw xl(t,Dl);return`:${Ml(t)}:`}function Nl(t){if(wl(t))throw xl(t,kl);return t.toString()}function Ul(t){return"@"+Nl(t.getTime()/1e3)}function Bl(t,e){if(t<0)return-Bl(-t,e);const i=Math.pow(10,e),s=Math.abs(t*i%1-.5)<Number.EPSILON;if(s){const e=Math.floor(t*i);return(e%2===0?e:e+1)/i}return Math.round(t*i)/i}function $l(t){const e=Bl(t,3);if(Math.floor(Math.abs(e)).toString().length>12)throw xl(t,Il);const i=e.toString();return i.includes(".")?i:i+".0"}const Gl="String";function jl(t){if(Cl.test(t))throw xl(t,Gl);return`"${t.replace(/\\/g,"\\\\").replace(/"/g,'\\"')}"`}function Kl(t){return t.description||t.toString().slice(7,-1)}function Vl(t){const e=Kl(t);if(!1===/^([a-zA-Z*])([!#$%&'*+\-.^_`|~\w:/]*)$/.test(e))throw xl(e,_l);return e}function Hl(t){switch(typeof t){case"number":if(!Ut(t))throw xl(t,Ll);return Number.isInteger(t)?Nl(t):$l(t);case"string":return jl(t);case"symbol":return Vl(t);case"boolean":return Pl(t);case"object":if(t instanceof Date)return Ul(t);if(t instanceof Uint8Array)return Fl(t);if(t instanceof Tl)return Vl(t);default:throw xl(t,Ll)}}function ql(t){if(!1===/^[a-z*][a-z0-9\-_.*]*$/.test(t))throw xl(t,Ol);return t}function Yl(t){return null==t?"":Object.entries(t).map(([t,e])=>!0===e?";"+ql(t):`;${ql(t)}=${Hl(e)}`).join("")}function Wl(t){return t instanceof El?`${Hl(t.value)}${Yl(t.params)}`:Hl(t)}function zl(t){return`(${t.value.map(Wl).join(" ")})${Yl(t.params)}`}function Xl(t,e={whitespace:!0}){if("object"!==typeof t)throw xl(t,Sl);const i=t instanceof Map?t.entries():Object.entries(t),s=null!=e&&e.whitespace?" ":"";return Array.from(i).map(([t,e])=>{e instanceof El===!1&&(e=new El(e));let i=ql(t);return!0===e.value?i+=Yl(e.params):(i+="=",Array.isArray(e.value)?i+=zl(e):i+=Wl(e)),i}).join(","+s)}function Ql(t,e){return Xl(t,e)}const Jl=t=>"ot"===t||"sf"===t||"st"===t,Zl=t=>"number"===typeof t?Ut(t):null!=t&&""!==t&&!1!==t;function th(t,e){const i=new URL(t),s=new URL(e);if(i.origin!==s.origin)return t;const r=i.pathname.split("/").slice(1),n=s.pathname.split("/").slice(1,-1);while(r[0]===n[0])r.shift(),n.shift();while(n.length)n.shift(),r.unshift("..");return r.join("/")}function eh(){try{return crypto.randomUUID()}catch(t){try{const t=URL.createObjectURL(new Blob),e=t.toString();return URL.revokeObjectURL(t),e.slice(e.lastIndexOf("/")+1)}catch(t){let e=(new Date).getTime();const i="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,t=>{const i=(e+16*Math.random())%16|0;return e=Math.floor(e/16),("x"==t?i:3&i|8).toString(16)});return i}}}const ih=t=>Math.round(t),sh=(t,e)=>(null!=e&&e.baseUrl&&(t=th(t,e.baseUrl)),encodeURIComponent(t)),rh=t=>100*ih(t/100),nh={br:ih,d:ih,bl:rh,dl:rh,mtp:rh,nor:sh,rtp:rh,tb:ih};function ah(t,e){const i={};if(null==t||"object"!==typeof t)return i;const s=Object.keys(t).sort(),r=Nt({},nh,null==e?void 0:e.formatters),n=null==e?void 0:e.filter;return s.forEach(s=>{if(null!=n&&n(s))return;let a=t[s];const o=r[s];o&&(a=o(a,e)),"v"===s&&1===a||"pr"==s&&1===a||Zl(a)&&(Jl(s)&&"string"===typeof a&&(a=new Tl(a)),i[s]=a)}),i}function oh(t,e={}){return t?Ql(ah(t,e),Nt({whitespace:!1},e)):""}function lh(t,e={}){if(!t)return{};const i=Object.entries(t),s=Object.entries(yl).concat(Object.entries((null==e?void 0:e.customHeaderMap)||{})),r=i.reduce((t,e)=>{var i;const[r,n]=e,a=(null==(i=s.find(t=>t[1].includes(r)))?void 0:i[0])||vl.REQUEST;return null!=t[a]||(t[a]={}),t[a][r]=n,t},{});return Object.entries(r).reduce((t,[i,s])=>(t[i]=oh(s,e),t),{})}function hh(t,e,i){return Nt(t,lh(e,i))}const ch="CMCD";function dh(t,e={}){if(!t)return"";const i=oh(t,e);return`${ch}=${encodeURIComponent(i)}`}const uh=/CMCD=[^&#]+/;function fh(t,e,i){const s=dh(e,i);if(!s)return t;if(uh.test(t))return t.replace(uh,s);const r=t.includes("?")?"&":"?";return`${t}${r}${s}`}class gh{constructor(t){this.hls=void 0,this.config=void 0,this.media=void 0,this.sid=void 0,this.cid=void 0,this.useHeaders=!1,this.includeKeys=void 0,this.initialized=!1,this.starved=!1,this.buffering=!0,this.audioBuffer=void 0,this.videoBuffer=void 0,this.onWaiting=()=>{this.initialized&&(this.starved=!0),this.buffering=!0},this.onPlaying=()=>{this.initialized||(this.initialized=!0),this.buffering=!1},this.applyPlaylistData=t=>{try{this.apply(t,{ot:ml.MANIFEST,su:!this.initialized})}catch(e){Xt.warn("Could not generate manifest CMCD data.",e)}},this.applyFragmentData=t=>{try{const e=t.frag,i=this.hls.levels[e.level],s=this.getObjectType(e),r={d:1e3*e.duration,ot:s};s!==ml.VIDEO&&s!==ml.AUDIO&&s!=ml.MUXED||(r.br=i.bitrate/1e3,r.tb=this.getTopBandwidth(s)/1e3,r.bl=this.getBufferLength(s)),this.apply(t,r)}catch(e){Xt.warn("Could not generate segment CMCD data.",e)}},this.hls=t;const e=this.config=t.config,{cmcd:i}=e;null!=i&&(e.pLoader=this.createPlaylistLoader(),e.fLoader=this.createFragmentLoader(),this.sid=i.sessionId||eh(),this.cid=i.contentId,this.useHeaders=!0===i.useHeaders,this.includeKeys=i.includeKeys,this.registerListeners())}registerListeners(){const t=this.hls;t.on(Gt.MEDIA_ATTACHED,this.onMediaAttached,this),t.on(Gt.MEDIA_DETACHED,this.onMediaDetached,this),t.on(Gt.BUFFER_CREATED,this.onBufferCreated,this)}unregisterListeners(){const t=this.hls;t.off(Gt.MEDIA_ATTACHED,this.onMediaAttached,this),t.off(Gt.MEDIA_DETACHED,this.onMediaDetached,this),t.off(Gt.BUFFER_CREATED,this.onBufferCreated,this)}destroy(){this.unregisterListeners(),this.onMediaDetached(),this.hls=this.config=this.audioBuffer=this.videoBuffer=null,this.onWaiting=this.onPlaying=null}onMediaAttached(t,e){this.media=e.media,this.media.addEventListener("waiting",this.onWaiting),this.media.addEventListener("playing",this.onPlaying)}onMediaDetached(){this.media&&(this.media.removeEventListener("waiting",this.onWaiting),this.media.removeEventListener("playing",this.onPlaying),this.media=null)}onBufferCreated(t,e){var i,s;this.audioBuffer=null==(i=e.tracks.audio)?void 0:i.buffer,this.videoBuffer=null==(s=e.tracks.video)?void 0:s.buffer}createData(){var t;return{v:1,sf:pl.HLS,sid:this.sid,cid:this.cid,pr:null==(t=this.media)?void 0:t.playbackRate,mtp:this.hls.bandwidthEstimate/1e3}}apply(t,e={}){Nt(e,this.createData());const i=e.ot===ml.INIT||e.ot===ml.VIDEO||e.ot===ml.MUXED;this.starved&&i&&(e.bs=!0,e.su=!0,this.starved=!1),null==e.su&&(e.su=this.buffering);const{includeKeys:s}=this;s&&(e=Object.keys(e).reduce((t,i)=>(s.includes(i)&&(t[i]=e[i]),t),{})),this.useHeaders?(t.headers||(t.headers={}),hh(t.headers,e)):t.url=fh(t.url,e)}getObjectType(t){const{type:e}=t;return"subtitle"===e?ml.TIMED_TEXT:"initSegment"===t.sn?ml.INIT:"audio"===e?ml.AUDIO:"main"===e?this.hls.audioTracks.length?ml.VIDEO:ml.MUXED:void 0}getTopBandwidth(t){let e,i=0;const s=this.hls;if(t===ml.AUDIO)e=s.audioTracks;else{const t=s.maxAutoLevel,i=t>-1?t+1:s.levels.length;e=s.levels.slice(0,i)}for(const r of e)r.bitrate>i&&(i=r.bitrate);return i>0?i:NaN}getBufferLength(t){const e=this.hls.media,i=t===ml.AUDIO?this.audioBuffer:this.videoBuffer;if(!i||!e)return NaN;const s=Gr.bufferInfo(i,e.currentTime,this.config.maxBufferHole);return 1e3*s.len}createPlaylistLoader(){const{pLoader:t}=this.config,e=this.applyPlaylistData,i=t||this.config.loader;return class{constructor(t){this.loader=void 0,this.loader=new i(t)}get stats(){return this.loader.stats}get context(){return this.loader.context}destroy(){this.loader.destroy()}abort(){this.loader.abort()}load(t,i,s){e(t),this.loader.load(t,i,s)}}}createFragmentLoader(){const{fLoader:t}=this.config,e=this.applyFragmentData,i=t||this.config.loader;return class{constructor(t){this.loader=void 0,this.loader=new i(t)}get stats(){return this.loader.stats}get context(){return this.loader.context}destroy(){this.loader.destroy()}abort(){this.loader.abort()}load(t,i,s){e(t),this.loader.load(t,i,s)}}}}const mh=3e5;class ph{constructor(t){this.hls=void 0,this.log=void 0,this.loader=null,this.uri=null,this.pathwayId=".",this.pathwayPriority=null,this.timeToLoad=300,this.reloadTimer=-1,this.updated=0,this.started=!1,this.enabled=!0,this.levels=null,this.audioTracks=null,this.subtitleTracks=null,this.penalizedPathways={},this.hls=t,this.log=Xt.log.bind(Xt,"[content-steering]:"),this.registerListeners()}registerListeners(){const t=this.hls;t.on(Gt.MANIFEST_LOADING,this.onManifestLoading,this),t.on(Gt.MANIFEST_LOADED,this.onManifestLoaded,this),t.on(Gt.MANIFEST_PARSED,this.onManifestParsed,this),t.on(Gt.ERROR,this.onError,this)}unregisterListeners(){const t=this.hls;t&&(t.off(Gt.MANIFEST_LOADING,this.onManifestLoading,this),t.off(Gt.MANIFEST_LOADED,this.onManifestLoaded,this),t.off(Gt.MANIFEST_PARSED,this.onManifestParsed,this),t.off(Gt.ERROR,this.onError,this))}startLoad(){if(this.started=!0,this.clearTimeout(),this.enabled&&this.uri){if(this.updated){const t=1e3*this.timeToLoad-(performance.now()-this.updated);if(t>0)return void this.scheduleRefresh(this.uri,t)}this.loadSteeringManifest(this.uri)}}stopLoad(){this.started=!1,this.loader&&(this.loader.destroy(),this.loader=null),this.clearTimeout()}clearTimeout(){-1!==this.reloadTimer&&(self.clearTimeout(this.reloadTimer),this.reloadTimer=-1)}destroy(){this.unregisterListeners(),this.stopLoad(),this.hls=null,this.levels=this.audioTracks=this.subtitleTracks=null}removeLevel(t){const e=this.levels;e&&(this.levels=e.filter(e=>e!==t))}onManifestLoading(){this.stopLoad(),this.enabled=!0,this.timeToLoad=300,this.updated=0,this.uri=null,this.pathwayId=".",this.levels=this.audioTracks=this.subtitleTracks=null}onManifestLoaded(t,e){const{contentSteering:i}=e;null!==i&&(this.pathwayId=i.pathwayId,this.uri=i.uri,this.started&&this.startLoad())}onManifestParsed(t,e){this.audioTracks=e.audioTracks,this.subtitleTracks=e.subtitleTracks}onError(t,e){const{errorAction:i}=e;if((null==i?void 0:i.action)===dr.SendAlternateToPenaltyBox&&i.flags===ur.MoveAllAlternatesMatchingHost){const t=this.levels;let s=this.pathwayPriority,r=this.pathwayId;if(e.context){const{groupId:i,pathwayId:s,type:n}=e.context;i&&t?r=this.getPathwayForGroupId(i,n,r):s&&(r=s)}r in this.penalizedPathways||(this.penalizedPathways[r]=performance.now()),!s&&t&&(s=t.reduce((t,e)=>(-1===t.indexOf(e.pathwayId)&&t.push(e.pathwayId),t),[])),s&&s.length>1&&(this.updatePathwayPriority(s),i.resolved=this.pathwayId!==r),i.resolved||Xt.warn(`Could not resolve ${e.details} ("${e.error.message}") with content-steering for Pathway: ${r} levels: ${t?t.length:t} priorities: ${JSON.stringify(s)} penalized: ${JSON.stringify(this.penalizedPathways)}`)}}filterParsedLevels(t){this.levels=t;let e=this.getLevelsForPathway(this.pathwayId);if(0===e.length){const i=t[0].pathwayId;this.log(`No levels found in Pathway ${this.pathwayId}. Setting initial Pathway to "${i}"`),e=this.getLevelsForPathway(i),this.pathwayId=i}return e.length!==t.length?(this.log(`Found ${e.length}/${t.length} levels in Pathway "${this.pathwayId}"`),e):t}getLevelsForPathway(t){return null===this.levels?[]:this.levels.filter(e=>t===e.pathwayId)}updatePathwayPriority(t){let e;this.pathwayPriority=t;const i=this.penalizedPathways,s=performance.now();Object.keys(i).forEach(t=>{s-i[t]>mh&&delete i[t]});for(let r=0;r<t.length;r++){const s=t[r];if(s in i)continue;if(s===this.pathwayId)return;const n=this.hls.nextLoadLevel,a=this.hls.levels[n];if(e=this.getLevelsForPathway(s),e.length>0){this.log(`Setting Pathway to "${s}"`),this.pathwayId=s,Qs(e),this.hls.trigger(Gt.LEVELS_UPDATED,{levels:e});const t=this.hls.levels[n];a&&t&&this.levels&&(t.attrs["STABLE-VARIANT-ID"]!==a.attrs["STABLE-VARIANT-ID"]&&t.bitrate!==a.bitrate&&this.log(`Unstable Pathways change from bitrate ${a.bitrate} to ${t.bitrate}`),this.hls.nextLoadLevel=n);break}}}getPathwayForGroupId(t,e,i){const s=this.getLevelsForPathway(i).concat(this.levels||[]);for(let r=0;r<s.length;r++)if(e===hs.AUDIO_TRACK&&s[r].hasAudioGroup(t)||e===hs.SUBTITLE_TRACK&&s[r].hasSubtitleGroup(t))return s[r].pathwayId;return i}clonePathways(t){const e=this.levels;if(!e)return;const i={},s={};t.forEach(t=>{const{ID:r,"BASE-ID":n,"URI-REPLACEMENT":a}=t;if(e.some(t=>t.pathwayId===r))return;const o=this.getLevelsForPathway(n).map(t=>{const e=new Zt(t.attrs);e["PATHWAY-ID"]=r;const n=e.AUDIO&&`${e.AUDIO}_clone_${r}`,o=e.SUBTITLES&&`${e.SUBTITLES}_clone_${r}`;n&&(i[e.AUDIO]=n,e.AUDIO=n),o&&(s[e.SUBTITLES]=o,e.SUBTITLES=o);const l=yh(t.uri,e["STABLE-VARIANT-ID"],"PER-VARIANT-URIS",a),h=new Ns({attrs:e,audioCodec:t.audioCodec,bitrate:t.bitrate,height:t.height,name:t.name,url:l,videoCodec:t.videoCodec,width:t.width});if(t.audioGroups)for(let i=1;i<t.audioGroups.length;i++)h.addGroupId("audio",`${t.audioGroups[i]}_clone_${r}`);if(t.subtitleGroups)for(let i=1;i<t.subtitleGroups.length;i++)h.addGroupId("text",`${t.subtitleGroups[i]}_clone_${r}`);return h});e.push(...o),vh(this.audioTracks,i,a,r),vh(this.subtitleTracks,s,a,r)})}loadSteeringManifest(t){const e=this.hls.config,i=e.loader;let s;this.loader&&this.loader.destroy(),this.loader=new i(e);try{s=new self.URL(t)}catch(h){return this.enabled=!1,void this.log("Failed to parse Steering Manifest URI: "+t)}if("data:"!==s.protocol){const t=0|(this.hls.bandwidthEstimate||e.abrEwmaDefaultEstimate);s.searchParams.set("_HLS_pathway",this.pathwayId),s.searchParams.set("_HLS_throughput",""+t)}const r={responseType:"json",url:s.href},n=e.steeringManifestLoadPolicy.default,a=n.errorRetry||n.timeoutRetry||{},o={loadPolicy:n,timeout:n.maxLoadTimeMs,maxRetry:a.maxNumRetry||0,retryDelay:a.retryDelayMs||0,maxRetryDelay:a.maxRetryDelayMs||0},l={onSuccess:(t,e,i,r)=>{this.log(`Loaded steering manifest: "${s}"`);const n=t.data;if(1!==n.VERSION)return void this.log(`Steering VERSION ${n.VERSION} not supported!`);this.updated=performance.now(),this.timeToLoad=n.TTL;const{"RELOAD-URI":a,"PATHWAY-CLONES":o,"PATHWAY-PRIORITY":l}=n;if(a)try{this.uri=new self.URL(a,s).href}catch(h){return this.enabled=!1,void this.log("Failed to parse Steering Manifest RELOAD-URI: "+a)}this.scheduleRefresh(this.uri||i.url),o&&this.clonePathways(o);const c={steeringManifest:n,url:s.toString()};this.hls.trigger(Gt.STEERING_MANIFEST_LOADED,c),l&&this.updatePathwayPriority(l)},onError:(t,e,i,s)=>{if(this.log(`Error loading steering manifest: ${t.code} ${t.text} (${e.url})`),this.stopLoad(),410===t.code)return this.enabled=!1,void this.log(`Steering manifest ${e.url} no longer available`);let r=1e3*this.timeToLoad;if(429!==t.code)this.scheduleRefresh(this.uri||e.url,r);else{const t=this.loader;if("function"===typeof(null==t?void 0:t.getResponseHeader)){const e=t.getResponseHeader("Retry-After");e&&(r=1e3*parseFloat(e))}this.log(`Steering manifest ${e.url} rate limited`)}},onTimeout:(t,e,i)=>{this.log(`Timeout loading steering manifest (${e.url})`),this.scheduleRefresh(this.uri||e.url)}};this.log("Requesting steering manifest: "+s),this.loader.load(r,o,l)}scheduleRefresh(t,e=1e3*this.timeToLoad){this.clearTimeout(),this.reloadTimer=self.setTimeout(()=>{var e;const i=null==(e=this.hls)?void 0:e.media;!i||i.ended?this.scheduleRefresh(t,1e3*this.timeToLoad):this.loadSteeringManifest(t)},e)}}function vh(t,e,i,s){t&&Object.keys(e).forEach(r=>{const n=t.filter(t=>t.groupId===r).map(t=>{const n=Nt({},t);return n.details=void 0,n.attrs=new Zt(n.attrs),n.url=n.attrs.URI=yh(t.url,t.attrs["STABLE-RENDITION-ID"],"PER-RENDITION-URIS",i),n.groupId=n.attrs["GROUP-ID"]=e[r],n.attrs["PATHWAY-ID"]=s,n});t.push(...n)})}function yh(t,e,i,s){const{HOST:r,PARAMS:n,[i]:a}=s;let o;e&&(o=null==a?void 0:a[e],o&&(t=o));const l=new self.URL(t);return r&&!o&&(l.host=r),n&&Object.keys(n).sort().forEach(t=>{t&&l.searchParams.set(t,n[t])}),l.href}const Eh=/^age:\s*[\d.]+\s*$/im;class Th{constructor(t){this.xhrSetup=void 0,this.requestTimeout=void 0,this.retryTimeout=void 0,this.retryDelay=void 0,this.config=null,this.callbacks=null,this.context=null,this.loader=null,this.stats=void 0,this.xhrSetup=t&&t.xhrSetup||null,this.stats=new se,this.retryDelay=0}destroy(){this.callbacks=null,this.abortInternal(),this.loader=null,this.config=null,this.context=null,this.xhrSetup=null}abortInternal(){const t=this.loader;self.clearTimeout(this.requestTimeout),self.clearTimeout(this.retryTimeout),t&&(t.onreadystatechange=null,t.onprogress=null,4!==t.readyState&&(this.stats.aborted=!0,t.abort()))}abort(){var t;this.abortInternal(),null!=(t=this.callbacks)&&t.onAbort&&this.callbacks.onAbort(this.stats,this.context,this.loader)}load(t,e,i){if(this.stats.loading.start)throw new Error("Loader can only be used once.");this.stats.loading.start=self.performance.now(),this.context=t,this.config=e,this.callbacks=i,this.loadInternal()}loadInternal(){const{config:t,context:e}=this;if(!t||!e)return;const i=this.loader=new self.XMLHttpRequest,s=this.stats;s.loading.first=0,s.loaded=0,s.aborted=!1;const r=this.xhrSetup;r?Promise.resolve().then(()=>{if(this.loader===i&&!this.stats.aborted)return r(i,e.url)}).catch(t=>{if(this.loader===i&&!this.stats.aborted)return i.open("GET",e.url,!0),r(i,e.url)}).then(()=>{this.loader!==i||this.stats.aborted||this.openAndSendXhr(i,e,t)}).catch(t=>{this.callbacks.onError({code:i.status,text:t.message},e,i,s)}):this.openAndSendXhr(i,e,t)}openAndSendXhr(t,e,i){t.readyState||t.open("GET",e.url,!0);const s=e.headers,{maxTimeToFirstByteMs:r,maxLoadTimeMs:n}=i.loadPolicy;if(s)for(const a in s)t.setRequestHeader(a,s[a]);e.rangeEnd&&t.setRequestHeader("Range","bytes="+e.rangeStart+"-"+(e.rangeEnd-1)),t.onreadystatechange=this.readystatechange.bind(this),t.onprogress=this.loadprogress.bind(this),t.responseType=e.responseType,self.clearTimeout(this.requestTimeout),i.timeout=r&&Ut(r)?r:n,this.requestTimeout=self.setTimeout(this.loadtimeout.bind(this),i.timeout),t.send()}readystatechange(){const{context:t,loader:e,stats:i}=this;if(!t||!e)return;const s=e.readyState,r=this.config;if(!i.aborted&&s>=2&&(0===i.loading.first&&(i.loading.first=Math.max(self.performance.now(),i.loading.start),r.timeout!==r.loadPolicy.maxLoadTimeMs&&(self.clearTimeout(this.requestTimeout),r.timeout=r.loadPolicy.maxLoadTimeMs,this.requestTimeout=self.setTimeout(this.loadtimeout.bind(this),r.loadPolicy.maxLoadTimeMs-(i.loading.first-i.loading.start)))),4===s)){self.clearTimeout(this.requestTimeout),e.onreadystatechange=null,e.onprogress=null;const s=e.status,n="text"!==e.responseType;if(s>=200&&s<300&&(n&&e.response||null!==e.responseText)){i.loading.end=Math.max(self.performance.now(),i.loading.first);const r=n?e.response:e.responseText,a="arraybuffer"===e.responseType?r.byteLength:r.length;if(i.loaded=i.total=a,i.bwEstimate=8e3*i.total/(i.loading.end-i.loading.first),!this.callbacks)return;const o=this.callbacks.onProgress;if(o&&o(i,t,r,e),!this.callbacks)return;const l={url:e.responseURL,data:r,code:s};this.callbacks.onSuccess(l,i,t,e)}else{const n=r.loadPolicy.errorRetry,a=i.retry,o={url:t.url,data:void 0,code:s};ir(n,a,!1,o)?this.retry(n):(Xt.error(`${s} while loading ${t.url}`),this.callbacks.onError({code:s,text:e.statusText},t,e,i))}}}loadtimeout(){if(!this.config)return;const t=this.config.loadPolicy.timeoutRetry,e=this.stats.retry;if(ir(t,e,!0))this.retry(t);else{var i;Xt.warn("timeout while loading "+(null==(i=this.context)?void 0:i.url));const t=this.callbacks;t&&(this.abortInternal(),t.onTimeout(this.stats,this.context,this.loader))}}retry(t){const{context:e,stats:i}=this;this.retryDelay=tr(t,i.retry),i.retry++,Xt.warn(`${status?"HTTP Status "+status:"Timeout"} while loading ${null==e?void 0:e.url}, retrying ${i.retry}/${t.maxNumRetry} in ${this.retryDelay}ms`),this.abortInternal(),this.loader=null,self.clearTimeout(this.retryTimeout),this.retryTimeout=self.setTimeout(this.loadInternal.bind(this),this.retryDelay)}loadprogress(t){const e=this.stats;e.loaded=t.loaded,t.lengthComputable&&(e.total=t.total)}getCacheAge(){let t=null;if(this.loader&&Eh.test(this.loader.getAllResponseHeaders())){const e=this.loader.getResponseHeader("age");t=e?parseFloat(e):null}return t}getResponseHeader(t){return this.loader&&new RegExp(`^${t}:\\s*[\\d.]+\\s*$`,"im").test(this.loader.getAllResponseHeaders())?this.loader.getResponseHeader(t):null}}function Sh(){if(self.fetch&&self.AbortController&&self.ReadableStream&&self.Request)try{return new self.ReadableStream({}),!0}catch(t){}return!1}const bh=/(\d+)-(\d+)\/(\d+)/;class Ah{constructor(t){this.fetchSetup=void 0,this.requestTimeout=void 0,this.request=null,this.response=null,this.controller=void 0,this.context=null,this.config=null,this.callbacks=null,this.stats=void 0,this.loader=null,this.fetchSetup=t.fetchSetup||Ih,this.controller=new self.AbortController,this.stats=new se}destroy(){this.loader=this.callbacks=this.context=this.config=this.request=null,this.abortInternal(),this.response=null,this.fetchSetup=this.controller=this.stats=null}abortInternal(){this.controller&&!this.stats.loading.end&&(this.stats.aborted=!0,this.controller.abort())}abort(){var t;this.abortInternal(),null!=(t=this.callbacks)&&t.onAbort&&this.callbacks.onAbort(this.stats,this.context,this.response)}load(t,e,i){const s=this.stats;if(s.loading.start)throw new Error("Loader can only be used once.");s.loading.start=self.performance.now();const r=Lh(t,this.controller.signal),n=i.onProgress,a="arraybuffer"===t.responseType,o=a?"byteLength":"length",{maxTimeToFirstByteMs:l,maxLoadTimeMs:h}=e.loadPolicy;this.context=t,this.config=e,this.callbacks=i,this.request=this.fetchSetup(t,r),self.clearTimeout(this.requestTimeout),e.timeout=l&&Ut(l)?l:h,this.requestTimeout=self.setTimeout(()=>{this.abortInternal(),i.onTimeout(s,t,this.response)},e.timeout),self.fetch(this.request).then(r=>{this.response=this.loader=r;const o=Math.max(self.performance.now(),s.loading.start);if(self.clearTimeout(this.requestTimeout),e.timeout=h,this.requestTimeout=self.setTimeout(()=>{this.abortInternal(),i.onTimeout(s,t,this.response)},h-(o-s.loading.start)),!r.ok){const{status:t,statusText:e}=r;throw new kh(e||"fetch, bad network response",t,r)}return s.loading.first=o,s.total=Dh(r.headers)||s.total,n&&Ut(e.highWaterMark)?this.loadProgressively(r,s,t,e.highWaterMark,n):a?r.arrayBuffer():"json"===t.responseType?r.json():r.text()}).then(r=>{const a=this.response;if(!a)throw new Error("loader destroyed");self.clearTimeout(this.requestTimeout),s.loading.end=Math.max(self.performance.now(),s.loading.first);const l=r[o];l&&(s.loaded=s.total=l);const h={url:a.url,data:r,code:a.status};n&&!Ut(e.highWaterMark)&&n(s,t,r,a),i.onSuccess(h,s,t,a)}).catch(e=>{if(self.clearTimeout(this.requestTimeout),s.aborted)return;const r=e&&e.code||0,n=e?e.message:null;i.onError({code:r,text:n},t,e?e.details:null,s)})}getCacheAge(){let t=null;if(this.response){const e=this.response.headers.get("age");t=e?parseFloat(e):null}return t}getResponseHeader(t){return this.response?this.response.headers.get(t):null}loadProgressively(t,e,i,s=0,r){const n=new fn,a=t.body.getReader(),o=()=>a.read().then(a=>{if(a.done)return n.dataLength&&r(e,i,n.flush(),t),Promise.resolve(new ArrayBuffer(0));const l=a.value,h=l.length;return e.loaded+=h,h<s||n.dataLength?(n.push(l),n.dataLength>=s&&r(e,i,n.flush(),t)):r(e,i,l,t),o()}).catch(()=>Promise.reject());return o()}}function Lh(t,e){const i={method:"GET",mode:"cors",credentials:"same-origin",signal:e,headers:new self.Headers(Nt({},t.headers))};return t.rangeEnd&&i.headers.set("Range","bytes="+t.rangeStart+"-"+String(t.rangeEnd-1)),i}function Rh(t){const e=bh.exec(t);if(e)return parseInt(e[2])-parseInt(e[1])+1}function Dh(t){const e=t.get("Content-Range");if(e){const t=Rh(e);if(Ut(t))return t}const i=t.get("Content-Length");if(i)return parseInt(i)}function Ih(t,e){return new self.Request(t.url,e)}class kh extends Error{constructor(t,e,i){super(t),this.code=void 0,this.details=void 0,this.code=e,this.details=i}}const wh=/\s/,Ch={newCue(t,e,i,s){const r=[];let n,a,o,l,h;const c=self.VTTCue||self.TextTrackCue;for(let u=0;u<s.rows.length;u++)if(n=s.rows[u],o=!0,l=0,h="",!n.isEmpty()){var d;for(let t=0;t<n.chars.length;t++)wh.test(n.chars[t].uchar)&&o?l++:(h+=n.chars[t].uchar,o=!1);n.cueStartTime=e,e===i&&(i+=1e-4),l>=16?l--:l++;const s=Mo(h.trim()),f=Go(e,i,s);null!=t&&null!=(d=t.cues)&&d.getCueById(f)||(a=new c(e,i,s),a.id=f,a.line=u+1,a.align="left",a.position=10+Math.min(80,10*Math.floor(8*l/32)),r.push(a))}return t&&r.length&&(r.sort((t,e)=>"auto"===t.line||"auto"===e.line?0:t.line>8&&e.line>8?e.line-t.line:t.line-e.line),r.forEach(e=>ms(t,e))),r}},_h={maxTimeToFirstByteMs:8e3,maxLoadTimeMs:2e4,timeoutRetry:null,errorRetry:null},Oh=xt(xt({autoStartLoad:!0,startPosition:-1,defaultAudioCodec:void 0,debug:!1,capLevelOnFPSDrop:!1,capLevelToPlayerSize:!1,ignoreDevicePixelRatio:!1,preferManagedMediaSource:!0,initialLiveManifestSize:1,maxBufferLength:30,backBufferLength:1/0,frontBufferFlushThreshold:1/0,maxBufferSize:6e7,maxBufferHole:.1,highBufferWatchdogPeriod:2,nudgeOffset:.1,nudgeMaxRetry:3,maxFragLookUpTolerance:.25,liveSyncDurationCount:3,liveMaxLatencyDurationCount:1/0,liveSyncDuration:void 0,liveMaxLatencyDuration:void 0,maxLiveSyncPlaybackRate:1,liveDurationInfinity:!1,liveBackBufferLength:null,maxMaxBufferLength:600,enableWorker:!0,workerPath:null,enableSoftwareAES:!0,startLevel:void 0,startFragPrefetch:!1,fpsDroppedMonitoringPeriod:5e3,fpsDroppedMonitoringThreshold:.2,appendErrorMaxRetry:3,loader:Th,fLoader:void 0,pLoader:void 0,xhrSetup:void 0,licenseXhrSetup:void 0,licenseResponseCallback:void 0,abrController:Pr,bufferController:io,capLevelController:cl,errorController:fr,fpsController:dl,stretchShortVideoTrack:!1,maxAudioFramesDrift:1,forceKeyFrameOnDiscontinuity:!0,abrEwmaFastLive:3,abrEwmaSlowLive:9,abrEwmaFastVoD:3,abrEwmaSlowVoD:9,abrEwmaDefaultEstimate:5e5,abrEwmaDefaultEstimateMax:5e6,abrBandWidthFactor:.95,abrBandWidthUpFactor:.7,abrMaxWithRealBitrate:!1,maxStarvationDelay:4,maxLoadingDelay:4,minAutoBitrate:0,emeEnabled:!1,widevineLicenseUrl:void 0,drmSystems:{},drmSystemOptions:{},requestMediaKeySystemAccessFunc:Ae,testBandwidth:!0,progressive:!1,lowLatencyMode:!0,cmcd:void 0,enableDateRangeMetadataCues:!0,enableEmsgMetadataCues:!0,enableID3MetadataCues:!0,useMediaCapabilities:!0,certLoadPolicy:{default:_h},keyLoadPolicy:{default:{maxTimeToFirstByteMs:8e3,maxLoadTimeMs:2e4,timeoutRetry:{maxNumRetry:1,retryDelayMs:1e3,maxRetryDelayMs:2e4,backoff:"linear"},errorRetry:{maxNumRetry:8,retryDelayMs:1e3,maxRetryDelayMs:2e4,backoff:"linear"}}},manifestLoadPolicy:{default:{maxTimeToFirstByteMs:1/0,maxLoadTimeMs:2e4,timeoutRetry:{maxNumRetry:2,retryDelayMs:0,maxRetryDelayMs:0},errorRetry:{maxNumRetry:1,retryDelayMs:1e3,maxRetryDelayMs:8e3}}},playlistLoadPolicy:{default:{maxTimeToFirstByteMs:1e4,maxLoadTimeMs:2e4,timeoutRetry:{maxNumRetry:2,retryDelayMs:0,maxRetryDelayMs:0},errorRetry:{maxNumRetry:2,retryDelayMs:1e3,maxRetryDelayMs:8e3}}},fragLoadPolicy:{default:{maxTimeToFirstByteMs:1e4,maxLoadTimeMs:12e4,timeoutRetry:{maxNumRetry:4,retryDelayMs:0,maxRetryDelayMs:0},errorRetry:{maxNumRetry:6,retryDelayMs:1e3,maxRetryDelayMs:8e3}}},steeringManifestLoadPolicy:{default:{maxTimeToFirstByteMs:1e4,maxLoadTimeMs:2e4,timeoutRetry:{maxNumRetry:2,retryDelayMs:0,maxRetryDelayMs:0},errorRetry:{maxNumRetry:1,retryDelayMs:1e3,maxRetryDelayMs:8e3}}},manifestLoadingTimeOut:1e4,manifestLoadingMaxRetry:1,manifestLoadingRetryDelay:1e3,manifestLoadingMaxRetryTimeout:64e3,levelLoadingTimeOut:1e4,levelLoadingMaxRetry:4,levelLoadingRetryDelay:1e3,levelLoadingMaxRetryTimeout:64e3,fragLoadingTimeOut:2e4,fragLoadingMaxRetry:6,fragLoadingRetryDelay:1e3,fragLoadingMaxRetryTimeout:64e3},xh()),{},{subtitleStreamController:Qa,subtitleTrackController:Za,timelineController:nl,audioStreamController:Wa,audioTrackController:za,emeController:fl,cmcdController:gh,contentSteeringController:ph});function xh(){return{cueHandler:Ch,enableWebVTT:!0,enableIMSC1:!0,enableCEA708Captions:!0,captionsTextTrack1Label:"English",captionsTextTrack1LanguageCode:"en",captionsTextTrack2Label:"Spanish",captionsTextTrack2LanguageCode:"es",captionsTextTrack3Label:"Unknown CC",captionsTextTrack3LanguageCode:"",captionsTextTrack4Label:"Unknown CC",captionsTextTrack4LanguageCode:"",renderTextTracksNatively:!0}}function Ph(t,e){if((e.liveSyncDurationCount||e.liveMaxLatencyDurationCount)&&(e.liveSyncDuration||e.liveMaxLatencyDuration))throw new Error("Illegal hls.js config: don't mix up liveSyncDurationCount/liveMaxLatencyDurationCount and liveSyncDuration/liveMaxLatencyDuration");if(void 0!==e.liveMaxLatencyDurationCount&&(void 0===e.liveSyncDurationCount||e.liveMaxLatencyDurationCount<=e.liveSyncDurationCount))throw new Error('Illegal hls.js config: "liveMaxLatencyDurationCount" must be greater than "liveSyncDurationCount"');if(void 0!==e.liveMaxLatencyDuration&&(void 0===e.liveSyncDuration||e.liveMaxLatencyDuration<=e.liveSyncDuration))throw new Error('Illegal hls.js config: "liveMaxLatencyDuration" must be greater than "liveSyncDuration"');const i=Mh(t),s=["manifest","level","frag"],r=["TimeOut","MaxRetry","RetryDelay","MaxRetryTimeout"];return s.forEach(t=>{const s=("level"===t?"playlist":t)+"LoadPolicy",n=void 0===e[s],a=[];r.forEach(r=>{const o=`${t}Loading${r}`,l=e[o];if(void 0!==l&&n){a.push(o);const t=i[s].default;switch(e[s]={default:t},r){case"TimeOut":t.maxLoadTimeMs=l,t.maxTimeToFirstByteMs=l;break;case"MaxRetry":t.errorRetry.maxNumRetry=l,t.timeoutRetry.maxNumRetry=l;break;case"RetryDelay":t.errorRetry.retryDelayMs=l,t.timeoutRetry.retryDelayMs=l;break;case"MaxRetryTimeout":t.errorRetry.maxRetryDelayMs=l,t.timeoutRetry.maxRetryDelayMs=l;break}}}),a.length&&Xt.warn(`hls.js config: "${a.join('", "')}" setting(s) are deprecated, use "${s}": ${JSON.stringify(e[s])}`)}),xt(xt({},i),e)}function Mh(t){return t&&"object"===typeof t?Array.isArray(t)?t.map(Mh):Object.keys(t).reduce((e,i)=>(e[i]=Mh(t[i]),e),{}):t}function Fh(t){const e=t.loader;if(e!==Ah&&e!==Th)Xt.log("[config]: Custom loader detected, cannot enable progressive streaming"),t.progressive=!1;else{const e=Sh();e&&(t.loader=Ah,t.progressive=!0,t.enableSoftwareAES=!0,Xt.log("[config]: Progressive streaming enabled, using FetchLoader"))}}let Nh;class Uh extends gr{constructor(t,e){super(t,"[level-controller]"),this._levels=[],this._firstLevel=-1,this._maxAutoLevel=-1,this._startLevel=void 0,this.currentLevel=null,this.currentLevelIndex=-1,this.manualLevelIndex=-1,this.steering=void 0,this.onParsedComplete=void 0,this.steering=e,this._registerListeners()}_registerListeners(){const{hls:t}=this;t.on(Gt.MANIFEST_LOADING,this.onManifestLoading,this),t.on(Gt.MANIFEST_LOADED,this.onManifestLoaded,this),t.on(Gt.LEVEL_LOADED,this.onLevelLoaded,this),t.on(Gt.LEVELS_UPDATED,this.onLevelsUpdated,this),t.on(Gt.FRAG_BUFFERED,this.onFragBuffered,this),t.on(Gt.ERROR,this.onError,this)}_unregisterListeners(){const{hls:t}=this;t.off(Gt.MANIFEST_LOADING,this.onManifestLoading,this),t.off(Gt.MANIFEST_LOADED,this.onManifestLoaded,this),t.off(Gt.LEVEL_LOADED,this.onLevelLoaded,this),t.off(Gt.LEVELS_UPDATED,this.onLevelsUpdated,this),t.off(Gt.FRAG_BUFFERED,this.onFragBuffered,this),t.off(Gt.ERROR,this.onError,this)}destroy(){this._unregisterListeners(),this.steering=null,this.resetLevels(),super.destroy()}stopLoad(){const t=this._levels;t.forEach(t=>{t.loadError=0,t.fragmentError=0}),super.stopLoad()}resetLevels(){this._startLevel=void 0,this.manualLevelIndex=-1,this.currentLevelIndex=-1,this.currentLevel=null,this._levels=[],this._maxAutoLevel=-1}onManifestLoading(t,e){this.resetLevels()}onManifestLoaded(t,e){const i=this.hls.config.preferManagedMediaSource,s=[],r={},n={};let a=!1,o=!1,l=!1;e.levels.forEach(t=>{var e,h;const c=t.attrs;let{audioCodec:d,videoCodec:u}=t;-1!==(null==(e=d)?void 0:e.indexOf("mp4a.40.34"))&&(Nh||(Nh=/chrome|firefox/i.test(navigator.userAgent)),Nh&&(t.audioCodec=d=void 0)),d&&(t.audioCodec=d=qi(d,i)),0===(null==(h=u)?void 0:h.indexOf("avc1"))&&(u=t.videoCodec=Wi(u));const{width:f,height:g,unknownCodecs:m}=t;if(a||(a=!(!f||!g)),o||(o=!!u),l||(l=!!d),null!=m&&m.length||d&&!Ui(d,"audio",i)||u&&!Ui(u,"video",i))return;const{CODECS:p,"FRAME-RATE":v,"HDCP-LEVEL":y,"PATHWAY-ID":E,RESOLUTION:T,"VIDEO-RANGE":S}=c,b=(E||".")+"-",A=`${b}${t.bitrate}-${T}-${v}-${p}-${S}-${y}`;if(r[A])if(r[A].uri===t.url||t.attrs["PATHWAY-ID"])r[A].addGroupId("audio",c.AUDIO),r[A].addGroupId("text",c.SUBTITLES);else{const e=n[A]+=1;t.attrs["PATHWAY-ID"]=new Array(e+1).join(".");const i=new Ns(t);r[A]=i,s.push(i)}else{const e=new Ns(t);r[A]=e,n[A]=1,s.push(e)}}),this.filterAndSortMediaOptions(s,e,a,o,l)}filterAndSortMediaOptions(t,e,i,s,r){let n=[],a=[],o=t;if((i||s)&&r&&(o=o.filter(({videoCodec:t,videoRange:e,width:i,height:s})=>(!!t||!(!i||!s))&&xs(e))),0===o.length)return void Promise.resolve().then(()=>{if(this.hls){e.levels.length&&this.warn("One or more CODECS in variant not supported: "+JSON.stringify(e.levels[0].attrs));const t=new Error("no level with compatible codecs found in manifest");this.hls.trigger(Gt.ERROR,{type:jt.MEDIA_ERROR,details:Kt.MANIFEST_INCOMPATIBLE_CODECS_ERROR,fatal:!0,url:e.url,error:t,reason:t.message})}});if(e.audioTracks){const{preferManagedMediaSource:t}=this.hls.config;n=e.audioTracks.filter(e=>!e.audioCodec||Ui(e.audioCodec,"audio",t)),Bh(n)}e.subtitles&&(a=e.subtitles,Bh(a));const l=o.slice(0);o.sort((t,e)=>{if(t.attrs["HDCP-LEVEL"]!==e.attrs["HDCP-LEVEL"])return(t.attrs["HDCP-LEVEL"]||"")>(e.attrs["HDCP-LEVEL"]||"")?1:-1;if(i&&t.height!==e.height)return t.height-e.height;if(t.frameRate!==e.frameRate)return t.frameRate-e.frameRate;if(t.videoRange!==e.videoRange)return Os.indexOf(t.videoRange)-Os.indexOf(e.videoRange);if(t.videoCodec!==e.videoCodec){const i=Gi(t.videoCodec),s=Gi(e.videoCodec);if(i!==s)return s-i}if(t.uri===e.uri&&t.codecSet!==e.codecSet){const i=ji(t.codecSet),s=ji(e.codecSet);if(i!==s)return s-i}return t.averageBitrate!==e.averageBitrate?t.averageBitrate-e.averageBitrate:0});let h=l[0];if(this.steering&&(o=this.steering.filterParsedLevels(o),o.length!==l.length))for(let f=0;f<l.length;f++)if(l[f].pathwayId===o[0].pathwayId){h=l[f];break}this._levels=o;for(let f=0;f<o.length;f++)if(o[f]===h){var c;this._firstLevel=f;const t=h.bitrate,e=this.hls.bandwidthEstimate;if(this.log(`manifest loaded, ${o.length} level(s) found, first bitrate: ${t}`),void 0===(null==(c=this.hls.userConfig)?void 0:c.abrEwmaDefaultEstimate)){const i=Math.min(t,this.hls.config.abrEwmaDefaultEstimateMax);i>e&&e===Oh.abrEwmaDefaultEstimate&&(this.hls.bandwidthEstimate=i)}break}const d=r&&!s,u={levels:o,audioTracks:n,subtitleTracks:a,sessionData:e.sessionData,sessionKeys:e.sessionKeys,firstLevel:this._firstLevel,stats:e.stats,audio:r,video:s,altAudio:!d&&n.some(t=>!!t.url)};this.hls.trigger(Gt.MANIFEST_PARSED,u),(this.hls.config.autoStartLoad||this.hls.forceStartLoad)&&this.hls.startLoad(this.hls.config.startPosition)}get levels(){return 0===this._levels.length?null:this._levels}get level(){return this.currentLevelIndex}set level(t){const e=this._levels;if(0===e.length)return;if(t<0||t>=e.length){const i=new Error("invalid level idx"),s=t<0;if(this.hls.trigger(Gt.ERROR,{type:jt.OTHER_ERROR,details:Kt.LEVEL_SWITCH_ERROR,level:t,fatal:s,error:i,reason:i.message}),s)return;t=Math.min(t,e.length-1)}const i=this.currentLevelIndex,s=this.currentLevel,r=s?s.attrs["PATHWAY-ID"]:void 0,n=e[t],a=n.attrs["PATHWAY-ID"];if(this.currentLevelIndex=t,this.currentLevel=n,i===t&&n.details&&s&&r===a)return;this.log(`Switching to level ${t} (${n.height?n.height+"p ":""}${n.videoRange?n.videoRange+" ":""}${n.codecSet?n.codecSet+" ":""}@${n.bitrate})${a?" with Pathway "+a:""} from level ${i}${r?" with Pathway "+r:""}`);const o={level:t,attrs:n.attrs,details:n.details,bitrate:n.bitrate,averageBitrate:n.averageBitrate,maxBitrate:n.maxBitrate,realBitrate:n.realBitrate,width:n.width,height:n.height,codecSet:n.codecSet,audioCodec:n.audioCodec,videoCodec:n.videoCodec,audioGroups:n.audioGroups,subtitleGroups:n.subtitleGroups,loaded:n.loaded,loadError:n.loadError,fragmentError:n.fragmentError,name:n.name,id:n.id,uri:n.uri,url:n.url,urlId:0,audioGroupIds:n.audioGroupIds,textGroupIds:n.textGroupIds};this.hls.trigger(Gt.LEVEL_SWITCHING,o);const l=n.details;if(!l||l.live){const t=this.switchParams(n.uri,null==s?void 0:s.details,l);this.loadPlaylist(t)}}get manualLevel(){return this.manualLevelIndex}set manualLevel(t){this.manualLevelIndex=t,void 0===this._startLevel&&(this._startLevel=t),-1!==t&&(this.level=t)}get firstLevel(){return this._firstLevel}set firstLevel(t){this._firstLevel=t}get startLevel(){if(void 0===this._startLevel){const t=this.hls.config.startLevel;return void 0!==t?t:this.hls.firstAutoLevel}return this._startLevel}set startLevel(t){this._startLevel=t}onError(t,e){!e.fatal&&e.context&&e.context.type===hs.LEVEL&&e.context.level===this.level&&this.checkRetry(e)}onFragBuffered(t,{frag:e}){if(void 0!==e&&e.type===cs.MAIN){const t=e.elementaryStreams;if(!Object.keys(t).some(e=>!!t[e]))return;const i=this._levels[e.level];null!=i&&i.loadError&&(this.log(`Resetting level error count of ${i.loadError} on frag buffered`),i.loadError=0)}}onLevelLoaded(t,e){var i;const{level:s,details:r}=e,n=this._levels[s];var a;if(!n)return this.warn("Invalid level index "+s),void(null!=(a=e.deliveryDirectives)&&a.skip&&(r.deltaUpdateFailed=!0));s===this.currentLevelIndex?(0===n.fragmentError&&(n.loadError=0),this.playlistLoaded(s,e,n.details)):null!=(i=e.deliveryDirectives)&&i.skip&&(r.deltaUpdateFailed=!0)}loadPlaylist(t){super.loadPlaylist();const e=this.currentLevelIndex,i=this.currentLevel;if(i&&this.shouldLoadPlaylist(i)){let r=i.uri;if(t)try{r=t.addDirectives(r)}catch(s){this.warn("Could not construct new URL with HLS Delivery Directives: "+s)}const n=i.attrs["PATHWAY-ID"];this.log(`Loading level index ${e}${void 0!==(null==t?void 0:t.msn)?" at sn "+t.msn+" part "+t.part:""} with${n?" Pathway "+n:""} ${r}`),this.clearTimer(),this.hls.trigger(Gt.LEVEL_LOADING,{url:r,level:e,pathwayId:i.attrs["PATHWAY-ID"],id:0,deliveryDirectives:t||null})}}get nextLoadLevel(){return-1!==this.manualLevelIndex?this.manualLevelIndex:this.hls.nextAutoLevel}set nextLoadLevel(t){this.level=t,-1===this.manualLevelIndex&&(this.hls.nextAutoLevel=t)}removeLevel(t){var e;const i=this._levels.filter((e,i)=>i!==t||(this.steering&&this.steering.removeLevel(e),e===this.currentLevel&&(this.currentLevel=null,this.currentLevelIndex=-1,e.details&&e.details.fragments.forEach(t=>t.level=-1)),!1));Qs(i),this._levels=i,this.currentLevelIndex>-1&&null!=(e=this.currentLevel)&&e.details&&(this.currentLevelIndex=this.currentLevel.details.fragments[0].level),this.hls.trigger(Gt.LEVELS_UPDATED,{levels:i})}onLevelsUpdated(t,{levels:e}){this._levels=e}checkMaxAutoUpdated(){const{autoLevelCapping:t,maxAutoLevel:e,maxHdcpLevel:i}=this.hls;this._maxAutoLevel!==e&&(this._maxAutoLevel=e,this.hls.trigger(Gt.MAX_AUTO_LEVEL_UPDATED,{autoLevelCapping:t,levels:this.levels,maxAutoLevel:e,minAutoLevel:this.hls.minAutoLevel,maxHdcpLevel:i}))}}function Bh(t){const e={};t.forEach(t=>{const i=t.groupId||"";t.id=e[i]=e[i]||0,e[i]++})}class $h{constructor(t){this.config=void 0,this.keyUriToKeyInfo={},this.emeController=null,this.config=t}abort(t){for(const i in this.keyUriToKeyInfo){const s=this.keyUriToKeyInfo[i].loader;if(s){var e;if(t&&t!==(null==(e=s.context)?void 0:e.frag.type))return;s.abort()}}}detach(){for(const t in this.keyUriToKeyInfo){const e=this.keyUriToKeyInfo[t];(e.mediaKeySessionContext||e.decryptdata.isCommonEncryption)&&delete this.keyUriToKeyInfo[t]}}destroy(){this.detach();for(const t in this.keyUriToKeyInfo){const e=this.keyUriToKeyInfo[t].loader;e&&e.destroy()}this.keyUriToKeyInfo={}}createKeyLoadError(t,e=Kt.KEY_LOAD_ERROR,i,s,r){return new sn({type:jt.NETWORK_ERROR,details:e,fatal:!1,frag:t,response:r,error:i,networkDetails:s})}loadClear(t,e){if(this.emeController&&this.config.emeEnabled){const{sn:i,cc:s}=t;for(let t=0;t<e.length;t++){const r=e[t];if(s<=r.cc&&("initSegment"===i||"initSegment"===r.sn||i<r.sn)){this.emeController.selectKeySystemFormat(r).then(t=>{r.setKeyFormat(t)});break}}}}load(t){return!t.decryptdata&&t.encrypted&&this.emeController?this.emeController.selectKeySystemFormat(t).then(e=>this.loadInternal(t,e)):this.loadInternal(t)}loadInternal(t,e){var i,s;e&&t.setKeyFormat(e);const r=t.decryptdata;if(!r){const i=new Error(e?"Expected frag.decryptdata to be defined after setting format "+e:"Missing decryption data on fragment in onKeyLoading");return Promise.reject(this.createKeyLoadError(t,Kt.KEY_LOAD_ERROR,i))}const n=r.uri;if(!n)return Promise.reject(this.createKeyLoadError(t,Kt.KEY_LOAD_ERROR,new Error(`Invalid key URI: "${n}"`)));let a=this.keyUriToKeyInfo[n];if(null!=(i=a)&&i.decryptdata.key)return r.key=a.decryptdata.key,Promise.resolve({frag:t,keyInfo:a});var o;if(null!=(s=a)&&s.keyLoadPromise)switch(null==(o=a.mediaKeySessionContext)?void 0:o.keyStatus){case void 0:case"status-pending":case"usable":case"usable-in-future":return a.keyLoadPromise.then(e=>(r.key=e.keyInfo.decryptdata.key,{frag:t,keyInfo:a}))}switch(a=this.keyUriToKeyInfo[n]={decryptdata:r,keyLoadPromise:null,loader:null,mediaKeySessionContext:null},r.method){case"ISO-23001-7":case"SAMPLE-AES":case"SAMPLE-AES-CENC":case"SAMPLE-AES-CTR":return"identity"===r.keyFormat?this.loadKeyHTTP(a,t):this.loadKeyEME(a,t);case"AES-128":return this.loadKeyHTTP(a,t);default:return Promise.reject(this.createKeyLoadError(t,Kt.KEY_LOAD_ERROR,new Error(`Key supplied with unsupported METHOD: "${r.method}"`)))}}loadKeyEME(t,e){const i={frag:e,keyInfo:t};if(this.emeController&&this.config.emeEnabled){const e=this.emeController.loadKey(i);if(e)return(t.keyLoadPromise=e.then(e=>(t.mediaKeySessionContext=e,i))).catch(e=>{throw t.keyLoadPromise=null,e})}return Promise.resolve(i)}loadKeyHTTP(t,e){const i=this.config,s=i.loader,r=new s(i);return e.keyLoader=t.loader=r,t.keyLoadPromise=new Promise((s,n)=>{const a={keyInfo:t,frag:e,responseType:"arraybuffer",url:t.decryptdata.uri},o=i.keyLoadPolicy.default,l={loadPolicy:o,timeout:o.maxLoadTimeMs,maxRetry:0,retryDelay:0,maxRetryDelay:0},h={onSuccess:(t,e,i,r)=>{const{frag:a,keyInfo:o,url:l}=i;if(!a.decryptdata||o!==this.keyUriToKeyInfo[l])return n(this.createKeyLoadError(a,Kt.KEY_LOAD_ERROR,new Error("after key load, decryptdata unset or changed"),r));o.decryptdata.key=a.decryptdata.key=new Uint8Array(t.data),a.keyLoader=null,o.loader=null,s({frag:a,keyInfo:o})},onError:(t,i,s,r)=>{this.resetLoader(i),n(this.createKeyLoadError(e,Kt.KEY_LOAD_ERROR,new Error(`HTTP Error ${t.code} loading key ${t.text}`),s,xt({url:a.url,data:void 0},t)))},onTimeout:(t,i,s)=>{this.resetLoader(i),n(this.createKeyLoadError(e,Kt.KEY_LOAD_TIMEOUT,new Error("key loading timed out"),s))},onAbort:(t,i,s)=>{this.resetLoader(i),n(this.createKeyLoadError(e,Kt.INTERNAL_ABORTED,new Error("key loading aborted"),s))}};r.load(a,l,h)})}resetLoader(t){const{frag:e,keyInfo:i,url:s}=t,r=i.loader;e.keyLoader===r&&(e.keyLoader=null,i.loader=null),delete this.keyUriToKeyInfo[s],r&&r.destroy()}}function Gh(){return self.SourceBuffer||self.WebKitSourceBuffer}function jh(){const t=Pi();if(!t)return!1;const e=Gh();return!e||e.prototype&&"function"===typeof e.prototype.appendBuffer&&"function"===typeof e.prototype.remove}function Kh(){if(!jh())return!1;const t=Pi();return"function"===typeof(null==t?void 0:t.isTypeSupported)&&(["avc1.42E01E,mp4a.40.2","av01.0.01M.08","vp09.00.50.08"].some(e=>t.isTypeSupported($i(e,"video")))||["mp4a.40.2","fLaC"].some(e=>t.isTypeSupported($i(e,"audio"))))}function Vh(){var t;const e=Gh();return"function"===typeof(null==e||null==(t=e.prototype)?void 0:t.changeType)}const Hh=250,qh=2,Yh=.1,Wh=.05;class zh{constructor(t,e,i,s){this.config=void 0,this.media=null,this.fragmentTracker=void 0,this.hls=void 0,this.nudgeRetry=0,this.stallReported=!1,this.stalled=null,this.moved=!1,this.seeking=!1,this.config=t,this.media=e,this.fragmentTracker=i,this.hls=s}destroy(){this.media=null,this.hls=this.fragmentTracker=null}poll(t,e){const{config:i,media:s,stalled:r}=this;if(null===s)return;const{currentTime:n,seeking:a}=s,o=this.seeking&&!a,l=!this.seeking&&a;if(this.seeking=a,n!==t){if(this.moved=!0,a||(this.nudgeRetry=0),null!==r){if(this.stallReported){const t=self.performance.now()-r;Xt.warn(`playback not stuck anymore @${n}, after ${Math.round(t)}ms`),this.stallReported=!1}this.stalled=null}return}if(l||o)return void(this.stalled=null);if(s.paused&&!a||s.ended||0===s.playbackRate||!Gr.getBuffered(s).length)return void(this.nudgeRetry=0);const h=Gr.bufferInfo(s,n,0),c=h.nextStart||0;if(a){const t=h.len>qh,i=!c||e&&e.start<=n||c-n>qh&&!this.fragmentTracker.getPartialFragment(n);if(t||i)return;this.moved=!1}if(!this.moved&&null!==this.stalled){var d;const t=h.len>0;if(!t&&!c)return;const e=Math.max(c,h.start||0)-n,i=this.hls.levels?this.hls.levels[this.hls.currentLevel]:null,r=null==i||null==(d=i.details)?void 0:d.live,a=r?2*i.details.targetduration:qh,o=this.fragmentTracker.getPartialFragment(n);if(e>0&&(e<=a||o))return void(s.paused||this._trySkipBufferHole(o))}const u=self.performance.now();if(null===r)return void(this.stalled=u);const f=u-r;if(!a&&f>=Hh&&(this._reportStall(h),!this.media))return;const g=Gr.bufferInfo(s,n,i.maxBufferHole);this._tryFixBufferStall(g,f)}_tryFixBufferStall(t,e){const{config:i,fragmentTracker:s,media:r}=this;if(null===r)return;const n=r.currentTime,a=s.getPartialFragment(n);if(a){const t=this._trySkipBufferHole(a);if(t||!this.media)return}(t.len>i.maxBufferHole||t.nextStart&&t.nextStart-n<i.maxBufferHole)&&e>1e3*i.highBufferWatchdogPeriod&&(Xt.warn("Trying to nudge playhead over buffer-hole"),this.stalled=null,this._tryNudgeBuffer())}_reportStall(t){const{hls:e,media:i,stallReported:s}=this;if(!s&&i){this.stallReported=!0;const s=new Error(`Playback stalling at @${i.currentTime} due to low buffer (${JSON.stringify(t)})`);Xt.warn(s.message),e.trigger(Gt.ERROR,{type:jt.MEDIA_ERROR,details:Kt.BUFFER_STALLED_ERROR,fatal:!1,error:s,buffer:t.len})}}_trySkipBufferHole(t){const{config:e,hls:i,media:s}=this;if(null===s)return 0;const r=s.currentTime,n=Gr.bufferInfo(s,r,0),a=r<n.start?n.start:n.nextStart;if(a){const o=n.len<=e.maxBufferHole,l=n.len>0&&n.len<1&&s.readyState<3,h=a-r;if(h>0&&(o||l)){if(h>e.maxBufferHole){const{fragmentTracker:e}=this;let i=!1;if(0===r){const t=e.getAppendedFrag(0,cs.MAIN);t&&a<t.end&&(i=!0)}if(!i){const i=t||e.getAppendedFrag(r,cs.MAIN);if(i){let t=!1,s=i.end;while(s<a){const i=e.getPartialFragment(s);if(!i){t=!0;break}s+=i.duration}if(t)return 0}}}const n=Math.max(a+Wh,r+Yh);if(Xt.warn(`skipping hole, adjusting currentTime from ${r} to ${n}`),this.moved=!0,this.stalled=null,s.currentTime=n,t&&!t.gap){const e=new Error(`fragment loaded with buffer holes, seeking from ${r} to ${n}`);i.trigger(Gt.ERROR,{type:jt.MEDIA_ERROR,details:Kt.BUFFER_SEEK_OVER_HOLE,fatal:!1,error:e,reason:e.message,frag:t})}return n}}return 0}_tryNudgeBuffer(){const{config:t,hls:e,media:i,nudgeRetry:s}=this;if(null===i)return;const r=i.currentTime;if(this.nudgeRetry++,s<t.nudgeMaxRetry){const n=r+(s+1)*t.nudgeOffset,a=new Error(`Nudging 'currentTime' from ${r} to ${n}`);Xt.warn(a.message),i.currentTime=n,e.trigger(Gt.ERROR,{type:jt.MEDIA_ERROR,details:Kt.BUFFER_NUDGE_ON_STALL,error:a,fatal:!1})}else{const i=new Error(`Playhead still not moving while enough data buffered @${r} after ${t.nudgeMaxRetry} nudges`);Xt.error(i.message),e.trigger(Gt.ERROR,{type:jt.MEDIA_ERROR,details:Kt.BUFFER_STALLED_ERROR,error:i,fatal:!0})}}}const Xh=100;class Qh extends un{constructor(t,e,i){super(t,e,i,"[stream-controller]",cs.MAIN),this.audioCodecSwap=!1,this.gapController=null,this.level=-1,this._forceStartLoad=!1,this.altAudio=!1,this.audioOnly=!1,this.fragPlaying=null,this.onvplaying=null,this.onvseeked=null,this.fragLastKbps=0,this.couldBacktrack=!1,this.backtrackFragment=null,this.audioCodecSwitch=!1,this.videoBuffer=null,this._registerListeners()}_registerListeners(){const{hls:t}=this;t.on(Gt.MEDIA_ATTACHED,this.onMediaAttached,this),t.on(Gt.MEDIA_DETACHING,this.onMediaDetaching,this),t.on(Gt.MANIFEST_LOADING,this.onManifestLoading,this),t.on(Gt.MANIFEST_PARSED,this.onManifestParsed,this),t.on(Gt.LEVEL_LOADING,this.onLevelLoading,this),t.on(Gt.LEVEL_LOADED,this.onLevelLoaded,this),t.on(Gt.FRAG_LOAD_EMERGENCY_ABORTED,this.onFragLoadEmergencyAborted,this),t.on(Gt.ERROR,this.onError,this),t.on(Gt.AUDIO_TRACK_SWITCHING,this.onAudioTrackSwitching,this),t.on(Gt.AUDIO_TRACK_SWITCHED,this.onAudioTrackSwitched,this),t.on(Gt.BUFFER_CREATED,this.onBufferCreated,this),t.on(Gt.BUFFER_FLUSHED,this.onBufferFlushed,this),t.on(Gt.LEVELS_UPDATED,this.onLevelsUpdated,this),t.on(Gt.FRAG_BUFFERED,this.onFragBuffered,this)}_unregisterListeners(){const{hls:t}=this;t.off(Gt.MEDIA_ATTACHED,this.onMediaAttached,this),t.off(Gt.MEDIA_DETACHING,this.onMediaDetaching,this),t.off(Gt.MANIFEST_LOADING,this.onManifestLoading,this),t.off(Gt.MANIFEST_PARSED,this.onManifestParsed,this),t.off(Gt.LEVEL_LOADED,this.onLevelLoaded,this),t.off(Gt.FRAG_LOAD_EMERGENCY_ABORTED,this.onFragLoadEmergencyAborted,this),t.off(Gt.ERROR,this.onError,this),t.off(Gt.AUDIO_TRACK_SWITCHING,this.onAudioTrackSwitching,this),t.off(Gt.AUDIO_TRACK_SWITCHED,this.onAudioTrackSwitched,this),t.off(Gt.BUFFER_CREATED,this.onBufferCreated,this),t.off(Gt.BUFFER_FLUSHED,this.onBufferFlushed,this),t.off(Gt.LEVELS_UPDATED,this.onLevelsUpdated,this),t.off(Gt.FRAG_BUFFERED,this.onFragBuffered,this)}onHandlerDestroying(){this._unregisterListeners(),super.onHandlerDestroying()}startLoad(t){if(this.levels){const{lastCurrentTime:e,hls:i}=this;if(this.stopLoad(),this.setInterval(Xh),this.level=-1,!this.startFragRequested){let t=i.startLevel;-1===t&&(i.config.testBandwidth&&this.levels.length>1?(t=0,this.bitrateTest=!0):t=i.firstAutoLevel),i.nextLoadLevel=t,this.level=i.loadLevel,this.loadedmetadata=!1}e>0&&-1===t&&(this.log("Override startPosition with lastCurrentTime @"+e.toFixed(3)),t=e),this.state=dn.IDLE,this.nextLoadPosition=this.startPosition=this.lastCurrentTime=t,this.tick()}else this._forceStartLoad=!0,this.state=dn.STOPPED}stopLoad(){this._forceStartLoad=!1,super.stopLoad()}doTick(){switch(this.state){case dn.WAITING_LEVEL:{const{levels:t,level:e}=this,i=null==t?void 0:t[e],s=null==i?void 0:i.details;if(s&&(!s.live||this.levelLastLoaded===i)){if(this.waitForCdnTuneIn(s))break;this.state=dn.IDLE;break}if(this.hls.nextLoadLevel!==this.level){this.state=dn.IDLE;break}break}case dn.FRAG_LOADING_WAITING_RETRY:{var t;const e=self.performance.now(),i=this.retryDate;if(!i||e>=i||null!=(t=this.media)&&t.seeking){const{levels:t,level:e}=this,i=null==t?void 0:t[e];this.resetStartWhenNotLoaded(i||null),this.state=dn.IDLE}}break}this.state===dn.IDLE&&this.doTickIdle(),this.onTickEnd()}onTickEnd(){super.onTickEnd(),this.checkBuffer(),this.checkFragmentChanged()}doTickIdle(){const{hls:t,levelLastLoaded:e,levels:i,media:s}=this;if(null===e||!s&&(this.startFragRequested||!t.config.startFragPrefetch))return;if(this.altAudio&&this.audioOnly)return;const r=t.nextLoadLevel;if(null==i||!i[r])return;const n=i[r],a=this.getMainFwdBufferInfo();if(null===a)return;const o=this.getLevelDetails();if(o&&this._streamEnded(a,o)){const t={};return this.altAudio&&(t.type="video"),this.hls.trigger(Gt.BUFFER_EOS,t),void(this.state=dn.ENDED)}t.loadLevel!==r&&-1===t.manualLevel&&this.log(`Adapting to level ${r} from level ${this.level}`),this.level=t.nextLoadLevel=r;const l=n.details;if(!l||this.state===dn.WAITING_LEVEL||l.live&&this.levelLastLoaded!==n)return this.level=r,void(this.state=dn.WAITING_LEVEL);const h=a.len,c=this.getMaxBufferLength(n.maxBitrate);if(h>=c)return;this.backtrackFragment&&this.backtrackFragment.start>a.end&&(this.backtrackFragment=null);const d=this.backtrackFragment?this.backtrackFragment.start:a.end;let u=this.getNextFragment(d,l);if(this.couldBacktrack&&!this.fragPrevious&&u&&"initSegment"!==u.sn&&this.fragmentTracker.getState(u)!==Fr.OK){var f;const t=(null!=(f=this.backtrackFragment)?f:u).sn,e=t-l.startSN,i=l.fragments[e-1];i&&u.cc===i.cc&&(u=i,this.fragmentTracker.removeFragment(i))}else this.backtrackFragment&&a.len&&(this.backtrackFragment=null);if(u&&this.isLoopLoading(u,d)){const t=u.gap;if(!t){const t=this.audioOnly&&!this.altAudio?re.AUDIO:re.VIDEO,e=(t===re.VIDEO?this.videoBuffer:this.mediaBuffer)||this.media;e&&this.afterBufferFlushed(e,t,cs.MAIN)}u=this.getNextFragmentLoopLoading(u,l,a,cs.MAIN,c)}u&&(!u.initSegment||u.initSegment.data||this.bitrateTest||(u=u.initSegment),this.loadFragment(u,n,d))}loadFragment(t,e,i){const s=this.fragmentTracker.getState(t);this.fragCurrent=t,s===Fr.NOT_LOADED||s===Fr.PARTIAL?"initSegment"===t.sn?this._loadInitSegment(t,e):this.bitrateTest?(this.log(`Fragment ${t.sn} of level ${t.level} is being downloaded to test bitrate and will not be buffered`),this._loadBitrateTestFrag(t,e)):(this.startFragRequested=!0,super.loadFragment(t,e,i)):this.clearTrackerIfNeeded(t)}getBufferedFrag(t){return this.fragmentTracker.getBufferedFrag(t,cs.MAIN)}followingBufferedFrag(t){return t?this.getBufferedFrag(t.end+.5):null}immediateLevelSwitch(){this.abortCurrentFrag(),this.flushMainBuffer(0,Number.POSITIVE_INFINITY)}nextLevelSwitch(){const{levels:t,media:e}=this;if(null!=e&&e.readyState){let i;const s=this.getAppendedFrag(e.currentTime);s&&s.start>1&&this.flushMainBuffer(0,s.start-1);const r=this.getLevelDetails();if(null!=r&&r.live){const t=this.getMainFwdBufferInfo();if(!t||t.len<2*r.targetduration)return}if(!e.paused&&t){const e=this.hls.nextLoadLevel,s=t[e],r=this.fragLastKbps;i=r&&this.fragCurrent?this.fragCurrent.duration*s.maxBitrate/(1e3*r)+1:0}else i=0;const n=this.getBufferedFrag(e.currentTime+i);if(n){const t=this.followingBufferedFrag(n);if(t){this.abortCurrentFrag();const e=t.maxStartPTS?t.maxStartPTS:t.start,i=t.duration,s=Math.max(n.end,e+Math.min(Math.max(i-this.config.maxFragLookUpTolerance,i*(this.couldBacktrack?.5:.125)),i*(this.couldBacktrack?.75:.25)));this.flushMainBuffer(s,Number.POSITIVE_INFINITY)}}}}abortCurrentFrag(){const t=this.fragCurrent;switch(this.fragCurrent=null,this.backtrackFragment=null,t&&(t.abortRequests(),this.fragmentTracker.removeFragment(t)),this.state){case dn.KEY_LOADING:case dn.FRAG_LOADING:case dn.FRAG_LOADING_WAITING_RETRY:case dn.PARSING:case dn.PARSED:this.state=dn.IDLE;break}this.nextLoadPosition=this.getLoadPosition()}flushMainBuffer(t,e){super.flushMainBuffer(t,e,this.altAudio?"video":null)}onMediaAttached(t,e){super.onMediaAttached(t,e);const i=e.media;this.onvplaying=this.onMediaPlaying.bind(this),this.onvseeked=this.onMediaSeeked.bind(this),i.addEventListener("playing",this.onvplaying),i.addEventListener("seeked",this.onvseeked),this.gapController=new zh(this.config,i,this.fragmentTracker,this.hls)}onMediaDetaching(){const{media:t}=this;t&&this.onvplaying&&this.onvseeked&&(t.removeEventListener("playing",this.onvplaying),t.removeEventListener("seeked",this.onvseeked),this.onvplaying=this.onvseeked=null,this.videoBuffer=null),this.fragPlaying=null,this.gapController&&(this.gapController.destroy(),this.gapController=null),super.onMediaDetaching()}onMediaPlaying(){this.tick()}onMediaSeeked(){const t=this.media,e=t?t.currentTime:null;Ut(e)&&this.log("Media seeked to "+e.toFixed(3));const i=this.getMainFwdBufferInfo();null!==i&&0!==i.len?this.tick():this.warn(`Main forward buffer length on "seeked" event ${i?i.len:"empty"})`)}onManifestLoading(){this.log("Trigger BUFFER_RESET"),this.hls.trigger(Gt.BUFFER_RESET,void 0),this.fragmentTracker.removeAllFragments(),this.couldBacktrack=!1,this.startPosition=this.lastCurrentTime=this.fragLastKbps=0,this.levels=this.fragPlaying=this.backtrackFragment=this.levelLastLoaded=null,this.altAudio=this.audioOnly=this.startFragRequested=!1}onManifestParsed(t,e){let i=!1,s=!1;e.levels.forEach(t=>{const e=t.audioCodec;e&&(i=i||-1!==e.indexOf("mp4a.40.2"),s=s||-1!==e.indexOf("mp4a.40.5"))}),this.audioCodecSwitch=i&&s&&!Vh(),this.audioCodecSwitch&&this.log("Both AAC/HE-AAC audio found in levels; declaring level codec as HE-AAC"),this.levels=e.levels,this.startFragRequested=!1}onLevelLoading(t,e){const{levels:i}=this;if(!i||this.state!==dn.IDLE)return;const s=i[e.level];(!s.details||s.details.live&&this.levelLastLoaded!==s||this.waitForCdnTuneIn(s.details))&&(this.state=dn.WAITING_LEVEL)}onLevelLoaded(t,e){var i;const{levels:s}=this,r=e.level,n=e.details,a=n.totalduration;if(!s)return void this.warn("Levels were reset while loading level "+r);this.log(`Level ${r} loaded [${n.startSN},${n.endSN}]${n.lastPartSn?`[part-${n.lastPartSn}-${n.lastPartIndex}]`:""}, cc [${n.startCC}, ${n.endCC}] duration:${a}`);const o=s[r],l=this.fragCurrent;!l||this.state!==dn.FRAG_LOADING&&this.state!==dn.FRAG_LOADING_WAITING_RETRY||l.level!==e.level&&l.loader&&this.abortCurrentFrag();let h=0;if(n.live||null!=(i=o.details)&&i.live){var c;if(this.checkLiveUpdate(n),n.deltaUpdateFailed)return;h=this.alignPlaylists(n,o.details,null==(c=this.levelLastLoaded)?void 0:c.details)}if(o.details=n,this.levelLastLoaded=o,this.hls.trigger(Gt.LEVEL_UPDATED,{details:n,level:r}),this.state===dn.WAITING_LEVEL){if(this.waitForCdnTuneIn(n))return;this.state=dn.IDLE}this.startFragRequested?n.live&&this.synchronizeToLiveEdge(n):this.setStartPosition(n,h),this.tick()}_handleFragmentLoadProgress(t){var e;const{frag:i,part:s,payload:r}=t,{levels:n}=this;if(!n)return void this.warn(`Levels were reset while fragment load was in progress. Fragment ${i.sn} of level ${i.level} will not be buffered`);const a=n[i.level],o=a.details;if(!o)return this.warn(`Dropping fragment ${i.sn} of level ${i.level} after level details were reset`),void this.fragmentTracker.removeFragment(i);const l=a.videoCodec,h=o.PTSKnown||!o.live,c=null==(e=i.initSegment)?void 0:e.data,d=this._getAudioCodec(a),u=this.transmuxer=this.transmuxer||new Ka(this.hls,cs.MAIN,this._handleTransmuxComplete.bind(this),this._handleTransmuxerFlush.bind(this)),f=s?s.index:-1,g=-1!==f,m=new jr(i.level,i.sn,i.stats.chunkCount,r.byteLength,f,g),p=this.initPTS[i.cc];u.push(r,c,d,l,i,s,o.totalduration,h,m,p)}onAudioTrackSwitching(t,e){const i=this.altAudio,s=!!e.url;if(!s){if(this.mediaBuffer!==this.media){this.log("Switching on main audio, use media.buffered to schedule main fragment loading"),this.mediaBuffer=this.media;const t=this.fragCurrent;t&&(this.log("Switching to main audio track, cancel main fragment load"),t.abortRequests(),this.fragmentTracker.removeFragment(t)),this.resetTransmuxer(),this.resetLoadingState()}else this.audioOnly&&this.resetTransmuxer();const t=this.hls;i&&(t.trigger(Gt.BUFFER_FLUSHING,{startOffset:0,endOffset:Number.POSITIVE_INFINITY,type:null}),this.fragmentTracker.removeAllFragments()),t.trigger(Gt.AUDIO_TRACK_SWITCHED,e)}}onAudioTrackSwitched(t,e){const i=e.id,s=!!this.hls.audioTracks[i].url;if(s){const t=this.videoBuffer;t&&this.mediaBuffer!==t&&(this.log("Switching on alternate audio, use video.buffered to schedule main fragment loading"),this.mediaBuffer=t)}this.altAudio=s,this.tick()}onBufferCreated(t,e){const i=e.tracks;let s,r,n=!1;for(const a in i){const t=i[a];if("main"===t.id){if(r=a,s=t,"video"===a){const t=i[a];t&&(this.videoBuffer=t.buffer)}}else n=!0}n&&s?(this.log(`Alternate track found, use ${r}.buffered to schedule main fragment loading`),this.mediaBuffer=s.buffer):this.mediaBuffer=this.media}onFragBuffered(t,e){const{frag:i,part:s}=e;if(i&&i.type!==cs.MAIN)return;if(this.fragContextChanged(i))return this.warn(`Fragment ${i.sn}${s?" p: "+s.index:""} of level ${i.level} finished buffering, but was aborted. state: ${this.state}`),void(this.state===dn.PARSED&&(this.state=dn.IDLE));const r=s?s.stats:i.stats;this.fragLastKbps=Math.round(8*r.total/(r.buffering.end-r.loading.first)),"initSegment"!==i.sn&&(this.fragPrevious=i),this.fragBufferedComplete(i,s)}onError(t,e){var i;if(e.fatal)this.state=dn.ERROR;else switch(e.details){case Kt.FRAG_GAP:case Kt.FRAG_PARSING_ERROR:case Kt.FRAG_DECRYPT_ERROR:case Kt.FRAG_LOAD_ERROR:case Kt.FRAG_LOAD_TIMEOUT:case Kt.KEY_LOAD_ERROR:case Kt.KEY_LOAD_TIMEOUT:this.onFragmentOrKeyLoadError(cs.MAIN,e);break;case Kt.LEVEL_LOAD_ERROR:case Kt.LEVEL_LOAD_TIMEOUT:case Kt.LEVEL_PARSING_ERROR:e.levelRetry||this.state!==dn.WAITING_LEVEL||(null==(i=e.context)?void 0:i.type)!==hs.LEVEL||(this.state=dn.IDLE);break;case Kt.BUFFER_APPEND_ERROR:case Kt.BUFFER_FULL_ERROR:if(!e.parent||"main"!==e.parent)return;if(e.details===Kt.BUFFER_APPEND_ERROR)return void this.resetLoadingState();this.reduceLengthAndFlushBuffer(e)&&this.flushMainBuffer(0,Number.POSITIVE_INFINITY);break;case Kt.INTERNAL_EXCEPTION:this.recoverWorkerError(e);break}}checkBuffer(){const{media:t,gapController:e}=this;if(t&&e&&t.readyState){if(this.loadedmetadata||!Gr.getBuffered(t).length){const t=this.state!==dn.IDLE?this.fragCurrent:null;e.poll(this.lastCurrentTime,t)}this.lastCurrentTime=t.currentTime}}onFragLoadEmergencyAborted(){this.state=dn.IDLE,this.loadedmetadata||(this.startFragRequested=!1,this.nextLoadPosition=this.startPosition),this.tickImmediate()}onBufferFlushed(t,{type:e}){if(e!==re.AUDIO||this.audioOnly&&!this.altAudio){const t=(e===re.VIDEO?this.videoBuffer:this.mediaBuffer)||this.media;this.afterBufferFlushed(t,e,cs.MAIN),this.tick()}}onLevelsUpdated(t,e){this.level>-1&&this.fragCurrent&&(this.level=this.fragCurrent.level),this.levels=e.levels}swapAudioCodec(){this.audioCodecSwap=!this.audioCodecSwap}seekToStartPos(){const{media:t}=this;if(!t)return;const e=t.currentTime;let i=this.startPosition;if(i>=0&&e<i){if(t.seeking)return void this.log(`could not seek to ${i}, already seeking at ${e}`);const s=Gr.getBuffered(t),r=s.length?s.start(0):0,n=r-i;n>0&&(n<this.config.maxBufferHole||n<this.config.maxFragLookUpTolerance)&&(this.log(`adjusting start position by ${n} to match buffer start`),i+=n,this.startPosition=i),this.log(`seek to target start position ${i} from current time ${e}`),t.currentTime=i}}_getAudioCodec(t){let e=this.config.defaultAudioCodec||t.audioCodec;return this.audioCodecSwap&&e&&(this.log("Swapping audio codec"),e=-1!==e.indexOf("mp4a.40.5")?"mp4a.40.2":"mp4a.40.5"),e}_loadBitrateTestFrag(t,e){t.bitrateTest=!0,this._doFragLoad(t,e).then(i=>{const{hls:s}=this;if(!i||this.fragContextChanged(t))return;e.fragmentError=0,this.state=dn.IDLE,this.startFragRequested=!1,this.bitrateTest=!1;const r=t.stats;r.parsing.start=r.parsing.end=r.buffering.start=r.buffering.end=self.performance.now(),s.trigger(Gt.FRAG_LOADED,i),t.bitrateTest=!1})}_handleTransmuxComplete(t){var e;const i="main",{hls:s}=this,{remuxResult:r,chunkMeta:n}=t,a=this.getCurrentContext(n);if(!a)return void this.resetWhenMissingContext(n);const{frag:o,part:l,level:h}=a,{video:c,text:d,id3:u,initSegment:f}=r,{details:g}=h,m=this.altAudio?void 0:r.audio;if(this.fragContextChanged(o))this.fragmentTracker.removeFragment(o);else{if(this.state=dn.PARSING,f){if(null!=f&&f.tracks){const t=o.initSegment||o;this._bufferInitSegment(h,f.tracks,t,n),s.trigger(Gt.FRAG_PARSING_INIT_SEGMENT,{frag:t,id:i,tracks:f.tracks})}const t=f.initPTS,e=f.timescale;Ut(t)&&(this.initPTS[o.cc]={baseTime:t,timescale:e},s.trigger(Gt.INIT_PTS_FOUND,{frag:o,id:i,initPTS:t,timescale:e}))}if(c&&g&&"initSegment"!==o.sn){const t=g.fragments[o.sn-1-g.startSN],e=o.sn===g.startSN,i=!t||o.cc>t.cc;if(!1!==r.independent){const{startPTS:t,endPTS:s,startDTS:r,endDTS:a}=c;if(l)l.elementaryStreams[c.type]={startPTS:t,endPTS:s,startDTS:r,endDTS:a};else if(c.firstKeyFrame&&c.independent&&1===n.id&&!i&&(this.couldBacktrack=!0),c.dropped&&c.independent){const r=this.getMainFwdBufferInfo(),n=(r?r.end:this.getLoadPosition())+this.config.maxBufferHole,l=c.firstKeyFramePTS?c.firstKeyFramePTS:t;if(!e&&n<l-this.config.maxBufferHole&&!i)return void this.backtrack(o);i&&(o.gap=!0),o.setElementaryStreamInfo(c.type,o.start,s,o.start,a,!0)}else e&&t>qh&&(o.gap=!0);o.setElementaryStreamInfo(c.type,t,s,r,a),this.backtrackFragment&&(this.backtrackFragment=o),this.bufferFragmentData(c,o,l,n,e||i)}else{if(!e&&!i)return void this.backtrack(o);o.gap=!0}}if(m){const{startPTS:t,endPTS:e,startDTS:i,endDTS:s}=m;l&&(l.elementaryStreams[re.AUDIO]={startPTS:t,endPTS:e,startDTS:i,endDTS:s}),o.setElementaryStreamInfo(re.AUDIO,t,e,i,s),this.bufferFragmentData(m,o,l,n)}if(g&&null!=u&&null!=(e=u.samples)&&e.length){const t={id:i,frag:o,details:g,samples:u.samples};s.trigger(Gt.FRAG_PARSING_METADATA,t)}if(g&&d){const t={id:i,frag:o,details:g,samples:d.samples};s.trigger(Gt.FRAG_PARSING_USERDATA,t)}}}_bufferInitSegment(t,e,i,s){if(this.state!==dn.PARSING)return;this.audioOnly=!!e.audio&&!e.video,this.altAudio&&!this.audioOnly&&delete e.audio;const{audio:r,video:n,audiovideo:a}=e;if(r){let e=t.audioCodec;const i=navigator.userAgent.toLowerCase();if(this.audioCodecSwitch){e&&(e=-1!==e.indexOf("mp4a.40.5")?"mp4a.40.2":"mp4a.40.5");const t=r.metadata;t&&"channelCount"in t&&1!==(t.channelCount||1)&&-1===i.indexOf("firefox")&&(e="mp4a.40.5")}e&&-1!==e.indexOf("mp4a.40.5")&&-1!==i.indexOf("android")&&"audio/mpeg"!==r.container&&(e="mp4a.40.2",this.log("Android: force audio codec to "+e)),t.audioCodec&&t.audioCodec!==e&&this.log(`Swapping manifest audio codec "${t.audioCodec}" for "${e}"`),r.levelCodec=e,r.id="main",this.log(`Init audio buffer, container:${r.container}, codecs[selected/level/parsed]=[${e||""}/${t.audioCodec||""}/${r.codec}]`)}n&&(n.levelCodec=t.videoCodec,n.id="main",this.log(`Init video buffer, container:${n.container}, codecs[level/parsed]=[${t.videoCodec||""}/${n.codec}]`)),a&&this.log(`Init audiovideo buffer, container:${a.container}, codecs[level/parsed]=[${t.codecs}/${a.codec}]`),this.hls.trigger(Gt.BUFFER_CODECS,e),Object.keys(e).forEach(t=>{const r=e[t],n=r.initSegment;null!=n&&n.byteLength&&this.hls.trigger(Gt.BUFFER_APPENDING,{type:t,data:n,frag:i,part:null,chunkMeta:s,parent:i.type})}),this.tickImmediate()}getMainFwdBufferInfo(){return this.getFwdBufferInfo(this.mediaBuffer?this.mediaBuffer:this.media,cs.MAIN)}backtrack(t){this.couldBacktrack=!0,this.backtrackFragment=t,this.resetTransmuxer(),this.flushBufferGap(t),this.fragmentTracker.removeFragment(t),this.fragPrevious=null,this.nextLoadPosition=t.start,this.state=dn.IDLE}checkFragmentChanged(){const t=this.media;let e=null;if(t&&t.readyState>1&&!1===t.seeking){const i=t.currentTime;if(Gr.isBuffered(t,i)?e=this.getAppendedFrag(i):Gr.isBuffered(t,i+.1)&&(e=this.getAppendedFrag(i+.1)),e){this.backtrackFragment=null;const t=this.fragPlaying,i=e.level;t&&e.sn===t.sn&&t.level===i||(this.fragPlaying=e,this.hls.trigger(Gt.FRAG_CHANGED,{frag:e}),t&&t.level===i||this.hls.trigger(Gt.LEVEL_SWITCHED,{level:i}))}}}get nextLevel(){const t=this.nextBufferedFrag;return t?t.level:-1}get currentFrag(){const t=this.media;return t?this.fragPlaying||this.getAppendedFrag(t.currentTime):null}get currentProgramDateTime(){const t=this.media;if(t){const e=t.currentTime,i=this.currentFrag;if(i&&Ut(e)&&Ut(i.programDateTime)){const t=i.programDateTime+1e3*(e-i.start);return new Date(t)}}return null}get currentLevel(){const t=this.currentFrag;return t?t.level:-1}get nextBufferedFrag(){const t=this.currentFrag;return t?this.followingBufferedFrag(t):null}get forceStartLoad(){return this._forceStartLoad}}class Jh{static get version(){return"1.5.11"}static isMSESupported(){return jh()}static isSupported(){return Kh()}static getMediaSource(){return Pi()}static get Events(){return Gt}static get ErrorTypes(){return jt}static get ErrorDetails(){return Kt}static get DefaultConfig(){return Jh.defaultConfig?Jh.defaultConfig:Oh}static set DefaultConfig(t){Jh.defaultConfig=t}constructor(t={}){this.config=void 0,this.userConfig=void 0,this.coreComponents=void 0,this.networkControllers=void 0,this.started=!1,this._emitter=new ja,this._autoLevelCapping=-1,this._maxHdcpLevel=null,this.abrController=void 0,this.bufferController=void 0,this.capLevelController=void 0,this.latencyController=void 0,this.levelController=void 0,this.streamController=void 0,this.audioTrackController=void 0,this.subtitleTrackController=void 0,this.emeController=void 0,this.cmcdController=void 0,this._media=null,this.url=null,this.triggeringException=void 0,zt(t.debug||!1,"Hls instance");const e=this.config=Ph(Jh.DefaultConfig,t);this.userConfig=t,e.progressive&&Fh(e);const{abrController:i,bufferController:s,capLevelController:r,errorController:n,fpsController:a}=e,o=new n(this),l=this.abrController=new i(this),h=this.bufferController=new s(this),c=this.capLevelController=new r(this),d=new a(this),u=new fs(this),f=new ks(this),g=e.contentSteeringController,m=g?new g(this):null,p=this.levelController=new Uh(this,m),v=new Nr(this),y=new $h(this.config),E=this.streamController=new Qh(this,v,y);c.setStreamController(E),d.setStreamController(E);const T=[u,p,E];m&&T.splice(1,0,m),this.networkControllers=T;const S=[l,h,c,d,f,v];this.audioTrackController=this.createController(e.audioTrackController,T);const b=e.audioStreamController;b&&T.push(new b(this,v,y)),this.subtitleTrackController=this.createController(e.subtitleTrackController,T);const A=e.subtitleStreamController;A&&T.push(new A(this,v,y)),this.createController(e.timelineController,S),y.emeController=this.emeController=this.createController(e.emeController,S),this.cmcdController=this.createController(e.cmcdController,S),this.latencyController=this.createController(ws,S),this.coreComponents=S,T.push(o);const L=o.onErrorOut;"function"===typeof L&&this.on(Gt.ERROR,L,o)}createController(t,e){if(t){const i=new t(this);return e&&e.push(i),i}return null}on(t,e,i=this){this._emitter.on(t,e,i)}once(t,e,i=this){this._emitter.once(t,e,i)}removeAllListeners(t){this._emitter.removeAllListeners(t)}off(t,e,i=this,s){this._emitter.off(t,e,i,s)}listeners(t){return this._emitter.listeners(t)}emit(t,e,i){return this._emitter.emit(t,e,i)}trigger(t,e){if(this.config.debug)return this.emit(t,t,e);try{return this.emit(t,t,e)}catch(i){if(Xt.error("An internal error happened while handling event "+t+'. Error message: "'+i.message+'". Here is a stacktrace:',i),!this.triggeringException){this.triggeringException=!0;const e=t===Gt.ERROR;this.trigger(Gt.ERROR,{type:jt.OTHER_ERROR,details:Kt.INTERNAL_EXCEPTION,fatal:e,event:t,error:i}),this.triggeringException=!1}}return!1}listenerCount(t){return this._emitter.listenerCount(t)}destroy(){Xt.log("destroy"),this.trigger(Gt.DESTROYING,void 0),this.detachMedia(),this.removeAllListeners(),this._autoLevelCapping=-1,this.url=null,this.networkControllers.forEach(t=>t.destroy()),this.networkControllers.length=0,this.coreComponents.forEach(t=>t.destroy()),this.coreComponents.length=0;const t=this.config;t.xhrSetup=t.fetchSetup=void 0,this.userConfig=null}attachMedia(t){Xt.log("attachMedia"),this._media=t,this.trigger(Gt.MEDIA_ATTACHING,{media:t})}detachMedia(){Xt.log("detachMedia"),this.trigger(Gt.MEDIA_DETACHING,void 0),this._media=null}loadSource(t){this.stopLoad();const e=this.media,i=this.url,s=this.url=_t.buildAbsoluteURL(self.location.href,t,{alwaysNormalize:!0});this._autoLevelCapping=-1,this._maxHdcpLevel=null,Xt.log("loadSource:"+s),e&&i&&(i!==s||this.bufferController.hasSourceTypes())&&(this.detachMedia(),this.attachMedia(e)),this.trigger(Gt.MANIFEST_LOADING,{url:t})}startLoad(t=-1){Xt.log(`startLoad(${t})`),this.started=!0,this.networkControllers.forEach(e=>{e.startLoad(t)})}stopLoad(){Xt.log("stopLoad"),this.started=!1,this.networkControllers.forEach(t=>{t.stopLoad()})}resumeBuffering(){this.started&&this.networkControllers.forEach(t=>{"fragmentLoader"in t&&t.startLoad(-1)})}pauseBuffering(){this.networkControllers.forEach(t=>{"fragmentLoader"in t&&t.stopLoad()})}swapAudioCodec(){Xt.log("swapAudioCodec"),this.streamController.swapAudioCodec()}recoverMediaError(){Xt.log("recoverMediaError");const t=this._media;this.detachMedia(),t&&this.attachMedia(t)}removeLevel(t){this.levelController.removeLevel(t)}get levels(){const t=this.levelController.levels;return t||[]}get currentLevel(){return this.streamController.currentLevel}set currentLevel(t){Xt.log("set currentLevel:"+t),this.levelController.manualLevel=t,this.streamController.immediateLevelSwitch()}get nextLevel(){return this.streamController.nextLevel}set nextLevel(t){Xt.log("set nextLevel:"+t),this.levelController.manualLevel=t,this.streamController.nextLevelSwitch()}get loadLevel(){return this.levelController.level}set loadLevel(t){Xt.log("set loadLevel:"+t),this.levelController.manualLevel=t}get nextLoadLevel(){return this.levelController.nextLoadLevel}set nextLoadLevel(t){this.levelController.nextLoadLevel=t}get firstLevel(){return Math.max(this.levelController.firstLevel,this.minAutoLevel)}set firstLevel(t){Xt.log("set firstLevel:"+t),this.levelController.firstLevel=t}get startLevel(){const t=this.levelController.startLevel;return-1===t&&this.abrController.forcedAutoLevel>-1?this.abrController.forcedAutoLevel:t}set startLevel(t){Xt.log("set startLevel:"+t),-1!==t&&(t=Math.max(t,this.minAutoLevel)),this.levelController.startLevel=t}get capLevelToPlayerSize(){return this.config.capLevelToPlayerSize}set capLevelToPlayerSize(t){const e=!!t;e!==this.config.capLevelToPlayerSize&&(e?this.capLevelController.startCapping():(this.capLevelController.stopCapping(),this.autoLevelCapping=-1,this.streamController.nextLevelSwitch()),this.config.capLevelToPlayerSize=e)}get autoLevelCapping(){return this._autoLevelCapping}get bandwidthEstimate(){const{bwEstimator:t}=this.abrController;return t?t.getEstimate():NaN}set bandwidthEstimate(t){this.abrController.resetEstimator(t)}get ttfbEstimate(){const{bwEstimator:t}=this.abrController;return t?t.getEstimateTTFB():NaN}set autoLevelCapping(t){this._autoLevelCapping!==t&&(Xt.log("set autoLevelCapping:"+t),this._autoLevelCapping=t,this.levelController.checkMaxAutoUpdated())}get maxHdcpLevel(){return this._maxHdcpLevel}set maxHdcpLevel(t){_s(t)&&this._maxHdcpLevel!==t&&(this._maxHdcpLevel=t,this.levelController.checkMaxAutoUpdated())}get autoLevelEnabled(){return-1===this.levelController.manualLevel}get manualLevel(){return this.levelController.manualLevel}get minAutoLevel(){const{levels:t,config:{minAutoBitrate:e}}=this;if(!t)return 0;const i=t.length;for(let s=0;s<i;s++)if(t[s].maxBitrate>=e)return s;return 0}get maxAutoLevel(){const{levels:t,autoLevelCapping:e,maxHdcpLevel:i}=this;let s;if(s=-1===e&&null!=t&&t.length?t.length-1:e,i)for(let r=s;r--;){const e=t[r].attrs["HDCP-LEVEL"];if(e&&e<=i)return r}return s}get firstAutoLevel(){return this.abrController.firstAutoLevel}get nextAutoLevel(){return this.abrController.nextAutoLevel}set nextAutoLevel(t){this.abrController.nextAutoLevel=t}get playingDate(){return this.streamController.currentProgramDateTime}get mainForwardBufferInfo(){return this.streamController.getMainFwdBufferInfo()}setAudioOption(t){var e;return null==(e=this.audioTrackController)?void 0:e.setAudioOption(t)}setSubtitleOption(t){var e;return null==(e=this.subtitleTrackController)||e.setSubtitleOption(t),null}get allAudioTracks(){const t=this.audioTrackController;return t?t.allAudioTracks:[]}get audioTracks(){const t=this.audioTrackController;return t?t.audioTracks:[]}get audioTrack(){const t=this.audioTrackController;return t?t.audioTrack:-1}set audioTrack(t){const e=this.audioTrackController;e&&(e.audioTrack=t)}get allSubtitleTracks(){const t=this.subtitleTrackController;return t?t.allSubtitleTracks:[]}get subtitleTracks(){const t=this.subtitleTrackController;return t?t.subtitleTracks:[]}get subtitleTrack(){const t=this.subtitleTrackController;return t?t.subtitleTrack:-1}get media(){return this._media}set subtitleTrack(t){const e=this.subtitleTrackController;e&&(e.subtitleTrack=t)}get subtitleDisplay(){const t=this.subtitleTrackController;return!!t&&t.subtitleDisplay}set subtitleDisplay(t){const e=this.subtitleTrackController;e&&(e.subtitleDisplay=t)}get lowLatencyMode(){return this.config.lowLatencyMode}set lowLatencyMode(t){this.config.lowLatencyMode=t}get liveSyncPosition(){return this.latencyController.liveSyncPosition}get latency(){return this.latencyController.latency}get maxLatency(){return this.latencyController.maxLatency}get targetLatency(){return this.latencyController.targetLatency}get drift(){return this.latencyController.drift}get forceStartLoad(){return this.streamController.forceStartLoad}}Jh.defaultConfig=void 0;var Zh=i("c26f"),tc=i("5902"),ec={name:"ugc",mixins:[Lt["a"],Rt["a"],Dt["a"]],components:{classifyDialog:tc["a"]},data:function(){return{radio:1,dialogVisible:!1,dialogDetail:!1,detailData:{},dialogAuditAll:!1,title:"",createdName:"",state:"",time:"",tableData:[],currentPage:1,pageSize:20,total:0,selectPower:"",lineText:"",checkList:[],selectvalue:[],RoleList:[],choseData:[],ClassifyData:[],ClassifyValue:[],offLineData:"",shenhe:!1,text:"",textAll:"",radio1:1,randomSettingDialog:!1,minRandomValue:0,maxRandomValue:1e3,btnStatus:!1,filterData:[],exportLoading:!1}},mounted:function(){this.init(),this.getPowerData(),this.getClassifyData()},computed:{processedContent:function(){return this.detailData&&this.detailData.content?Zh["a"].unexcapeHtml(this.detailData.content):""}},watch:{radio:{handler:function(t){1==t&&(this.text="")}}},methods:{openUrl:function(t){t&&window.open(t,"_blank")},openRandomSettingDialog:function(){this.randomSettingDialog=!0,this.minRandomValue=0,this.maxRandomValue=1e3},submitRandomSetting:function(){var t=this;this.minRandomValue>this.maxRandomValue?this.$message.warning("最小随机数不能大于最大随机数"):Object(It["qe"])(this.minRandomValue,this.maxRandomValue).then((function(e){200==e.status?(t.$message.success("设置成功"),t.randomSettingDialog=!1,t.init()):t.$message.error(e.message||"设置失败")})).catch((function(e){t.$message.error("设置失败："+e.message)}))},searchDetail:function(t){var e=this;this.dialogDetail=!0,this.text="",this.shenhe=!1,Object(It["Re"])(t.id).then((function(t){e.detailData=null,e.detailData=t.data,setTimeout((function(){var t=e.$refs.videoPlayer;if(Jh.isSupported()){var i=new Jh;i.loadSource(e.detailData.videoUrl),i.attachMedia(t),i.on(Jh.Events.MANIFEST_PARSED,(function(){t.play()}))}else t.canPlayType("application/vnd.apple.mpegurl")&&(t.src=e.detailData.videoUrl,t.addEventListener("loadedmetadata",(function(){t.play()})))}),1e3)}))},audit:function(t){var e=this;this.radio=1,this.text="",this.dialogDetail=!0,this.shenhe=!0,Object(It["Re"])(t.id).then((function(t){e.detailData=null,e.detailData=t.data,setTimeout((function(){var t=e.$refs.videoPlayer;if(Jh.isSupported()){var i=new Jh;i.loadSource(e.detailData.videoUrl),i.attachMedia(t),i.on(Jh.Events.MANIFEST_PARSED,(function(){t.play()}))}else t.canPlayType("application/vnd.apple.mpegurl")&&(t.src=e.detailData.videoUrl,t.addEventListener("loadedmetadata",(function(){t.play()})))}),1e3)}))},batch_audit:function(){0!=this.choseData.length?(this.textAll="",this.radio1=1,this.dialogAuditAll=!0):kt["c"].warning("请至少选择一条数据！")},setClass:function(){0!=this.choseData.length?(this.$refs.classifyRef.openDialog("ugc"),this.$refs.classifyRef.gitClassList()):kt["c"].warning("请至少选择一条数据！")},set:function(){this.init()},exportUgc:function(){var t=this;this.exportLoading=!0;var e={classifyIds:this.ClassifyValue,title:this.title,createdName:this.createdName,state:this.state,ugcIds:[]};Object(It["Se"])(e).then((function(e){if(t.exportLoading=!1,e.size>57){var i="UGC数据导出_".concat((new Date).toLocaleDateString(),".xlsx"),s=document.createElement("a");s.href=window.URL.createObjectURL(e),s.download=i,s.click(),kt["c"].success("导出成功")}else kt["c"].warning("暂无数据导出")})).catch((function(e){t.exportLoading=!1,kt["c"].error("导出失败："+(e.message||"未知错误"))}))},init:function(){var t=this;this.tableData=[];var e={classifyIds:this.ClassifyValue,pageIndex:this.currentPage,pageSize:this.pageSize,title:this.title,createdName:this.createdName,state:this.state,ugcIds:[]};Object(It["Pe"])(e).then((function(e){200==e.status&&(t.total=e.totalSize,t.tableData=e.data,t.filterData=t.tableData,t.btnStatus=!1,t.initScroll())}))},getPowerData:function(){var t=this,e={title:""};Object(It["Od"])(e).then((function(e){200==e.status&&(t.RoleList=t.getList(e.data))}))},getClassifyData:function(){var t=this,e={tagIds:[],tagType:"ugc"};Object(It["md"])(e).then((function(e){t.ClassifyData=t.removeClassifyChild(e.data)}))},getList:function(t){var e=this;return t.filter((function(t){return 0===t.childRoles.length?(delete t.childRoles,t):e.getList(t.childRoles)}))},removeClassifyChild:function(t){var e=this;return t.filter((function(t){return 0===t.childTags.length?(delete t.childTags,t):e.removeClassifyChild(t.childTags)}))},handleUp:function(t,e){e.onlineState?this.lineText="下线提示":this.lineText="上线提示",this.dialogVisible=!0,this.currentChose=e},lineSave:function(){var t=this,e=[];e.push(this.currentChose.id);var i={operationType:"上线提示"==this.lineText?1:0,moduleType:"ugc",moduleIds:e};Object(It["O"])(i).then((function(e){200==e.status&&("上线提示"===t.lineText?kt["c"].success("上线成功"):kt["c"].success("下线成功"),t.init(),t.dialogVisible=!1)}))},examine:function(){var t=this,e={operationType:this.radio,reason:this.text,ugcIds:[this.detailData.id]};Object(It["Qe"])(e).then((function(){t.dialogDetail=!1,t.init()}))},goToEdit:function(t){this.$router.push({path:"/ugc_edit",query:{id:t.id}})},examineAll:function(){var t=this,e=this.choseData.map((function(t){return t.id})),i={operationType:this.radio1,reason:this.textAll,ugcIds:e};Object(It["Qe"])(i).then((function(){t.dialogAuditAll=!1,t.init()}))},handleSizeChange:function(t){this.pageSize=t,this.currentPage=1,this.init()},handleCurrentChange:function(t){this.currentPage=t,this.init()},reset:function(){this.time="",this.title="",this.createdName="",this.state="",this.selectvalue=[],this.ClassifyValue=[];var t={stopPropagation:function(){}};try{this.$refs.Classify.clearValue(t)}catch(nc){this.$refs.Classify.handleClear(t)}},handleSelectionChange:function(t){this.choseData=t},change:function(){var t=this,e=this.$refs["cascader"].getCheckedNodes();this.selectvalue=[],e.forEach((function(e){return t.selectvalue.push(e.data.id)}))},ClassifyChange:function(){var t=this,e=this.$refs["Classify"].getCheckedNodes();this.ClassifyValue=[],e.forEach((function(e){return t.ClassifyValue.push(e.data.id)}))}}},ic=(i("232c"),i("d959")),sc=i.n(ic);const rc=sc()(ec,[["render",At],["__scopeId","data-v-1eb25e52"]]);e["default"]=rc},f44b:function(t,e,i){}}]);