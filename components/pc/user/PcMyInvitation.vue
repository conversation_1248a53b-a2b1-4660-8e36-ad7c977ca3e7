<template>
  <div class="pc-my-invitation pc-user-container-scroll">
    <div class="header-section">
      <h3>我的邀请</h3>
      <button
        v-if="shouldShowWithdrawBtn"
        class="withdraw-btn inactive"
        @click="handleWithdrawClick"
      >
        提现
      </button>
    </div>
    <ul class="screen-tab">
      <li
        v-for="item in options"
        :key="item.label"
        :class="{ 'active bgColor': item.active }"
        @click="toggle(item)"
      >
        {{ item.label }}
      </li>
    </ul>

    <div class="invitations" v-if="list.length">
      <div
        v-for="(item, index) in list"
        :key="item.id"
        class="invitation-card"
        :class="{ 'card-margin-right': (index + 1) % 2 !== 0 }"
      >
        <div class="card-content" @click="handleCardClick(item)">
          <div class="invitation-avatar">
            <img
              :src="item.avatar || defaultAvatar"
              :alt="item.userName"
              @error="handleImageError"
            />
          </div>
          <div class="invitation-info">
            <div class="user-name">{{ item.userName }}</div>
            <div class="time-info">
              <div class="register-time">注册时间：{{ item.registerTime }}</div>
              <div v-if="item.completeTime" class="complete-time">
                完成时间：{{ item.completeTime }}
              </div>
            </div>
          </div>
          <div class="invitation-status">
            <span
              :class="['status-tag', item.status === 'completed' ? 'status-completed' : 'status-incomplete']"
            >
              {{ item.status === 'completed' ? '已填写' : '未填写' }}
            </span>
          </div>
        </div>
      </div>
    </div>
    <pc-empty-page v-else :description="loading ? '数据加载中' : '暂无邀请记录~'"></pc-empty-page>
  </div>
</template>

<script>
export default {
  name: "PcMyInvitation",
  data() {
    return {
      options: [
        { label: "全部", value: "", active: true },
        { label: "已填写", value: "completed", active: false },
        { label: "未填写", value: "incomplete", active: false },
      ],
      params: {
        status: "",
      },
      list: [],
      loading: false,
      defaultAvatar: 'https://static.medsci.cn/public-image/ms-image/90246d70-bed5-11eb-b319-cd51f388f4e7_user.png',
      _loadingPromise: null,
      _initialized: false
    };
  },
  computed: {
    // 判断是否显示提现按钮
    shouldShowWithdrawBtn() {
      const projectId = this.$store.state.projectData?.projectId;
      const accountType = this.$store.state.user?.userinfo?.accountType;
      return projectId === 246 && accountType === 0;
    }
  },
  created() {
    this.getList();
  },
  beforeDestroy() {
    // 清理Promise引用
    this._loadingPromise = null;
  },
  methods: {
    toggle(row) {
      this.options.forEach((t) => {
        if (t.label === row.label) {
          t.active = true;
        } else {
          t.active = false;
        }
      });
      this.params.status = row.value;
      // 先清空数据
      this.list = [];
      // 重置初始化状态，强制重新加载
      this._initialized = false;
      this.getList();
    },
    async getList() {
      if (this.loading) return;

      // 防止重复调用
      if (this._loadingPromise) {
        return this._loadingPromise;
      }

      // 如果已经初始化，则不重复加载
      if (this._initialized && this.list.length > 0) {
        return Promise.resolve();
      }

      this.loading = true;

      // 创建加载Promise
      this._loadingPromise = (async () => {
        try {
        const params = {
          pageIndex: 1,
          pageSize: 50
        };

        // 根据状态设置hasSurvey参数
        if (this.params.status === 'completed') {
          params.hasSurvey = true;
        } else if (this.params.status === 'incomplete') {
          params.hasSurvey = false;
        }
        // 全部时不传hasSurvey参数

        const res = await this.$api.personalCenter.getInvitationList(params);

        if (res.status === 200 && res.data) {
          this.list = (res.data || []).map(item => ({
            id: item.id || Math.random(),
            userName: item.userName || '未知用户',
            avatar: item.avatar || this.defaultAvatar,
            registerTime: item.createdTime || '',
            completeTime: item.firstSurveyTime || '',
            status: (item.encryptionSurveyIds && Array.isArray(item.encryptionSurveyIds) && item.encryptionSurveyIds.length > 0) ? 'completed' : 'incomplete',
            surveyIds: item.surveyIds || [],
            encryptionSurveyIds: item.encryptionSurveyIds || [],
            encryptionUserId: item.encryptionUserId || ''
          }));
          this._initialized = true;
        }
        } catch (error) {
          console.error('获取邀请列表失败:', error);
          this.$message.error('获取邀请列表失败，请重试');
        } finally {
          this.loading = false;
          this._loadingPromise = null;
        }
      })();

      return this._loadingPromise;
    },

    handleImageError(event) {
      event.target.src = this.defaultAvatar;
    },

    // 处理卡片点击事件
    handleCardClick(item) {
      // 检查 encryptionSurveyIds 数组长度
      if (item.encryptionSurveyIds && Array.isArray(item.encryptionSurveyIds)) {
        if (item.encryptionSurveyIds.length === 1) {
          // 如果数组长度为1，直接跳转到调研页面，带上参数
          const query = {
            type: 'preview',
            id: item.encryptionSurveyIds[0],
            userId: item.encryptionUserId,
            userName: item.userName,
            account: 'apoc'
          };
          this.$router.push({
            path: `/templatePage/${item.encryptionSurveyIds[0]}`,
            query: query
          });
        } else if (item.encryptionSurveyIds.length > 1) {
          // 如果数组长度大于1，跳转到用户列表页面
          this.$router.push(`/personal-center/my-invitation-users?userId=${item.encryptionUserId}`);
        }
      }
    },

    // 处理提现按钮点击事件
    handleWithdrawClick() {
      this.$message({
        message: '1）待调研项目完成后（计划收集至11.15），"提现"按钮启动<br/>2）点击"提现"按钮完善账号信息、签署电子合同。根据"已完成调研"数量给予相应的激励',
        type: 'info',
        duration: 5000,
        showClose: true,
        dangerouslyUseHTMLString: true
      });
    }
  },
};
</script>

<style lang="scss" scoped>
.pc-my-invitation {
  padding: 24px 40px;
  overflow: auto;
  box-sizing: border-box;

  .header-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0;
  }

  h3 {
    font-weight: 600;
    line-height: 37px;
    font-size: 26px;
    padding-bottom: 16px;
    margin: 0;
  }

  .withdraw-btn {
    padding: 8px 20px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;

    &.inactive {
      background-color: #2f92ee;
      color: #fff;
      opacity: 0.6;
    }
  }
  
  .screen-tab {
    display: flex;
    margin-bottom: 32px;
    
    li {
      width: auto;
      padding: 0px 15px;
      height: 30px;
      border-radius: 15px;
      font-size: 14px;
      line-height: 30px;
      text-align: center;
      margin-right: 20px;
      box-sizing: border-box;
      cursor: pointer;
      transition: all 0.3s;
      
      &:hover {
        background-color: var(--button-color-theme);
        color: #fff;
      }
    }
    
    .active {
      color: #fff;
    }
  }
  
  .invitations {
    display: flex;
    flex-wrap: wrap;
    gap: 0;
  }

  .invitation-card {
    width: calc(50% - 12px);
    margin-bottom: 24px;
    cursor: pointer;

    &.card-margin-right {
      margin-right: 24px;
    }

    .card-content {
      background: #fff;
      border-radius: 12px;
      padding: 20px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      height: 100%;
      cursor: pointer;

      &:hover {
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
        transform: translateY(-2px);
      }
    }

    .invitation-avatar {
      margin-right: 16px;
      flex-shrink: 0;

      img {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        object-fit: cover;
      }
    }

    .invitation-info {
      flex: 1;
      min-width: 0;

      .user-name {
        font-size: 16px;
        font-weight: 500;
        color: #333;
        margin-bottom: 8px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .time-info {
        .register-time,
        .complete-time {
          font-size: 14px;
          color: #666;
          margin-bottom: 4px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }

    .invitation-status {
      flex-shrink: 0;
      margin-left: 12px;

      .status-tag {
        padding: 4px 12px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 500;
        border: 1px solid transparent;

        &.status-completed {
          background-color: #f6ffed;
          color: #52c41a;
          border-color: #b7eb8f;
        }

        &.status-incomplete {
          background-color: #fff7e6;
          color: #fa8c16;
          border-color: #ffd591;
        }
      }
    }
  }
}

.bgColor {
  background-color: var(--button-color-theme);
}
</style>
