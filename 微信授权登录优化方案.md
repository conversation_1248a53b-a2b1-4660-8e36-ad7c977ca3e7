# 微信授权登录优化方案

## 问题描述

目前在 `/login` 页面触发微信授权登录逻辑时，会从登录页拉起授权跳转页面重定向回来，但是重定向还是回到了登录页闪了一下才继续进行跳转，希望可以优化为重定向之后不闪登录页。

## 问题分析

### 原有流程
1. 用户访问 `/login` 页面
2. `mounted()` 钩子中调用 `checkWxAuthorization()` 检查是否需要微信授权
3. 如果需要授权且没有 code，会调用 `wxAuthorization()` 跳转到微信授权页面
4. 微信授权完成后重定向回 `/login` 页面（带有 code 参数）
5. **页面重新加载，先显示登录页面UI，然后 `mounted()` 钩子执行**
6. 调用 `wxSignLogin()` 处理授权结果
7. 如果登录成功，再跳转到目标页面

### 问题根因
**微信授权重定向回来后，会先渲染并显示登录页面，然后才执行 `wxSignLogin()` 进行登录跳转，这就造成了"闪现登录页"的问题**。

## 优化方案

### 1. 添加加载状态控制页面显示

在页面模板中添加加载状态，当检测到微信授权 code 时显示加载界面而不是登录表单：

```vue
<template>
  <div>
    <!-- 微信授权登录处理中的加载状态 -->
    <div v-if="isProcessingWxLogin" class="wx-login-loading">
      <div class="loading-content">
        <div class="loading-spinner"></div>
        <p>正在处理微信登录...</p>
      </div>
    </div>
    
    <div v-show="!!size('m') && !isProcessingWxLogin" class="container">
      <!-- 原有登录表单内容 -->
    </div>
  </div>
</template>
```

### 2. 优化 wxSignLogin 方法

修改 `wxSignLogin` 方法返回布尔值，表示是否处理了微信登录：

```javascript
async wxSignLogin(url) {
  // ... 原有逻辑
  if (this.code && !this.globalIsLogin()) {
    try {
      const res = await this.$api.user.getWxLogin(this.code, this.state);
      if (res.status === 200) {
        if (Object.keys(res.data).length === 1) {
          // 绑定手机号授权
          this.$store.dispatch("user/SET_AUTH", res.data.sign);
          this.$router.push("/Wxauthorization");
          return true; // 表示已处理微信登录
        } else {
          // 用户已授权，登录操作
          // ... 设置用户信息和跳转逻辑
          return true; // 表示已处理微信登录
        }
      }
    } catch (error) {
      console.error('微信登录处理失败:', error);
      return false; // 异常情况，未处理
    }
  }
  return false; // 没有微信授权code或已登录，未处理
}
```

### 3. 优化页面生命周期

在 `beforeMount` 和 `mounted` 钩子中优化处理逻辑：

```javascript
beforeMount() {
  // 在组件挂载前检查是否有微信授权code
  if (process.client) {
    const urlParams = new URLSearchParams(window.location.search);
    const hasWxCode = urlParams.get('code') && !this.globalIsLogin();
    
    if (hasWxCode) {
      this.isProcessingWxLogin = true; // 显示加载状态
    }
  }
},

async mounted() {
  await this.getWxConfig();
  
  // 优化：先处理微信授权登录，避免闪现登录页
  const hasHandledWxLogin = await this.wxSignLogin();
  
  // 如果处理了微信登录，不需要显示登录页面
  if (hasHandledWxLogin) {
    return;
  }
  
  // 隐藏加载状态，显示登录页面
  this.isProcessingWxLogin = false;
  
  // 页面加载时执行微信授权判断逻辑
  this.checkWxAuthorization();
  
  // 加载验证码
  this.loadTencentCaptcha();
}
```

### 4. 添加加载状态样式

```scss
.wx-login-loading {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  
  .loading-content {
    text-align: center;
    
    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 3px solid #f3f3f3;
      border-top: 3px solid #2f92ee;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto 16px;
    }
    
    p {
      color: #666;
      font-size: 14px;
      margin: 0;
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
```

## 优化效果

### 优化前
1. 微信授权重定向回 `/login` 页面
2. **显示登录表单（闪现）**
3. 执行 `wxSignLogin()` 处理登录
4. 跳转到目标页面

### 优化后
1. 微信授权重定向回 `/login` 页面
2. **显示"正在处理微信登录..."加载界面**
3. 执行 `wxSignLogin()` 处理登录
4. 直接跳转到目标页面（无闪现）

## 关键改进点

1. **提前检测**：在 `beforeMount` 中提前检测是否有微信授权 code
2. **状态控制**：使用 `isProcessingWxLogin` 状态控制页面显示
3. **优先处理**：在 `mounted` 中优先处理微信登录逻辑
4. **用户体验**：显示友好的加载提示而不是空白或登录表单
5. **错误处理**：增加异常处理，确保在登录失败时能正常显示登录页面

## 测试建议

1. 测试正常微信授权登录流程
2. 测试微信授权失败的情况
3. 测试非微信环境下的正常登录流程
4. 测试已登录用户访问登录页的情况
5. 测试网络异常情况下的处理

这个优化方案通过状态控制和生命周期优化，有效解决了微信授权重定向后闪现登录页的问题，提升了用户体验。
