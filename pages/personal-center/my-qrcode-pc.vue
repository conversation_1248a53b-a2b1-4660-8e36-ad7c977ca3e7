<template>
  <div class="container">
    <div class="content">
      <div class="avator">
        <img src="@/assets/images/user.png" alt="" />
        <div>
          <div>{{$store.state.user.userinfo.realName}}</div>
          <div v-if="!$store.state.user.userinfo.accountType">{{$store.state.user.userinfo.professionalName}}</div>
        </div>
      </div>
      <div class="qrcode" v-if="url">
        <img :src="url" alt="">
      </div>
      <div class="title">扫一扫上面的二维码，跟我建立关系</div>
      <div class="btn" @click="downImg(url)" v-if="!isWeixin">保存二维码</div>
    </div>
  </div>
</template>

<script>
import qrcode from "qrcode";
export default {
  layout: "nav",
  data() {
    return {
      url: "",
      options : {
  errorCorrectionLevel: 'H', // 错误纠正级别（可选）
  type: 'image/png', // 生成的二维码类型（可选）
  quality: 0.92, // 图像质量（可选）
  margin: 3, // 边距（可选）
  scale: 4 // 缩放比例（可选）
},
    };
  },
    computed: {
    isWeixin() {
      return this.$store.state.isWeixin;
    },
  },
  mounted() {
    let apiName = 'userQRCodeInfo'
    if(this.$store.state.projectData.projectId == 246) {
      apiName = 'userMpQRCodeInfo'
    }
    this.$api.special[apiName]().then((res) => {
      
      qrcode.toDataURL(res.data.trim(), this.options, (err, url) => {
        if (err) {
          console.error(err);
          return;
        }
         this.url =url
      });
    });
  },
  methods: {
       //下载图片 filename是图片名称
    downImg(url) {
      // 创建a标签 并设置其相关属性，最后触发其点击事件
      let a = document.createElement("a");
      let clickEvent = document.createEvent("MouseEvents");
      a.setAttribute("href", url);
      a.setAttribute("download", "filename");
      a.setAttribute("target", "_blank");
      clickEvent.initEvent("click", true, true);
      a.dispatchEvent(clickEvent);
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  background: #fff;
  display: flex;
  justify-content: center;
  height: 100vh;
  .content {
    margin-top: 0.21rem;
    background: #fff;
    padding: 1.2rem 1.01rem 0.69rem;
    position: relative;
  }
  .avator {
    display: flex;
    margin-bottom: 0.41rem;
    img {
      width: 45px;
      height: 45px;
      margin-right: 12px;
    }
    > div {
      div:first-child {
        font-weight: 400;
        font-size: 16px;
        color: #333333;
        letter-spacing: 0;
      }
      div:last-child {
        margin-top: 4.5px;
        font-weight: 400;
        font-size: 13px;
        color: #999999;
        letter-spacing: 0;
      }
    }
  }
  .qrcode {
    margin-bottom: 8px;
    width: 250px;
    height: 250px;
    img {
      width: 250px;
      height: 250px;
    }
  }
  .title {
    font-weight: 400;
    font-size: 13px;
    color: #999999;
    letter-spacing: 0;
    text-align: center;
  }
  .btn:hover{
      cursor: pointer;
  }
  .btn {
    position: absolute;
    text-align: center;
    bottom: 138px;
    width: calc(100% - 2.02rem);
    font-weight: 400;
    font-size: 16px;
    color: #2f92ee;
    letter-spacing: 0;
  }
}
</style>