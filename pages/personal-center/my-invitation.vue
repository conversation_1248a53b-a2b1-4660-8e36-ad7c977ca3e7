<template>
  <div>
    <!-- 移动端 -->
    <div class="containers" v-show="size('m')">
      <!-- 移动端头部区域 -->
      <div class="mobile-tabs-header">
        <div class="tabs-wrapper">
          <el-tabs
            v-model="activeTab"
            class="demo-tabs"
            tab-position="top"
            @tab-click="handleClick"
          >
            <el-tab-pane label="全部" name="all">
              <div class="invitation_content" v-loading="loading">
                <InvitationList
                  :list="filteredList"
                  :empty-text="getEmptyText()"
                  :is-loading="loading"
                  @item-click="viewDetail"
                />
              </div>
            </el-tab-pane>
            <el-tab-pane label="已填写" name="completed">
              <div class="invitation_content" v-loading="loading">
                <InvitationList
                  :list="filteredList"
                  :empty-text="getEmptyText()"
                  :is-loading="loading"
                  @item-click="viewDetail"
                />
              </div>
            </el-tab-pane>
            <el-tab-pane label="未填写" name="incomplete">
              <div class="invitation_content" v-loading="loading">
                <InvitationList
                  :list="filteredList"
                  :empty-text="getEmptyText()"
                  :is-loading="loading"
                  @item-click="viewDetail"
                />
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
        <button
          v-if="shouldShowWithdrawBtn"
          class="mobile-withdraw-btn inactive"
          @click="handleWithdrawClick"
        >
          提现
        </button>
      </div>
    </div>

    <!-- PC端 -->
    <div v-show="size('pc')">
      <PcMyInvitation />
    </div>
  </div>
</template>

<script>
import InvitationList from '@/components/invitation/InvitationList.vue';
import PcMyInvitation from '@/components/pc/user/PcMyInvitation.vue';

export default {
  name: 'MyInvitation',
  layout: 'nav',
  components: {
    InvitationList,
    PcMyInvitation
  },
  head() {
    return {
      title: `我的邀请-${this.globalTitle}`,
    };
  },
  data() {
    return {
      activeTab: 'all',
      invitationList: [],
      loading: false,
      defaultAvatar: 'https://static.medsci.cn/public-image/ms-image/90246d70-bed5-11eb-b319-cd51f388f4e7_user.png',
      _loadingPromise: null,
      _initialized: false
    };
  },
  computed: {
    filteredList() {
      if (this.activeTab === 'all') {
        return this.invitationList;
      } else if (this.activeTab === 'completed') {
        return this.invitationList.filter(item => item.status === 'completed');
      } else if (this.activeTab === 'incomplete') {
        return this.invitationList.filter(item => item.status === 'incomplete');
      }
      return this.invitationList;
    },
    // 判断是否显示提现按钮
    shouldShowWithdrawBtn() {
      const projectId = this.$store.state.projectData?.projectId;
      const accountType = this.$store.state.user?.userinfo?.accountType;
      return projectId === 246 && accountType === 0;
    }
  },
  async mounted() {
    // 只在移动端加载数据，PC端由子组件自己控制
    if (this.size('m')) {
      await this.loadInvitationList();
    }
  },
  beforeDestroy() {
    // 清理Promise引用
    this._loadingPromise = null;
  },
  methods: {
    // 处理tab点击事件
    handleClick(val) {
      this.activeTab = val.name;
      // 先清空数据
      this.invitationList = [];
      // 重置初始化状态，强制重新加载
      this._initialized = false;
      this.loadInvitationList();
    },

    // 加载邀请列表
    async loadInvitationList() {
      if (this.loading) return;

      // 防止重复调用
      if (this._loadingPromise) {
        return this._loadingPromise;
      }

      // 如果已经初始化，则不重复加载
      if (this._initialized && this.invitationList.length > 0) {
        return Promise.resolve();
      }

      this.loading = true;

      // 创建加载Promise
      this._loadingPromise = (async () => {
        try {
        const params = {
          pageIndex: 1,
          pageSize: 50
        };

        // 根据tab设置hasSurvey参数
        if (this.activeTab === 'completed') {
          params.hasSurvey = true;
        } else if (this.activeTab === 'incomplete') {
          params.hasSurvey = false;
        }
        // 全部时不传hasSurvey参数

        const res = await this.$api.personalCenter.getInvitationList(params);

        if (res.status === 200 && res.data) {
          const newList = (res.data || []).map(item => ({
            id: item.id || Math.random(),
            userName: item.userName || '未知用户',
            avatar: item.avatar || this.defaultAvatar,
            registerTime: item.createdTime || '',
            completeTime: item.firstSurveyTime || '',
            status: (item.encryptionSurveyIds && Array.isArray(item.encryptionSurveyIds) && item.encryptionSurveyIds.length > 0) ? 'completed' : 'incomplete',
            surveyIds: item.surveyIds || [],
            encryptionSurveyIds: item.encryptionSurveyIds || [],
            encryptionUserId: item.encryptionUserId || ''
          }));

          this.invitationList = newList;
          this._initialized = true;
        }
        } catch (error) {
          console.error('加载邀请列表失败:', error);
          this.$toast('加载失败，请重试');
        } finally {
          this.loading = false;
          this._loadingPromise = null;
        }
      })();

      return this._loadingPromise;
    },

    // 查看详情
    viewDetail(item) {
      // 检查 encryptionSurveyIds 数组长度
      if (item.encryptionSurveyIds && Array.isArray(item.encryptionSurveyIds)) {
        if (item.encryptionSurveyIds.length === 1) {
          // 如果数组长度为1，直接跳转到调研页面，带上参数
          const query = {
            type: 'preview',
            id: item.encryptionSurveyIds[0],
            userId: item.encryptionUserId,
            userName: item.userName,
            account: 'apoc'
          };
          this.$router.push({
            path: `/templatePage/${item.encryptionSurveyIds[0]}`,
            query: query
          });
        } else if (item.encryptionSurveyIds.length > 1) {
          // 如果数组长度大于1，跳转到用户列表页面
          this.$router.push(`/personal-center/my-invitation-users?userId=${item.encryptionUserId}`);
        }
      }
    },

    // 处理提现按钮点击事件
    handleWithdrawClick() {
      this.$toast({
        message: '1）待调研项目完成后（计划收集至11.15），"提现"按钮启动\n2）点击"提现"按钮完善账号信息、签署电子合同。根据"已完成调研"数量给予相应的激励',
        duration: 5000
      });
    },

    // 获取空状态文本
    getEmptyText() {
      // 如果正在加载，显示加载中文本
      if (this.loading) {
        return '数据加载中';
      }

      const textMap = {
        'all': '暂无邀请数据了',
        'completed': '暂无已填写的邀请',
        'incomplete': '暂无未填写的邀请'
      };
      return textMap[this.activeTab] || '暂无数据';
    }
  }
};
</script>

<style lang="scss" scoped>
@import "~@/assets/css/sty";

.containers {
  min-height: 100vh;
  background: #fff;

  .mobile-tabs-header {
    position: relative;
    background: #fff;
    border-bottom: 1px solid #f0f0f0;

    .tabs-wrapper {
      flex: 1;
    }

    .mobile-withdraw-btn {
      position: absolute;
      top: 0.25rem;
      right: 0.3rem;
      padding: 0.12rem 0.3rem;
      border-radius: 0.08rem;
      font-size: 0.26rem;
      font-weight: 500;
      border: none;
      cursor: pointer;
      transition: all 0.3s ease;
      z-index: 200;

      &.inactive {
        background-color: #2f92ee;
        color: #fff;
        opacity: 0.6;
      }
    }
  }

  ::v-deep {
    .van-list__placeholder {
      padding-bottom: 1.35rem;
    }
    .el-tabs__item {
      padding: 0 0.15rem;
      font-size: 0.3rem;
    }
    .el-tabs__nav-wrap::after {
      background-color: #fff;
    }
    .el-tabs__nav-scroll {
      position: relative;
      left: 0;
      width: calc(100% - 1.5rem); /* 为右侧按钮留出空间 */
      background: #fff;
      z-index: 100;
      padding-left: 0.3rem;
      padding-top: 0.15rem;
    }
    .el-tabs__item.is-active {
      color: var(--button-color-theme);
    }
    .el-tabs__active-bar {
      background: var(--button-color-theme);
    }
  }
  .invitation_content {
    margin: 0.2rem 0 0 0;
    min-height: calc(100vh - 1.6rem);
  }
}
</style>
