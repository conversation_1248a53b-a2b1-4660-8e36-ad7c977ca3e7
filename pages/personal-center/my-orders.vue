<template>
  <!--兑换订单-->
  <div class="container">
    <van-list
      v-model="loading"
      :finished="finished"
      finished-text="没有更多了"
      @load="init"
      v-if="initShow == 2"
    >
      <div>
        <ul>
          <li v-for="item in list" @click="toDetail(item)">
            <p class="handleSty">
              兑换时间： {{ item.createdTime }}
              <span
                class="statusSty"
                :style="{ color: item.status == '1' ?  '#388CFF' : '#FF7E7F' }"
                >{{ item.status == 0 ? "待发货" : "已发货" }}</span
              >
            </p>
            <div class="showSty">
              <div>
                <h2 class="van-multi-ellipsis--l2 showSty_top">
                  {{ item.goodsTitle }}
                </h2>
                <p class="showSty_bottom">
                  <span class="red">-{{ item.goodsPaymentAmount }}</span>
                  <span class="unit">梅花</span>
                  <span class="unit">x1</span>
                </p>
              </div>
            </div>
          </li>
        </ul>
      </div>
    </van-list>

    <empty-page  v-if="initShow == 1" :description="'暂无兑换订单信息'"></empty-page>
  </div>
</template>

<script>
import WhitePage from "../../components/lesson/WhitePage";
export default {
  name: "exchange-order",
  components: { WhitePage },
  head() {
    return {
      title: `兑换记录-${this.globalTitle}`,
    };
  },
  data() {
    return {
      pageSize: 10,
      pageIndex: 1,
      list: [],
      loading: false,
      finished: false,
      initShow:0
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    init() {
      let params = {
        pageSize: this.pageSize,
        pageIndex: this.pageIndex,
      };
      this.$api.store.goodsOrderList(params).then((res) => {
        if (res.status == 200) {
          this.list = this.list.concat(res.data);
          if(this.list.length==0){
            this.initShow = 1
          }else{
            this.initShow = 2
          }
          this.loading = false;
          this.pageIndex++;
          if (this.list.length === res.totalSize) {
            this.finished = true;
          }
        } else {
          this.loading = false;
          this.finished = false;
          if(this.list.length==0){
            this.initShow = 1
          }else{
            this.initShow = 2
          }
        }
        
      });
    },
    toDetail(row){
      this.$router.push(`/store/orderDetail?orderId=${row.id}`)
    }
  },
};
</script>

<style scoped lang="scss">
.container {
  height: calc(100vh - .88rem);
  background: #f7f7f7;
}

ul li {
  background: #fff;
  margin: 0.2rem 0.28rem;
  border-radius: 0.12rem;
  padding: 0.26rem 0.31rem;
}

.statusSty {
  height: 0.32rem;
  float: right;
}

.handleSty {
  font-size: 0.24rem;
  font-weight: 400;
  text-align: left;
  color: #999999;
  line-height: 0.33rem;
}

.handleSty span {
  font-weight: 500;
  color: #388cff;
}

.imgSty {
  width: 1.44rem;
  height: 1.43rem;
  border-radius: 0.12rem;
  margin-right: 0.2rem;
}
.showSty {
  display: flex;
  margin-top: 0.1rem;
}
.showSty_bottom {
  margin-top: 0.16rem;
}
.showSty_top {
  color: #333;
  font-size: 0.3rem;
}

.showSty div {
  text-align: justify;
}

.rightSect {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.red {
  font-size: 0.34rem;
  font-weight: 500;
  color: #fe6263;
  margin-right: 0.10rem;
}

.unit {
  font-size: 0.24rem;
  font-weight: 500;
  color: #999999;
  margin-right: 0.56rem;
}

h2 {
  font-size: 0.32rem;
  font-weight: 500;
  color: #333333;
  line-height: 0.45rem;
}
</style>
