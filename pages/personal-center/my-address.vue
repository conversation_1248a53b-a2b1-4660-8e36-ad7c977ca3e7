<template>
  <div class="yxd_address">
    <div v-if="list && list.length > 0" class="address_list">
      <div class="item" v-for="(item, index) in list" :key="index" >
        <div class="item_top" @click="checkOne(item)">{{item.receiverName}} {{item.mobile}}</div>
        <div class="item_center" @click="checkOne(item)">
          {{item.provinceName}}{{item.cityName}}{{item.districtName}}{{item.expressAddress}}
        </div>
        <div class="item_bottom">
          <van-checkbox v-model="item.isDefault" @click.stop="changcheck(item)" v-if="!item.isDefault" icon-size="24px"
            ><span>设为默认地址</span></van-checkbox
          >
          <van-checkbox v-model="item.isDefault" @click.stop="changcheck(item)" v-else icon-size="24px"
            ><span class="checkactive">默认地址</span></van-checkbox
          >
          <div class="b_btn">
            <span @click.stop="deleteOne(item)">删除</span>
            <span @click.stop="edit(item)">修改</span>
          </div>
        </div>
      </div>
    </div>
    <pc-empty-page
      v-else
      description="-暂无地址-"
      paddingTop="100px"
    ></pc-empty-page>
    <div class="btn" @click="toAdd">
      <van-button type="info" size="large">新增地址</van-button>
    </div>
  </div>
</template>

<script>
import { Dialog } from 'vant';
import Vue from 'vue';
export const EventBus = new Vue();
export default {
  head() {
    return {
      title: `我的地址-${this.globalTitle}`,
    };
  },
  layout: "nav", // 使用 default.vue 布局
  data() {
    return {
      list: [],
      checked: false,
    };
  },
  mounted(){
      this.init()
  },
  methods: {
    checkOne(item){
      if(this.$route.query.check){
        this.$router.go(-1)
        this.$store.dispatch("SetAddressChoose",item)
      }
    },
    // 跳转到添加地址页面
    toAdd(){
      if(this.$route.query.check){
        this.$router.push("/store/addAddress?check=true")
      }else{
        this.$router.push("/store/addAddress")
      }
    },
      deleteOne(item){
          Dialog.confirm({
            title: '确认删除该地址',
            message: '',
            })
            .then(() => {
                let params = {
              id: item.id
          }
          this.$api.store.addressDelete(params).then((res)=>{
           this.init()
       })
            })
            .catch(() => {
                // on cancel
            });
         
      },
    init() {
      this.$api.store.addressList({}).then((res) => {
        this.list = res.data;
      });
    },
    edit(item){
        this.$router.push(`/store/addAddress?id=${item.id}`)
    },
    changcheck(item){
        this.list.forEach(element => {
           if(element.id == item.id){
               element.isDefault = 1
           }else{
               element.isDefault = 0
           }
       });
       let params = {
           id:item.id
       }
       this.$api.store.addressSet(params).then((res)=>{
           this.init()
       })
    }
  },
};
</script>

<style lang="scss" scoped>
.yxd_address {
  position: relative;
    ::v-deep .van-checkbox__icon{
      font-size: .4rem !important;
  }
}
.btn {
  position: fixed;
  bottom: 0;
  width: 100%;
  padding: 0.1rem 0.32rem .91rem;
  background: #F2F4F5;
  ::v-deep .van-button--large {
    height: 0.8rem;
    line-height: 0.8rem;
    border-radius: 0.12rem;
  }
  ::v-deep .van-button__text {
    font-size: 0.3rem;
    color: #ffffff;
    letter-spacing: 0;
    font-weight: 400;
  }
}
.address_list {
  padding: 0.26rem 0.3rem 2rem;
  .item {
    margin: 0 0 0.26rem;
    background: #fff;
    padding: 0.4rem;
    .item_top {
      font-weight: 500;
      font-size: 0.3rem;
      color: #333333;
    }
    .item_center {
      font-weight: 400;
      font-size: 0.28rem;
      color: #666666;
      margin-top: 0.16rem;
    }
    .item_bottom {
      margin-top: 0.35rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .checkactive {
        font-weight: 400;
        font-size: 0.26rem;
        color: #3396f1;
        letter-spacing: 0;
      }
      .b_btn {
        font-size: 0.28rem;
        color: #999999;
        letter-spacing: 0;
        span:first-child {
          margin-right: 0.51rem;
        }
      }
    }
  }
}
</style>