const path = require('path')
import configs from './configs/app'
const webpack = require('webpack');
// 测试环境数据
let devUrl = 'https://medsci-gateway.medon.com.cn'
// let devUrl = 'http://1*********:9997'
// 正式环境数据
// let devUrl = 'https://apigateway.medsci.cn'

export default {
  env: {
    MODE: process.env.NUXT_ENV
  },
  // Global page headers: https://go.nuxtjs.dev/config-head
  head: {
    title: configs.title,
    htmlAttrs: {
      lang: 'en'
    },
    meta: [
      { charset: 'utf-8' },
      {
        name: 'viewport',
        content:
          'width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, viewport-fit=cover'
      },
      { hid: 'description', name: 'description', content: '' },
      { name: 'referrer', content: 'no-referrer' },
      {
        history: true
      }
    ],
    link: [
      // { rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' },
      {
        rel: 'stylesheet',
        href: 'https://static.medsci.cn/public-css/aliplayer/2.9.12/aliplayer-min.css'
      },
      {
        rel: 'stylesheet',
        href: 'https://static.medsci.cn/product/medsci-site/portal/css/main-stylesheet.min.css'
      },
      {
        rel: 'stylesheet',
        href: 'https://img.medsci.cn/web/prod/css/ms-header.min.css'
      },
      {
        rel: 'stylesheet',
        href: 'https://img.medsci.cn/web/prod/css/login.min.css'
      },
      {
        rel: 'stylesheet',
        href: 'https://static.medsci.cn/public-css/quill/1.3.6/quill.snow.css'
      }
    ],
    script: [
      { src: '/rem.js', type: 'text/javascript', charset: 'utf-8' },
      { src: '/growingio.js', type: 'text/javascript', charset: 'utf-8' },
      {
        src: 'https://static.medsci.cn/public-js/AWSC/awsc.js',
        type: 'text/javascript'
      },
      { src: 'https://static.medsci.cn/public-js/aliplayer/2.9.12/aliplayer-min.js' },
      { src: 'https://static.medsci.cn/product/medsci-site/iframe.js' },
      { src: 'https://static.medsci.cn/public-js/weixin-js-sdk/1.6.0/weixin-js-sdk.js' },
      {
        src: 'https://static.medsci.cn/public-js/qrcode/logo/qrcode.min.js'
      },
      {
        src: 'https://static.medsci.cn/product/avue/avue.min.js'
      },
      {
        src: 'https://static.medsci.cn/public-js/ali-oss/6.10.0/aliyun-oss-sdk.min.js'
      },
      {
        src: 'https://static.medsci.cn/public-js/quill/1.3.6/quill.js'
      },
      { src: 'https://static.medsci.cn/public-js/hls/hls.js' },
      { src: 'https://static.medsci.cn/public-js/uni/uni.webview.1.5.3.js', type: 'text/javascript' },
      { src: 'https://static.medsci.cn/public-js/tinymce/@4.9.3/tinymce.min.js', type: 'text/javascript' },
      // { src: 'https://turing.captcha.qcloud.com/TCaptcha.js', type: 'text/javascript' }
      // { src: '/msstatis.js', type: 'text/javascript', charset: 'utf-8'}
    ]
  },
  serverMiddleware: [
    '~/middleware/filterRequestCookies'
  ],

  // Global CSS: https://go.nuxtjs.dev/config-css
  css: ['vant/lib/index.css', 'styles/index.scss', 'assets/css/icon.css'],

  loading: false,

  // Plugins to run before rendering page: https://go.nuxtjs.dev/config-plugins
  plugins: [
    { src: '@/plugins/vant', ssr: true },
    { src: '@/plugins/element', ssr: true },
    { src: '@/plugins/axios-client', mode: 'client',ssr: false },
    { src: '@/plugins/axios-serve', mode: 'server' },
    '@/plugins/request',
    '@/plugins/permission',
    { src: '@/plugins/icons', ssr: true },
    { src: '@/plugins/avue', mode: 'client' },
    { src: '@/plugins/moment' },
    {
      src: '@/plugins/video',
      ssr: false
    },
    '@/plugins/bus',
    '@/plugins/global',
    {src: '~/plugins/disable-console.js', ssr: false},
    { src: '@/plugins/txIm', mode: 'client' },
    { src: '@/plugins/vueDanmuku', mode: 'client' },
    { src: '~/plugins/ms-form', mode: 'client' },
    { src: '~/plugins/ms-editor', mode: 'client' },
    // '@/plugins/vconsole',
    // { src: '@/plugins/WebIM/WebIM', mode: 'client' },
    { src: '@/plugins/dpplayer', mode: 'client' },
    // { src: '@/plugins/localStorage', ssr: false }
    // {
    //   src: '@/plugins/lib-flexible',
    //   ssr: false
    // },
  ],

  // Auto import components: https://go.nuxtjs.dev/config-components
  components: true,

  // Modules for dev and build (recommended): https://go.nuxtjs.dev/config-modules
  buildModules: [],

  // Modules: https://go.nuxtjs.dev/config-modules
  modules: [
    '@nuxtjs/axios',
    'cookie-universal-nuxt',
    '@nuxtjs/proxy',
    '@nuxtjs/style-resources'
  ],

  // Build Configuration: https://go.nuxtjs.dev/config-build
  build: {
    extractCSS: true,
    publicPath:
      process.env.NUXT_ENV == "prod"
        ? "https://static.medsci.cn/product/medsci/apoc/prod/"
        : process.env.NUXT_ENV == "demo"
        ? "https://static.medsci.cn/product/medsci/apoc/test/"
        : "",
    extend(config, ctx) {
      // ...
      const svgRule = config.module.rules.find((rule) => rule.test.test('.svg'))
      svgRule.exclude = [path.resolve(__dirname, 'assets/svg')]
      // Includes /icons/svg for svg-sprite-loader
      config.module.rules.push({
        test: /\.svg$/,
        include: [path.resolve(__dirname, 'assets/svg')],
        loader: 'svg-sprite-loader',
        options: {
          symbolId: 'icon-[name]'
        }
      })
    },
    postcss: {
      // plugins: {
      //   'postcss-pxtorem': {
      //     rootValue({ file }) {
      //       return file.indexOf('vant') !== -1 ? 37.5 : 75;
      //     },
      //     propList: ['*']
      //   }
      // },
      plugins: [
        require('postcss-px-to-viewport')({
          unitToConvert: 'px', // 需要转换的单位，默认为"px"
          viewportWidth: 750, // 视窗的宽度，对应pc设计稿的宽度，一般是1920
          viewportHeight: 1624, // 视窗的高度，对应的是我们设计稿的高度
          unitPrecision: 3, // 单位转换后保留的精度
          propList: [
            // 能转化为vw的属性列表
            '*'
          ],
          viewportUnit: 'vw', // 希望使用的视口单位
          fontViewportUnit: 'vw', // 字体使用的视口单位
          selectorBlackList: [], // 需要忽略的CSS选择器，不会转为视口单位，使用原有的px等单位。
          minPixelValue: 1, // 设置最小的转换数值，如果为1的话，只有大于1的值会被转换
          mediaQuery: false, // 媒体查询里的单位是否需要转换单位
          exclude: [
            /(\/|\\)(node_modules)(\/|\\)/,
            /[/|\\]components[/|\\]common[/|\\]/,
            /[/|\\]components[/|\\]guider[/|\\]/,
            /[/|\\]components[/|\\]home[/|\\]/,
            /[/|\\]components[/|\\]lesson[/|\\]/,
            /[/|\\]components[/|\\]pc[/|\\]/,
            /[/|\\]components[/|\\]imsl[/|\\]/,
            /[/|\\]components[/|\\]live[/|\\]pc[/|\\]/,
            /[/|\\]components[/|\\]information[/|\\]pc[/|\\]/,
            /[/|\\]components[/|\\]person-center[/|\\]/,
            /[/|\\]components[/|\\]personal-privacy[/|\\]/,
            /[/|\\]components[/|\\]authentication[/|\\]/,
            /[/|\\]components[/|\\]points-mall[/|\\]/,
            /[/|\\]components[/|\\]empty-page[/|\\]/,
            /[/|\\]components[/|\\]MsList[/|\\]/,
            /[/|\\]components[/|\\]no-more[/|\\]/,
            /[/|\\]components[/|\\]SvgIcon[/|\\]/,
            /[/|\\]pages[/|\\]/,
            /[/|\\]layouts[/|\\]/,
            /[/|\\]assets[/|\\]/,
            /[/|\\]plugins[/|\\]/,
            /[/|\\]styles[/|\\]/
          ] // 忽略某些文件夹下的文件或特定文件，例如 'node_modules' 下的文件
        })
      ],
      preset: {
        autoprefixer: true
      }
    },
    plugins: [
      new webpack.ProvidePlugin({
        '$': 'jquery'
      })
    ],
    vendor: ['axios']
  },

  axios: {
    proxy: true, // 开启axios跨域
    credentials: false // 表示跨域请求时是否需要使用凭证
    // perfix: '/api'  //baseUrl
  },
  proxy: {
    '/apis/bi': {
      target:
        process.env.NUXT_ENV == 'demo'
          ? 'https://apo-bi.medon.com.cn/dev-api'
          : process.env.NUXT_ENV == 'prod'
          ? 'https://apo-bi.medon.com.cn/dev-api'
          : 'https://apo-bi.medon.com.cn/dev-api',
      changeOrigin: true,
      pathRewrite: {
        '^/apis/bi': '/apo-bi/'
      }
    },
    '/apis/old': {
      target:
        process.env.NUXT_ENV == 'demo'
          ? 'http://medsci-gateway.ms-site.svc.cluster.local:8080'
          : process.env.NUXT_ENV == 'prod'
          ? 'http://medsci-gateway.ms-site:8080'
          : devUrl,
      changeOrigin: true,
      pathRewrite: {
        '^/apis/old': '/web/medsci'
      }
    },
    '/apis/alive': {
      target:
        process.env.NUXT_ENV == 'demo'
          ? 'http://medsci-gateway.ms-site.svc.cluster.local:8080/api/live'
          : process.env.NUXT_ENV == 'prod'
          ? 'http://medsci-gateway.ms-site:8080/api/live'
          : devUrl + '/api/live',
      changeOrigin: true,
      pathRewrite: {
        '^/apis/alive': ''
      }
    },
    '/apis/mg/': {
      target:
        process.env.NUXT_ENV == 'demo'
          ? 'http://medsci-gateway.ms-site.svc.cluster.local:8080'
          : process.env.NUXT_ENV == 'prod'
          ? 'http://medsci-gateway.ms-site:8080'
          : devUrl,
      changeOrigin: true,
      pathRewrite: {
        '^/apis/mg/': '/api/mg/'
      }
    },
    '/apis/eda/': {
      target:
        process.env.NUXT_ENV == 'demo'
          ? 'http://medsci-gateway.ms-site.svc.cluster.local:8080'
          : process.env.NUXT_ENV == 'prod'
          ? 'http://medsci-gateway.ms-site:8080'
          : devUrl,
      changeOrigin: true,
      pathRewrite: {
        '^/apis/eda/': '/api/eda/'
      }
    },
    '/apis/yxd/': {
      target:
        process.env.NUXT_ENV == 'demo'
          ? 'http://medsci-gateway.ms-site.svc.cluster.local:8080'
          : process.env.NUXT_ENV == 'prod'
          ? 'http://medsci-gateway.ms-site:8080'
          : devUrl,
      changeOrigin: true,
      pathRewrite: {
        '^/apis/yxd/': '/api/yxd/'
      }
    },
    '/apis/chatrecord': {
      target:
        process.env.NUXT_ENV == 'demo'
          ? 'https://mid.medon.com.cn'
          : process.env.NUXT_ENV == 'prod'
          ? 'https://mid.medsci.cn'
          : 'https://mid.medon.com.cn',
      changeOrigin: true,
      pathRewrite: {
        '^/apis/chatrecord': ''
      }
    },
    '/apis/im': {
      target:
        process.env.NUXT_ENV == 'demo'
          ? 'https://mid.medon.com.cn'
          : process.env.NUXT_ENV == 'prod'
          ? 'https://mid.medsci.cn'
          : 'https://mid.medon.com.cn',
      changeOrigin: true,
      pathRewrite: {
        '^/apis/im': '/api/im'
      }
    },
    '/apis/sp': {
      target:
        process.env.NUXT_ENV == 'demo'
          ? 'http://medsci-gateway.ms-site.svc.cluster.local:8080'
          : process.env.NUXT_ENV == 'prod'
          ? 'http://medsci-gateway.ms-site:8080'
          : devUrl,
      changeOrigin: true,
      pathRewrite: {
        '^/apis/sp': '/api/pm/open/'
      }
    },
    '/apis/wechat': {
      target:
        process.env.NUXT_ENV == 'demo'
          ? 'https://yika-demo.medon.com.cn'
          : process.env.NUXT_ENV == 'prod'
          ? 'https://yika.medsci.cn'
          : 'https://yika-demo.medon.com.cn',
      changeOrigin: true,
      pathRewrite: {
        '^/apis/wechat': '/wechat'
      }
    },
    // 这个必须放最后，否则会拦截掉其它代理
    '/apis': {
      target:
        process.env.NUXT_ENV == 'demo'
          ? 'http://medsci-gateway.ms-site.svc.cluster.local:8080'
          : process.env.NUXT_ENV == 'prod'
          ? 'http://medsci-gateway.ms-site:8080'
          : devUrl,
      changeOrigin: true,
      pathRewrite: {
        '^/apis': '/api/pm/open/'
      }
    }
  },
  router: {
    middleware: 'initLive'
  },

  globals: {
    title: '医讯达'
  }
}
