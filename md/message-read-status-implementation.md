# 消息已读/未读状态功能实现

## 功能概述
在 `/message` 页面的移动端和PC端，为系统消息列表添加了已读/未读状态显示和交互功能。

## 实现的功能

### 1. 视觉状态显示
- **已读消息**: 文字置灰显示
- **未读消息**: 文字保持黑色，并在头像右上角显示红色小圆点提醒

### 2. 交互功能
- **未读消息**: 鼠标悬停时显示点击手势
- **点击未读消息**: 触发消息状态更新接口，将消息标记为已读
- **已读消息**: 点击不触发任何接口调用

### 3. 状态更新
- 点击未读消息后，消息文字自动置灰
- 红色提醒小圆点自动消失
- 触发全局未读消息计数更新

## 修改的文件

### 1. API接口 (`api/modules/common.js`)
```javascript
// 新增消息状态更新接口
updateMessageReadStatus(id) {
  return axios({
    url: `/individualCenter/update-read/${id}`,
    method: 'put',
    msopt: ['projectId', 'userId']
  })
},

// 新增未读消息计数接口（替代原有getUnread接口）
getUnreadCount() {
  return axios({
    url: '/yxd/web/medsciMessageSystem/unreadCount',
    method: 'get',
    msopt: ['projectId', 'userId']
  })
}
```

### 2. 移动端页面 (`pages/message/index.vue`)
- 添加消息点击事件处理
- 根据 `readStatus` 字段动态应用样式类
- 添加头像容器包装器，在头像右上角显示未读消息红点
- 新增 `handleMessageClick` 方法处理点击事件

### 3. PC端组件 (`components/pc/message/PcMessage.vue`)
- 与移动端相同的功能实现
- 适配PC端的样式尺寸

## 样式实现

### 移动端样式
```scss
// 头像容器
.avatar-wrapper{
  position: absolute;
  left: 0;
  top: 0;
}
.avatar{
  display: block;
}

// 已读消息文本置灰
.read-text {
  color: #999 !important;
}

// 未读消息红点（位于头像右上角）
.unread-dot {
  position: absolute;
  top: -.04rem;
  right: -.04rem;
  width: .12rem;
  height: .12rem;
  background-color: #ff4444;
  border-radius: 50%;
  border: .02rem solid #fff;
}

// 未读消息鼠标悬停效果
.notice-item.unread-message {
  cursor: pointer;
}
```

### PC端样式
```scss
// 头像容器
.avatar-wrapper {
  position: absolute;
  left: 0;
  top: 0;
}
.avatar {
  display: block;
}

// 已读消息文本置灰
.read-text {
  color: #999 !important;
}

// 未读消息红点（位于头像右上角）
.unread-dot {
  position: absolute;
  top: -2px;
  right: -2px;
  width: 8px;
  height: 8px;
  background-color: #ff4444;
  border-radius: 50%;
  border: 1px solid #fff;
}

// 未读消息鼠标悬停效果
.notice-item.unread-message {
  cursor: pointer;
}
```

## 数据字段说明
- `readStatus`: 布尔值，`true` 表示已读，`false` 表示未读
- `id`: 消息的唯一标识符，用于调用更新接口

## 接口说明

### 消息状态更新接口
- **接口地址**: `/individualCenter/update-read/{messageId}`
- **请求方法**: PUT
- **参数**: 消息ID通过URL参数传递
- **功能**: 将指定消息标记为已读状态

### 未读消息计数接口（新增）
- **接口地址**: `/yxd/web/medsciMessageSystem/unreadCount`
- **请求方法**: GET
- **参数**: 无需传递参数（通过msopt自动传递projectId和userId）
- **功能**: 获取当前用户的未读消息总数
- **说明**: 此接口替代了原有的 `getUnread` 接口，无需传递userId参数

## 全局接口替换
项目中以下文件的 `getUnread` 接口调用已全部替换为新的 `getUnreadCount` 接口：
- `layouts/nav.vue`
- `layouts/nav2.vue`
- `layouts/nav3.vue`
- `layouts/navImsl.vue`
- `pages/imsl/navH.vue`
- `pages/examinationDetail/navH.vue`

## 注意事项
1. 只有未读消息（`readStatus: false`）才会触发点击事件
2. 已读消息点击不会调用任何接口
3. 状态更新成功后会触发全局未读消息计数更新
4. 错误处理：如果接口调用失败，会在控制台输出错误信息
5. 新的 `getUnreadCount` 接口无需传递参数，简化了调用方式
