# 项目文档整理说明

## 概述
本文件夹包含了项目的所有技术文档和说明文件，这些文档原本分散在项目根目录中，为了更好的项目结构管理，统一移动到此文件夹中。

## 文件列表

### AI功能相关
- `AI助手滚动优化说明.md` - AI助手页面自动滚动功能优化文档

### 登录和认证相关
- `AUTO_LOGIN_FIX.md` - 自动登录功能修复文档
- `FINAL_AUTO_LOGIN_FIX.md` - 最终自动登录修复方案
- `REDIRECT_LOOP_FIX.md` - 重定向循环问题修复

### 路由和导航相关
- `ROUTE_GUARD_FIXES.md` - 路由守卫修复文档
- `ROUTE_GUARD_FIXES_FINAL.md` - 路由守卫最终修复方案

### 页面和UI相关
- `END_PAGE_LOGIC_OPTIMIZATION.md` - 结束页面逻辑优化
- `MODAL_OVERLAY_FIX.md` - 模态框遮罩层修复
- `WHITE_SCREEN_FIX.md` - 白屏问题修复

### 考试功能相关
- `EXAM_BUTTON_OPTIMIZATION.md` - 考试按钮优化文档
- `EXAM_OPTIMIZATION_TEST_GUIDE.md` - 考试优化测试指南

### API和数据相关
- `INVITATION_USERS_API_OPTIMIZATION.md` - 邀请用户API优化
- `数字输入精度问题修复说明.md` - 数字输入精度问题修复

### SSR相关
- `SSR_COMPATIBILITY_FIX.md` - SSR兼容性修复
- `SSR_STATE_SYNC_FIX.md` - SSR状态同步修复

### 功能实现相关
- `message-read-status-implementation.md` - 消息已读状态实现
- `test-my-release-reason.md` - 测试发布原因
- `test-personal-center-approval-suggestion.md` - 个人中心审批建议测试

## 文件组织原则
1. 所有技术文档统一放在 `md/` 文件夹中
2. 项目根目录只保留 `README.md` 主文档
3. 文档按功能模块分类，便于查找和维护
4. 每个文档都包含详细的实现说明和测试指南

## 使用说明
- 查看具体功能的实现细节，请参考对应的文档文件
- 所有文档都使用Markdown格式，便于阅读和维护
- 如需添加新的技术文档，请放在此文件夹中并更新本说明文件

## 维护记录
- 2024-07-31: 初始整理，将根目录下的所有.md文件（除README.md外）移动到md文件夹
