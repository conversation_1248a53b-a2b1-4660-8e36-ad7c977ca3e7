# My Release 页面 Reason 字段显示功能实现

## 功能需求
在 `/my-release` 页面列表数据中，PC端和移动端都找一个合适的位置，当 `auditState` 为 2 并且有 `reason` 字段时，显示红色字体的审核不通过理由，前面加上"未通过审核："前缀。

## 实现方案

### 1. PC端实现 (components/pc/user/PcMyRelease.vue)

#### 模板修改
```vue
<template #reason v-if="item.auditState === 2 && item.reason">
  <div class="reason-text">未通过审核：{{ item.reason }}</div>
</template>
```

#### 样式添加
```scss
.reason-text {
  color: #ff4444;
  font-size: 12px;
  margin-top: 4px;
  line-height: 16px;
  word-break: break-all;
}
```

### 2. PcBlockC 组件修改 (components/pc/common/PcBlockC.vue)

#### 添加 reason 插槽
```vue
<div class="reason">
  <slot name="reason" />
</div>
```

### 3. 移动端实现 (pages/personal-center/my-release.vue)

#### 模板修改（在三个 tab 中都添加）
```vue
<div v-if="item.auditState === 2 && item.reason" class="reason-text">
  未通过审核：{{ item.reason }}
</div>
```

#### 样式添加
```scss
.reason-text {
  color: #ff4444;
  font-size: 0.24rem;
  margin-top: 0.08rem;
  line-height: 0.32rem;
  word-break: break-all;
  width: 100%;
}
```

## 实现特点

1. **条件显示**: 只有当 `item.auditState === 2` 且 `item.reason` 字段存在且有值时才显示
2. **红色标识**: 使用 `#ff4444` 颜色标识审核不通过理由
3. **响应式设计**: PC端和移动端使用不同的字体大小和间距
4. **文本处理**: 使用 `word-break: break-all` 确保长文本正确换行
5. **位置合适**: 
   - PC端：在 PcBlockC 组件的底部显示
   - 移动端：在每个列表项的底部显示

## 数据结构要求

API 返回的数据结构中需要包含 `reason` 字段：

```javascript
{
  id: "xxx",
  title: "发布标题",
  createdName: "作者名称",
  hits: 100,
  auditState: 2, // 审核状态：1-通过，2-不通过
  reason: "审核不通过的具体理由" // 可选字段，当 auditState 为 2 时显示
}
```

## 测试场景

1. **auditState = 2 且有 reason 字段**: 显示红色的"未通过审核：具体理由"
2. **auditState = 1**: 不显示任何审核理由（即使有 reason 字段）
3. **auditState = 2 但无 reason 字段**: 不显示任何额外内容
4. **reason 为空**: 不显示任何额外内容
5. **长文本 reason**: 正确换行显示，前缀"未通过审核："保持完整

## 兼容性

- ✅ PC端浏览器
- ✅ 移动端浏览器
- ✅ 响应式布局
- ✅ 现有功能不受影响
