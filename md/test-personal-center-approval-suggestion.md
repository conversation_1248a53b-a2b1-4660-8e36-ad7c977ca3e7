# Personal Center 页面修改建议功能实现

## 功能需求
在 `/personal-center` 页面的移动端和PC端最顶部头像上方加上一个判断逻辑，如果返回的用户数据 `perfectInfoStatus` 为 3 且有 `perfectInfoApprovalReason` 的值，则用红色字体显示"修改建议：perfectInfoApprovalReason"。

## 实现方案

### 1. 移动端实现

#### 模板修改
在移动端头像容器上方添加修改建议提示：

```vue
<!-- 移动端修改建议提示 -->
<div v-if="userData.perfectInfoStatus === 3 && userData.perfectInfoApprovalReason" class="mobile-approval-suggestion">
  修改建议：{{ userData.perfectInfoApprovalReason }}
</div>
```

#### 移动端样式
```scss
.mobile-approval-suggestion {
  background-color: #fff2f0;
  border: 1px solid #ffccc7;
  border-radius: 0.12rem;
  padding: 0.24rem;
  margin: 0.24rem;
  color: #ff4444;
  font-size: 0.28rem;
  line-height: 0.4rem;
  word-break: break-all;
}
```

### 2. PC端实现

#### 模板修改
在PC端组件上方添加修改建议提示：

```vue
<!-- PC端修改建议提示 -->
<div v-if="size('pc') && userData.perfectInfoStatus === 3 && userData.perfectInfoApprovalReason" class="pc-approval-suggestion">
  修改建议：{{ userData.perfectInfoApprovalReason }}
</div>
```

#### PC端样式
```scss
.pc-approval-suggestion {
  background-color: #fff2f0;
  border: 1px solid #ffccc7;
  border-radius: 6px;
  padding: 16px 24px;
  margin: 24px 40px;
  color: #ff4444;
  font-size: 14px;
  line-height: 20px;
  word-break: break-all;
}
```

## 实现特点

1. **双重条件判断**: 
   - `perfectInfoStatus === 3` (审核状态为需要修改)
   - `perfectInfoApprovalReason` 存在且有值

2. **前缀文本**: 
   - 显示内容为："修改建议：[具体建议内容]"

3. **红色警告样式**: 
   - 使用 `#ff4444` 红色文字
   - 浅红色背景 `#fff2f0` 和边框 `#ffccc7`
   - 圆角边框设计

4. **响应式设计**: 
   - 移动端：使用 rem 单位，适配移动设备
   - PC端：使用 px 单位，适配桌面设备

5. **位置设计**:
   - 移动端：在头像容器上方，整个页面顶部
   - PC端：在 PcUserInfo 组件上方

## 数据结构要求

API `medsci-yxd-user/get/user` 需要返回包含以下字段的用户数据：

```javascript
{
  perfectInfoStatus: 3, // 1-审核通过，2-审核中，3-需要修改
  perfectInfoApprovalReason: "请完善您的职业信息和工作单位" // 具体的修改建议
}
```

**数据流程**:
1. 调用 `$api.personalCenter.getUserData()` 接口（对应 `medsci-yxd-user/get/user`）
2. 将返回的 `res.data` 保存到组件的 `userData` 中
3. 模板中使用 `userData.perfectInfoStatus` 和 `userData.perfectInfoApprovalReason` 进行判断和显示

## 显示逻辑

- ✅ 当 `perfectInfoStatus === 3` 且 `perfectInfoApprovalReason` 有值时：显示红色的修改建议
- ❌ 当 `perfectInfoStatus !== 3` 时：不显示（即使有 perfectInfoApprovalReason 字段）
- ❌ 当 `perfectInfoStatus === 3` 但 `perfectInfoApprovalReason` 为空时：不显示

## 测试场景

1. **需要修改且有建议**: `perfectInfoStatus = 3` 且有 `perfectInfoApprovalReason` → 显示红色修改建议
2. **审核通过**: `perfectInfoStatus = 1` → 不显示任何提示
3. **审核中**: `perfectInfoStatus = 2` → 不显示任何提示
4. **需要修改但无建议**: `perfectInfoStatus = 3` 但 `perfectInfoApprovalReason` 为空 → 不显示
5. **长文本建议**: 长文本会自动换行显示

## 兼容性

- ✅ 移动端浏览器
- ✅ PC端浏览器  
- ✅ 响应式布局
- ✅ 现有功能不受影响
- ✅ 与项目须知按钮等其他功能共存
