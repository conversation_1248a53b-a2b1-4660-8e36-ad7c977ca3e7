# 数字输入精度问题修复说明

## 问题描述

在 `/privacy-policy` 页面移动端，当自定义字段 `fieldType=2`（数字类型）输入16位数字后，第17位开始无论输入什么数字都会变成0。

## 问题原因

这是由于 JavaScript 数字精度限制导致的。原始代码使用了 `keepDecimal` 方法：

```javascript
keepDecimal(iptNumber, iptPlace) {
  let place = Math.pow(10, iptPlace)
  let res = Math.floor(iptNumber * place) / place
  return res === 0 ? '' : res
}
```

当数字超过 JavaScript 的安全整数范围（`Number.MAX_SAFE_INTEGER = 9007199254740991`，约16位）时，会出现精度丢失。

## 解决方案

使用字符串处理方式替代数字运算，避免精度问题：

```javascript
handleNumberInput(value) {
  if (!value || value === '') return ''
  
  // 转换为字符串处理
  let strValue = String(value)
  
  // 移除非数字和小数点的字符
  strValue = strValue.replace(/[^\d.]/g, '')
  
  // 处理多个小数点的情况，只保留第一个
  const dotIndex = strValue.indexOf('.')
  if (dotIndex !== -1) {
    strValue = strValue.substring(0, dotIndex + 1) + strValue.substring(dotIndex + 1).replace(/\./g, '')
  }
  
  // 如果有小数点，保留最多2位小数
  if (dotIndex !== -1) {
    const parts = strValue.split('.')
    if (parts[1] && parts[1].length > 2) {
      strValue = parts[0] + '.' + parts[1].substring(0, 2)
    }
  }
  
  // 如果输入为空或只有小数点，返回空字符串
  if (strValue === '' || strValue === '.') {
    return ''
  }
  
  return strValue
}
```

## 修复的文件

### 移动端组件
1. `components/personal-privacy/mobile/components/Other.vue`
2. `components/information/mobile/components/Other.vue`

### PC端组件
1. `components/personal-privacy/pc/components/Other.vue`
2. `components/information/pc/components/Other.vue`
3. `components/pc/patient/components/Other.vue`

## 修复内容

### 移动端修复
- 将 `fieldType=2` 的数字输入处理从 `keepDecimal` 方法改为 `handleNumberInput` 方法
- 在 `setInput` 方法中调用新的处理函数
- 在 `numBlur` 事件中也使用新的处理函数

### PC端修复
- 为不同字段类型添加了专门的输入框：
  - `fieldType=1`：字符串类型，限制32字符
  - `fieldType=2`：数字类型，使用 `handleNumberInput` 处理
  - `fieldType=5`：整数类型，使用 `handleIntegerInput` 处理
- 添加了 `handleNumberInput` 和 `handleIntegerInput` 方法

## 特性

1. **精度保持**：使用字符串处理，完全避免 JavaScript 数字精度问题
2. **格式验证**：只允许数字和小数点输入
3. **小数位限制**：自动限制最多2位小数
4. **多小数点处理**：只保留第一个小数点
5. **向后兼容**：保留原始 `keepDecimal` 方法，防止其他地方使用

## 测试

创建了 `test-number-input.html` 文件用于测试修复效果，可以直接在浏览器中打开测试：

1. 测试原始方法的精度问题
2. 验证修复后方法的正确性
3. 对比两种方法的处理结果

## 注意事项

1. 原始的 `keepDecimal` 方法仍然保留，标记为"保留原方法以防其他地方使用，但不再用于fieldType=2的处理"
2. 新方法完全使用字符串处理，不会有精度丢失
3. 所有相关的移动端和PC端组件都已修复
4. 修复后的代码保持了原有的功能逻辑，只是改变了数字处理方式

## 验证方法

1. 在隐私政策页面的自定义数字字段中输入超过16位的数字
2. 验证第17位及以后的数字是否能正确保持
3. 测试小数输入是否正确限制为2位
4. 测试非数字字符是否被正确过滤
